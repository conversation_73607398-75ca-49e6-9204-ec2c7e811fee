﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 年度计划-计划维护
    /// </summary>
    [Route("api/AnnualPlanMain")]
    [ApiController]
    [EnableCors("any")]
    public class AnnualPlanMaintainController : Controller
    {
        private readonly IAnnualPlanMaintainService _annualPlanMainService;
        private readonly IAnnualGenerateService _annualGenerateService;
        private readonly ISessionService _session;

        /// <summary>
        /// 年度计划
        /// </summary>
        /// <param name="mainGoalService"></param>
        /// <param name="session"></param>
        /// <param name="annualGenerateService"></param>
        public AnnualPlanMaintainController(
            ISessionService session
            , IAnnualPlanMaintainService mainGoalService
            , IAnnualGenerateService annualGenerateService)
        {
            _annualPlanMainService = mainGoalService;
            _session = session;
            _annualGenerateService = annualGenerateService;
        }

        #region 查询

        /// <summary>
        /// 查询本人及上下级已制定的年度计划
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBrowseAPViews")]
        public async Task<IActionResult> GetBrowseAPViews(int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetBrowseAPViews(year, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 查询本人部门及上下级部门
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetBrowseAPDepartments")]
        public async Task<IActionResult> GetBrowseAPDepartments(int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetBrowseAPDepartments(year, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取年度计划状态
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanMainIDAndStatus")]
        public async Task<IActionResult> GetAnnualPlanMainIDAndStatus(int departmentID, int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetAnnualPlanMainIDAndStatus(departmentID, year);
            return result.ToJson();
        }

        /// <summary>
        /// 获取附件列表
        /// </summary>
        /// <param name="annualPlanMainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanAttachments")]
        public async Task<IActionResult> GetAnnualPlanAttachments(string annualPlanMainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetAnnualPlanAttachments(annualPlanMainID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取年度计划详情
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlan")]
        public async Task<IActionResult> GetAnnualPlan(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetAnnualPlan(mainID);
            return result.ToJson();
        }

        #endregion

        #region 分类&目标

        /// <summary>
        /// 分类排序（本质仍为目标排序）
        /// </summary>
        /// <param name="resetSortView">排序View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ResetAnnualPlanTypesSort")]
        public async Task<IActionResult> ResetAnnualPlanTypesSort([FromBody] ResetAnnualPlanTypesSortDto resetSortView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.ResetAnnualPlanTypesSort(resetSortView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 目标排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ResetAnnualPlanGoalsSort")]
        public async Task<IActionResult> ResetAnnualPlanGoalsSort([FromBody] AnnualPlanGoalResetSortDto sortView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.ResetAnnualPlanPlanGoalsSort(sortView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取今年的年度计划分类-目标
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetAnnualPlanMainGoalList")]
        public async Task<IActionResult> GetAnnualPlanMainGoalList(int departmentID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _annualPlanMainService.GetAnnualPlanMainGoalList(departmentID, DateTime.Now.Year);
            return result.ToJson();
        }

        #endregion

        #region 分组

        /// <summary>
        /// 获取负责部门候选项
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetDepartmentOptions")]
        public async Task<IActionResult> GetDepartmentOptions(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetDepartmentOptions(mainID);
            return result.ToJson();
        }

        /// <summary>
        /// 分组保存
        /// </summary>
        /// <param name="saveView">分组更新View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveAnnualPlanGroup")]
        public async Task<IActionResult> SaveAnnualPlanGroup([FromBody] SaveGroupView saveView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(saveView, session);
            result.Data = await _annualPlanMainService.SaveAnnualPlanGroup(saveView);
            return result.ToJson();
        }

        /// <summary>
        /// 分组删除
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteAnnualGoalGroup")]
        public async Task<IActionResult> DeleteAnnualGoalGroup(string groupID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.DeleteAnnualGoalGroup(groupID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 分类排序（批量排序目标）
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ResetAnnualPlanGroupsSort")]
        public async Task<IActionResult> ResetAnnualPlanGroupsSort([FromBody] ResetAnnualPlanGroupSortDto sortView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.ResetAnnualPlanGroupsSort(sortView, session.EmployeeID);
            return result.ToJson();
        }

        #endregion

        #region 策略指标

        /// <summary>
        /// 获取指标明细
        /// </summary>
        /// <param name="view">查询view</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetIndicatorDetails")]
        public async Task<IActionResult> GetIndicatorDetails(APDetailsSearchView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetIndicatorDetails(view);
            return result.ToJson();
        }

        /// <summary>
        /// 获取指标字典及当前计划中未引用的指标ID集合
        /// </summary>
        /// <param name="mainID">业务主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRefIndicatorIDs")]
        public async Task<IActionResult> GetRefIndicatorIDs(string mainID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetRefIndicatorIDs(mainID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取某分组下的指标明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetIndicatorDetailsByGroupID")]
        public async Task<IActionResult> GetIndicatorDetailsByGroupID(string mainID, string groupID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetIndicatorDetailsByGroupID(mainID, groupID);
            return result.ToJson();
        }

        /// <summary>
        /// 新增指标明细
        /// </summary>
        /// <param name="addView">新的指标明细</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddIndicatorDetail")]
        public async Task<IActionResult> AddIndicatorDetail([FromBody] SaveIndicatorDetailView addView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(addView, session);
            result.Data = await _annualPlanMainService.AddIndicatorDetail(addView);
            return result.ToJson();
        }

        /// <summary>
        /// 更新策略指标
        /// </summary>
        /// <param name="updateView">更新指标View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateIndicatorDetail")]
        public async Task<IActionResult> UpdateIndicatorDetail([FromBody] SaveIndicatorDetailView updateView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(updateView, session);
            result.Data = await _annualPlanMainService.UpdateIndicatorDetail(updateView);
            return result.ToJson();
        }

        /// <summary>
        /// 策略指标重排序
        /// </summary>
        /// <param name="resortView">重排序View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ResetAnnualPlanIndicatorsSort")]
        public async Task<IActionResult> ResetAnnualPlanIndicatorsSort([FromBody] ResetIndicatorsSortDto resortView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(resortView, session);
            result.Data = await _annualPlanMainService.ResetAnnualPlanIndicatorsSort(resortView);
            return result.ToJson();
        }

        /// <summary>
        /// 删除指标明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="detailID">指标明细ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteIndicatorDetail")]
        public async Task<IActionResult> DeleteIndicatorDetail(string mainID, string detailID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.DeleteIndicatorDetail(mainID, detailID, session.EmployeeID);
            return result.ToJson();
        }

        #endregion

        #region 目标任务

        /// <summary>
        /// 获取目标下的目标任务
        /// </summary>
        /// <param name="view">查询View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetProjectDetails")]
        public async Task<IActionResult> GetProjectDetails(APDetailsSearchView view)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetProjectDetails(view.MainID, view.MainGoalIDs);
            return result.ToJson();
        }

        /// <summary>
        /// 获取目标分组下的目标任务
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetProjectDetailsByGroupID")]
        public async Task<IActionResult> GetProjectDetailsByGroupID(string mainID, string groupID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.GetProjectDetailsByGroupID(mainID, groupID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取上级部门的目标任务，用于建议
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="mainGoalID">目标ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetSuperiorProjectDetail")]
        public async Task<IActionResult> GetSuperiorProjectDetail(int departmentID, string mainGoalID, int year)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }

            result.Data = await _annualPlanMainService.GetSuperiorProjectDetail(departmentID, year, mainGoalID);
            return result.ToJson();
        }

        /// <summary>
        /// 新增目标任务
        /// </summary>
        /// <param name="addView">新的指标明细</param>
        /// <returns></returns>
        [HttpPost]
        [Route("AddProjectDetail")]
        public async Task<IActionResult> AddProjectDetail([FromBody] APProjectDetail addView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.AddProjectDetail(addView, session.HospitalID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 更新目标任务
        /// </summary>
        /// <param name="updateView">更新View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateProjectDetail")]
        public async Task<IActionResult> UpdateProjectDetail([FromBody] APProjectDetail updateView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.UpdateProjectDetail(updateView, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 排序目标任务
        /// </summary>
        /// <param name="resortView">更新View</param>
        /// <returns></returns>
        [HttpPost]
        [Route("ResetAnnualPlanProjectsSort")]
        public async Task<IActionResult> ResetAnnualPlanProjectsSort([FromBody] ResetProjectsSortDto resortView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            ViewCommonProp.Set(resortView, session);
            result.Data = await _annualPlanMainService.ResetAnnualPlanProjectsSort(resortView);
            return result.ToJson();
        }

        /// <summary>
        /// 删除目标任务
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="detailID">项目明细ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("DeleteProjectDetail")]
        public async Task<IActionResult> DeleteProjectDetail(string mainID, string detailID)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.DeleteProjectDetail(mainID, detailID, session.EmployeeID);
            return result.ToJson();
        }

        #endregion

        /// <summary>
        /// 复制新一年数据
        /// </summary>
        /// <param name="departmentID">部门ID，传空则全部</param>
        /// <param name="year">新一年度</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GenAnnualPlanData")]
        public async Task<IActionResult> GenAnnualPlanData(int? departmentID, int year)
        {
            var result = new ResponseResult
            {
                Data = await _annualGenerateService.GenAnnualPlanData(departmentID, year)
            };
            return result.ToJson();
        }

        /// <summary>
        /// 发布年度计划
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        [HttpGet]
        [Route("PublishAnnualPlan")]
        public async Task<IActionResult> PublishAnnualPlan(string mainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _annualPlanMainService.PublishAnnualPlan(mainID, session.EmployeeID);
            return result.ToJson();
        }
    }
}
