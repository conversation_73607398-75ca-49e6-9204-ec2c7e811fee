﻿using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NursingManagement.Common;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels.EmployeeGroup;

namespace NursingManagement.API.Controllers;

/// <summary>
/// 用户组维护
/// </summary>
[Route("api/EmployeeGroup")]
[ApiController]
[EnableCors("any")]
public class EmployeeGroupController(
    IEmployeeGroupMaintainService employeeGroupMaintainService,
    ISessionService session) : ControllerBase
{
    private readonly ISessionService _session = session;

    /// <summary>
    /// 获取用户组列表
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Route("GetEmployeeGroups")]
    public async Task<IActionResult> GetEmployeeGroups()
    {
        var result = new ResponseResult();
        var session = await _session.GetSession();
        if (session == null)
        {
            result.TimeOut();
            return result.ToJson();
        }
        result.Data = await employeeGroupMaintainService.GetEmployeeGroups(session.EmployeeID);
        return result.ToJson();
    }

    /// <summary>
    /// 获取用户组列表（选项）
    /// </summary>
    /// <returns></returns>
    [HttpGet]
    [Route("GetEmployeeGroupsAsOptions")]
    public async Task<IActionResult> GetEmployeeGroupsAsOptions()
    {
        var result = new ResponseResult();
        var session = await _session.GetSession();
        if (session == null)
        {
            result.TimeOut();
            return result.ToJson();
        }
        result.Data = await employeeGroupMaintainService.GetEmployeeGroupsAsOptions();
        return result.ToJson();
    }

    /// <summary>
    /// 新建用户组
    /// </summary>
    /// <param name="dto">参数</param>
    /// <returns></returns>
    [HttpPost]
    [Route("AddEmployeeGroup")]
    public async Task<IActionResult> AddEmployeeGroup([FromBody] AddEmployeeGroupDto dto)
    {
        var result = new ResponseResult();
        var session = await _session.GetSession();
        if (session == null)
        {
            result.TimeOut();
            return result.ToJson();
        }
        result.Data = await employeeGroupMaintainService.AddEmployeeGroup(dto, session.EmployeeID);
        return result.ToJson();
    }

    /// <summary>
    /// 更新用户组
    /// </summary>
    /// <param name="dto">参数</param>
    /// <returns></returns>
    [HttpPost]
    [Route("UpdateEmployeeGroup")]
    public async Task<IActionResult> UpdateEmployeeGroup([FromBody] UpdateEmployeeGroupDto dto)
    {
        var result = new ResponseResult();
        var session = await _session.GetSession();
        if (session == null)
        {
            result.TimeOut();
            return result.ToJson();
        }
        result.Data = await employeeGroupMaintainService.UpdateEmployeeGroup(dto, session.EmployeeID);
        return result.ToJson();
    }

    /// <summary>
    /// 删除用户组
    /// </summary>
    /// <param name="groupID">主键</param>
    /// <returns></returns>
    [HttpPost]
    [Route("DeleteEmployeeGroup")]
    public async Task<IActionResult> DeleteEmployeeGroup([FromForm] int groupID)
    {
        var result = new ResponseResult();
        var session = await _session.GetSession();
        if (session == null)
        {
            result.TimeOut();
            return result.ToJson();
        }
        result.Data = await employeeGroupMaintainService.DeleteEmployeeGroup(groupID, session.EmployeeID);
        return result.ToJson();
    }
}
