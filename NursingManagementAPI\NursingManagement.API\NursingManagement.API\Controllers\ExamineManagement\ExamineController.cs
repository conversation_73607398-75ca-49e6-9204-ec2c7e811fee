using Microsoft.AspNetCore.Cors;
using Microsoft.AspNetCore.Mvc;
using NLog;
using NursingManagement.Common;
using NursingManagement.Services;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Examine;

namespace NursingManagement.API.Controllers
{
    /// <summary>
    /// 考核控制器
    /// </summary>
    [Produces("application/json")]
    [Route("api/Examine")]
    [EnableCors("any")]
    public class ExamineController : Controller
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly ISessionService _session;
        private readonly IExamineService _examineService;
        private readonly IQuestionBankService _questionBankService;
        private readonly IExaminationPaperService _examinationPaperService;
        private readonly IExaminationAppointmentService _examinationAppointmentService;
        private readonly IExaminationPaperTemplateService _examinationPaperTemplateService;

        /// <summary>
        /// 构造器
        /// </summary>
        /// <param name="sessionService"></param>
        /// <param name="examineService"></param>
        /// <param name="questionBankService"></param>
        /// <param name="examinationPaperService"></param>
        /// <param name="examinationAppointmentService"></param>
        /// <param name="examinationPaperTemplateService"></param>
        public ExamineController(
            ISessionService sessionService
            , IExamineService examineService
            , IQuestionBankService questionBankService
            , IExaminationPaperService examinationPaperService
            , IExaminationAppointmentService examinationAppointmentService
            , IExaminationPaperTemplateService examinationPaperTemplateService
        )
        {
            _examineService = examineService;
            _session = sessionService;
            _questionBankService = questionBankService;
            _examinationPaperService = examinationPaperService;
            _examinationAppointmentService = examinationAppointmentService;
            _examinationPaperTemplateService = examinationPaperTemplateService;
        }

        #region  题库/题目相关接口

        /// <summary>
        /// 获取考核题库数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuestionBankList")]
        public async Task<IActionResult> GetQuestionBankList()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.GetQuestionBankList(session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存维护考核题库数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveQuestionBank")]
        public async Task<IActionResult> SaveQuestionBank([FromBody] QuestionBankView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.ModifyEmployeeID = string.IsNullOrEmpty(view.ModifyEmployeeID) ? session.EmployeeID : view.ModifyEmployeeID;
            view.HospitalID ??= string.IsNullOrEmpty(view.HospitalID) ? session.HospitalID : view.HospitalID;
            result.Data = await _questionBankService.SaveQuestionBank(view);
            return result.ToJson();
        }

        /// <summary>
        /// 删除考核题库维护数据
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteQuestionBank")]
        public async Task<IActionResult> DeleteQuestionBank(string questionBankID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.DeleteQuestionBank(questionBankID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取题目列表
        /// </summary>
        /// <param name="questionBankID">题库ID</param>
        /// <returns>题目列表</returns>
        [HttpGet]
        [Route("GetQuestionsByBankID")]
        public async Task<IActionResult> GetQuestionsByBankID(string questionBankID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var questions = await _questionBankService.GetQuestionsByBankIDAsync(questionBankID);
            result.Data = questions;
            return result.ToJson();
        }

        /// <summary>
        /// 删除指定题目
        /// </summary>
        /// <param name="questionID">题目ID</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("DeleteQuestion")]
        public async Task<IActionResult> DeleteQuestion(int questionID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var success = await _questionBankService.DeleteQuestionAsync(questionID, session.EmployeeID);
            if (success)
            {
                result.Data = true;
                result.Sucess();
            }
            else
            {
                result.Message = "删除题目失败";
            }
            return result.ToJson();
        }

        /// <summary>
        /// 导入题目到题库
        /// </summary>
        /// <param name="examinationQuestionOuterView">题目信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("ImportQuestions")]
        public async Task<IActionResult> ImportQuestion([FromBody] ExaminationQuestionOuterView examinationQuestionOuterView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var importSuccessFlag = await _questionBankService.ImportQuestionsAsync(examinationQuestionOuterView, session.EmployeeID);
            if (importSuccessFlag)
            {
                result.Data = true;
                result.Sucess();
            }
            else
            {
                result.Message = "导入失败";
            }
            return result.ToJson();
        }

        /// <summary>
        /// 保存题目和题目明细数据
        /// </summary>
        /// <param name="questionSaveParamsView">编辑的题目信息</param>
        /// <returns>操作结果</returns>
        [HttpPost]
        [Route("SaveQuestionData")]
        public async Task<IActionResult> SaveQuestionData([FromBody] ExaminationQuestionView questionSaveParamsView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            var success = await _questionBankService.SaveQuestionData(questionSaveParamsView, session.EmployeeID);
            if (success)
            {
                result.Data = true;
                result.Sucess();
            }
            else
            {
                result.Message = "保存失败";
            }

            return result.ToJson();
        }

        /// <summary>
        /// 获取题库对应关系字典
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuestionBankDict")]
        public async Task<IActionResult> GetQuestionBankDict()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.GetQuestionBankDictAsync();
            return result.ToJson();
        }

        /// <summary>
        /// 根据题库ID集合获取题目下拉框数据
        /// </summary>
        /// <param name="questionBankIDs"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("GetQuestionOptionList")]
        public async Task<IActionResult> GetQuestionOptionList(List<string> questionBankIDs)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.GetQuestionOptionList(questionBankIDs);
            return result.ToJson();
        }

        /// <summary>
        /// 获取题库下拉框内容
        /// </summary>
        /// <param name="isPractical">是否是获取实操类题库，true获取实操类，false获取非实操类，空获取所有</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuestionBankSelectList")]
        public async Task<IActionResult> GetQuestionBankSelectList(bool? isPractical)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.GetQuestionBankSelectList(isPractical);
            return result.ToJson();
        }

        /// <summary>
        /// 获取题库对应关系字典
        /// </summary>
        /// <param name="isPractical">是否是获取实操类题库，true获取实操类，false获取非实操类，空获取所有</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuestionBankCascader")]
        public async Task<IActionResult> GetQuestionBankCascader(bool? isPractical)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.GetQuestionBankCascaderAsync(isPractical, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 根据题库ID集合获取每个题库中各个题型的题目数量
        /// </summary>
        /// <param name="questionBankIDs">题库ID集合</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetQuestionTypeCount")]
        public async Task<IActionResult> GetQuestionTypeCount([FromQuery] List<string> questionBankIDs)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.GetQuestionTypeCount(questionBankIDs);
            return result.ToJson();
        }

        /// <summary>
        /// 更新题库排序
        /// </summary>
        /// <param name="bankSortView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateBankSort")]
        public async Task<IActionResult> UpdateBankSort([FromBody] List<BankSortView> bankSortView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.UpdateBankSort(bankSortView);
            return result.ToJson();
        }

        /// <summary>
        /// 更新题目排序
        /// </summary>
        /// <param name="questionSortView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("UpdateQuestionSort")]
        public async Task<IActionResult> UpdateQuestionSort([FromBody] List<BankSortView> questionSortView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.UpdateQuestionSort(questionSortView);
            return result.ToJson();
        }

        /// <summary>
        /// 获取题库对应的题目模板
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <param name="paperTitle"></param>
        /// <param name="isPractical"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperTemplateByBankID")]
        public async Task<IActionResult> GetPaperTemplateByBankID(string questionBankID, string paperTitle, bool isPractical)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            (result.Data, _) = await _examinationPaperTemplateService.GetPaperTemplateByBankID(questionBankID, paperTitle, isPractical);
            return result.ToJson();
        }

        /// <summary>
        /// 获取题库对应的题目模板
        /// </summary>
        /// <param name="questionBankID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetTheoryPaperTemplateByBankID")]
        public async Task<IActionResult> GetTheoryPaperTemplateByBankID(string questionBankID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            (result.Data, _) = await _examinationPaperTemplateService.CreateTemplateByBankID(questionBankID);
            return result.ToJson();
        }

        #endregion

        #region 试卷相关接口

        /// <summary>
        /// 获取试卷主记录数据
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="paperType"></param>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetExaminationPaperMainList")]
        public async Task<IActionResult> GetExaminationPaperMainList(DateTime startDate, DateTime endDate, string paperType, int? departmentID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationPaperService.GetExaminationPaperMainList(startDate, endDate, paperType, departmentID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 删除试卷相关数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteExaminationPaperMainData")]
        public async Task<IActionResult> DeleteExaminationPaperMainData(string mainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationPaperService.DeleteExaminationPaperMainData(mainID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存考核试卷
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveExaminationPaper")]
        public async Task<IActionResult> SaveExaminationPaper([FromBody] ExaminationPaperMainView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.ModifyEmployeeID = session.EmployeeID;
            var ret = false;
            // 实操类试卷保存逻辑
            if (view.IsPractical)
            {
                ret = await _examinationPaperService.SavePracticalPaper(view);
            }
            else
            {
                // 理论类试卷保存逻辑
                ret = await _examinationPaperService.SaveTheoryPaper(view);
            }
            if (ret)
            {
                result.Data = true;
            }
            else
            {
                result.Error("保存失败");
            }
            return result.ToJson();
        }

        /// <summary>
        /// 获取试卷模版数据
        /// </summary>
        /// <param name="mainID">试卷主表ID</param>
        /// <param name="isPreview">是否是预览</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperFormTemplate")]
        public async Task<IActionResult> GetPaperFormTemplate(string mainID, bool isPreview)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationPaperService.GetPaperFormTemplate(mainID, isPreview, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取试卷下拉框数据
        /// </summary>
        /// <param name="type"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperMainSelectList")]
        public async Task<IActionResult> GetPaperMainSelectList(string type)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationPaperService.GetPaperMainSelectList(type, session.EmployeeID, session.DepartmentID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取组卷规则下拉框数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperRuleSelectList")]
        public async Task<IActionResult> GetPaperRuleSelectList()
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationPaperService.GetPaperRuleSelectList();
            return result.ToJson();
        }

        /// <summary>
        /// 根据考核记录MainID获取试卷模版内容
        /// </summary>
        /// <param name="examinationMainID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPaperFormTemplateByMainID")]
        public async Task<IActionResult> GetPaperFormTemplateByMainID(string examinationMainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationPaperService.GetPaperFormTemplateByMainID(examinationMainID, session.EmployeeID, session.HospitalID);
            return result.ToJson();
        }

        /// <summary>
        /// 根据试卷主记录ID复制一份新的试卷
        /// </summary>
        /// <param name="examinationPaperMainID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("CopyExamPaperMain")]
        public async Task<IActionResult> CopyExamPaperMain(string examinationPaperMainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examinationPaperService.CopyExamPaperMain(examinationPaperMainID, session.EmployeeID);
            return result.ToJson();
        }

        #endregion

        #region 考核计划相关接口

        /// <summary>
        /// 获取考核主记录数据
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="type"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetExaminationRecordList")]
        public async Task<IActionResult> GetExaminationRecordList(DateTime startDate, DateTime endDate, string type)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.GetExaminationRecordList(startDate, endDate, type, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 删除考核主记录数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("DeleteExaminationRecordData")]
        public async Task<IActionResult> DeleteExaminationRecordData(string recordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.DeleteExaminationRecordData(recordID, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存考核主记录数据
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveExaminationRecordData")]
        public async Task<IActionResult> SaveExaminationRecordData([FromBody] ExaminationRecordView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.EmployeeID = session.EmployeeID;
            result.Data = await _examineService.SaveExaminationRecordData(view);
            return result.ToJson();
        }

        /// <summary>
        /// 发布考核
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("PublishExamine")]
        public async Task<IActionResult> PublishExamine([FromBody] ExaminationRecordView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.EmployeeID = session.EmployeeID;
            result.Data = await _examineService.PublishExamine(view);
            return result.ToJson();
        }

        /// <summary>
        /// 停止发布
        /// </summary>
        /// <param name="examinationRecordID">考核记录ID</param>
        /// <returns></returns>
        [HttpPost]
        [Route("StopPublishExamine")]
        public async Task<IActionResult> StopPublishExamine(string examinationRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.StopPublishExamine(examinationRecordID, session.EmployeeID);
            return result.ToJson();
        }

        #endregion

        #region 考核记录相关接口

        /// <summary>
        /// 获取考核主表数据
        /// </summary>
        /// <param name="examineEmployeeID"></param>
        /// <param name="examineRecordID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="examinationType">考核类型1、理论 2、实操</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetExamineMainList")]
        public async Task<IActionResult> GetExamineMainList(string examineEmployeeID, string examineRecordID, DateTime startDate, DateTime endDate, string examinationType)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.GetExamineMainList(examineEmployeeID, examineRecordID, startDate, endDate, examinationType);
            return result.ToJson();
        }

        /// <summary>
        /// 更改考核主表状态
        /// </summary>
        /// <param name="examinationMainID"></param>
        /// <param name="statusCode"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("ChangeExamineMainStatus")]
        public async Task<IActionResult> ChangeExamineMainStatus(string examinationMainID, string statusCode)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.ChangeExamineMainStatus(examinationMainID, statusCode, session.EmployeeID);
            return result.ToJson();
        }

        #region 补考相关逻辑
        /// <summary>
        /// 获取考核计划符合补考的人员
        /// </summary>
        /// <param name="examinationRecordID"></param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetRetakeEmployeeIDsByRecordID")]
        public async Task<IActionResult> GetRetakeEmployeeIDsByRecordID(string examinationRecordID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.GetRetakeEmployeeIDsByRecordID(examinationRecordID);
            return result.ToJson();
        }

        /// <summary>
        /// 保存补考
        /// </summary>
        /// <param name="saveRetakeExamineView"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveRetakeExamine")]
        public async Task<IActionResult> SaveRetakeExamine([FromBody] SaveRetakeExamineView saveRetakeExamineView)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            saveRetakeExamineView.AddEmployeeID = session.EmployeeID;
            result.Data = await _examineService.SaveRetakeExamine(saveRetakeExamineView);
            return result.ToJson();
        }
        #endregion

        /// <summary>
        /// 获取考核记录(个人理论考核以及主考的实操考核)
        /// </summary>
        /// <param name="employeeID">人员工号</param>
        /// <param name="statusCodes">作答状态</param>
        /// <param name="startDate">开始日期</param>
        /// <param name="endDate">结束日期</param>
        /// <param name="filterExaminerFlag">根据主考人筛选数据开关</param>
        /// <param name="examinationType">根据主考人筛选数据开关</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetEmployeeExamineMainList")]
        public async Task<IActionResult> GetEmployeeExamineMainList(string employeeID, string statusCodes, bool filterExaminerFlag, string examinationType, DateTime? startDate, DateTime? endDate)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.GetEmployeeExamineMainList(employeeID, statusCodes, filterExaminerFlag, examinationType, startDate, endDate);
            return result.ToJson();
        }

        /// <summary>
        /// 保存考核明细记录
        /// </summary>
        /// <param name="view"></param>
        /// <returns></returns>
        [HttpPost]
        [Route("SaveExamineDetail")]
        public async Task<IActionResult> SaveExamineDetail([FromBody] SaveExaminationDetailView view)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            view.ModifyEmployeeID = session.EmployeeID;
            result.Data = await _examineService.SaveExamineDetail(view);
            return result.ToJson();
        }

        /// <summary>
        /// 二维码扫描签到
        /// </summary>
        /// <param name="examinationRecordID">考核主记录ID</param>
        /// <param name="timeStamp">二维码时间戳</param>
        /// <returns></returns>
        [HttpGet]
        [Route("ExaminationSignIn")]
        public async Task<IActionResult> ExaminationSignIn(string examinationRecordID, long timeStamp)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.ExaminationSignIn(examinationRecordID, timeStamp, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 判断考核是否完成考核
        /// </summary>
        /// <param name="examinationMainID">考核Main记录主键</param>
        /// <returns></returns>
        [HttpGet]
        [Route("CheckExamineCompleteAssessOrNot")]
        public async Task<IActionResult> CheckExamineCompleteAssessOrNot(string examinationMainID)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.CheckExamineCompleteAssessOrNot(examinationMainID, session.EmployeeID);
            return result.ToJson();
        }

        #endregion

        #region 刷题相关接口

        /// <summary>
        /// 保存刷题练习考核记录
        /// </summary>
        /// <param name="questionBankID">题库ID</param>
        /// <param name="questionBankName">题库名称（含父级题库名称）</param>
        /// <returns></returns>
        [HttpPost]
        [Route("SavePracticeExamination")]
        public async Task<IActionResult> SavePracticeExaminationRecord(string questionBankID, string questionBankName)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.SavePracticeExaminationRecord(questionBankID, questionBankName, session.DepartmentID, session.EmployeeID, session.HospitalID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取刷题练习数据
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="statusCode">考核状态</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        [HttpGet]
        [Route("GetPracticeExamineMainList")]
        public async Task<IActionResult> GetPracticeExamineMainList(string employeeID, string statusCode, DateTime? startDate, DateTime? endDate)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.GetPracticeExamineMainList(employeeID ?? session.EmployeeID, statusCode, startDate, endDate);
            return result.ToJson();
        }

        #endregion

        /// <summary>
        /// 更新题目排序
        /// </summary>
        /// <param name="questionBankID">题库ID</param>
        /// <param name="cloneQuestionBankIDs">被复制题库ID集合</param>
        /// <returns></returns>
        [HttpPost]
        [Route("CloneQuestionBank")]
        public async Task<IActionResult> CloneQuestionBank([FromForm] string questionBankID, [FromForm] List<string> cloneQuestionBankIDs)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.CloneQuestionBankAsync(questionBankID, cloneQuestionBankIDs, session.EmployeeID);
            return result.ToJson();
        }

        #region 实操类考核提前通知相关接口

        /// <summary>
        /// （预约过的实操类考核，提前一天通知考核人参加考核）定时任务调用
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("SendPracticalExamNotification")]
        public async Task<IActionResult> SendPracticalExamNotification()
        {
            var result = new ResponseResult();
            result.Data = await _examinationAppointmentService.SendPracticalExamNotification();
            return result.ToJson();
        }

        #endregion

        /// <summary>
        /// 根据试卷主记录ID复制一份新的试卷
        /// </summary>
        /// <param name="questionBank">前端返回的题库view</param>
        /// <returns></returns>
        [HttpPost]
        [Route("CopyQuestionBank")]
        public async Task<IActionResult> CopyQuestionBank(QuestionBankView questionBank)
        {
            var result = new ResponseResult();
            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _questionBankService.CopyQuestionBankAsync(questionBank, session.EmployeeID);
            return result.ToJson();
        }

        /// <summary>
        /// 获取未完成的的实操类考核计划记录
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetNotFinishedPracticalRecords")]
        public async Task<IActionResult> GetNotFinishedPracticalRecords()
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.GetNotFinishedPracticalRecords();
            return result.ToJson();
        }
        /// <summary>
        /// 获取考核记录汇总数据
        /// </summary>
        /// <returns></returns>
        [HttpGet]
        [Route("GetExaminationSummaryList")]
        public async Task<IActionResult> GetExaminationSummaryList([FromQuery] ExaminationSummaryQueryView summaryQueryView)
        {
            var result = new ResponseResult();

            var session = await _session.GetSession();
            if (session == null)
            {
                result.TimeOut();
                return result.ToJson();
            }
            result.Data = await _examineService.GetExaminationSummaryList(summaryQueryView);
            return result.ToJson();
        }
    }
}
