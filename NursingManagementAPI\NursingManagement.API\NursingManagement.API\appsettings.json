{
  "ConnectionStrings": {
    "Connection": "server=**************;TrustServerCertificate=true;database=NursingManagement;uid=zhongYunCCC;pwd=**`1q",
    "MessageConnection": "server=**************;database=Message;uid=zhongYunCCC;pwd=**`1q",
    "RedisConnection": "127.0.0.1:6379,password=ZhongYun`1q20230716,syncTimeout =20000,connectTimeout=3000,connectRetry=3,DefaultDatabase=6",
    "HangFireConnection": "server=**************;TrustServerCertificate=true;database=HangFire;uid=Medical;pwd=************",

    // 宏力内网
    //"Connection": "server=***********;TrustServerCertificate=true;database=NursingManagement;uid=zhytech;pwd=*****************",
    //"MessageConnection": "server=**************;database=Message;uid=medical;pwd=******",
    //"RedisConnection": "127.0.0.1:6379,password=ZhongYun`1q20230716,syncTimeout =20000,connectTimeout=3000,connectRetry=3,DefaultDatabase=6",
    //"HangFireConnection": "server=***********;database=HangFireNursingManagement;uid=sa;pwd=*****************;TrustServerCertificate=true"
  },
  "Cache": {
    "Dictionary": 3000
  },
  "MQ": {
    "HostName": "127.0.0.1"
  },

  "Configs": {
    "HospitalID": "1",
    "Language": 1,
    "ServerType": 1,
    "UploadDocumentPath": "E:\\uploadDocuments",
    "FileBaseUrl": "http://**********:8900" //可以是服务器上的地址
  },
  "UseSwagger": "true"
}
