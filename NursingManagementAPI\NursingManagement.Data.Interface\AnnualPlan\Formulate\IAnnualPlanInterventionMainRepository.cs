﻿using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public interface IAnnualPlanInterventionMainRepository
    {
        /// <summary>
        /// 根据计划主表ID获取分解目标任务字典ID集合
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <returns></returns>
        Task<int[]> GetInterventionIDsByPlanMainID(string annualPlanMainID);

        /// <summary>
        /// 获取年度计划-计划制定列表
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <returns></returns>
        Task<List<APIntervention>> GetViewsByPlanMainID(string mainID, string projectDetailID = null);

        /// <summary>
        /// 获取计划制定主表Model
        /// </summary>
        /// <param name="planMainID">主表ID</param>
        /// <param name="projectDetailIDs">工作项目ID集合</param>
        /// <returns></returns>
        Task<List<AnnualInterventionMainInfo>> GetInfosByProjectDetailIDs(string planMainID, params string[] projectDetailIDs);

        /// <summary>
        /// 根据ID获取年度计划-计划制定主表信息
        /// </summary>
        /// <param name="interventionMainID">制定主表ID</param>
        /// <returns></returns>
        Task<AnnualInterventionMainInfo> GetInfoByID(string interventionMainID);

        /// <summary>
        /// 根据年度计划主键ID获取明细数据
        /// </summary>
        /// <param name="annualPlanMainID"></param>
        /// <returns></returns>
        Task<List<APInterventionDetailView>> GetDetailByMainID(string annualPlanMainID);

        /// <summary>
        /// 依据年度计划措施查询记录
        /// </summary>
        /// <param name="planInterventionMainIDs">年度计划措施集合</param>
        /// <returns></returns>
        Task<List<AnnualInterventionMainInfo>> GetInfoByIDs(List<string> planInterventionMainIDs);

        /// <summary>
        /// 获取不跟踪的执行项目主表集合
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        Task<APInterventionView[]> GetAPInterventionViews(string annualPlanMainID);

        /// <summary>
        /// 获取当前计划已有的负责人选项情况
        /// </summary>
        /// <param name="planMainID"></param>
        /// <returns></returns>
        Task<List<APInterventionRecPrincipal>> GetRecPrincipalViewByPlanMainID(string planMainID);

        /// <summary>
        /// 依据计划措施MainID获取部分字段数据
        /// </summary>
        /// <param name="planInterventionMainIDs">计划措施MainID集合</param>
        /// <returns></returns>
        Task<List<APInterventionMainView>> GetPartByIDs(IEnumerable<string> planInterventionMainIDs);

        /// <summary>
        /// 获取执行项目主表信息
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        Task<List<AnnualInterventionMainInfo>> GetInfosByPlanMainAsNoTracking(string annualPlanMainID);

        /// <summary>
        /// 依据计划措施MainID集合获取部分字段数据
        /// </summary>
        /// <param name="annualPlanInterventionMainIDs"></param>
        /// <returns></returns>
        Task<List<APInterventionDetailView>> GetViewByInterventionMainIDs(List<string> annualPlanInterventionMainIDs);

        /// <summary>
        /// 获取执行项目ID与自定义名称
        /// </summary>
        /// <param name="interventionIDs">字典ID集合</param>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        Task<Dictionary<int, string[]>> GetInterventionIDAndLocalShowName(int[] interventionIDs, string annualPlanMainID);

        /// <summary>
        /// 获取执行项目转换视图
        /// </summary>
        /// <param name="planMainID">主表ID</param>
        /// <param name="months">月份集合</param>
        /// <returns></returns>
        Task<List<APInterventionConvertView>> GetConvertView(string planMainID, int[] months);
    }
}
