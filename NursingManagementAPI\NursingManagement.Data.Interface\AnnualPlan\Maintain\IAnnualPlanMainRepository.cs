using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 年度计划主表
    /// </summary>
    public interface IAnnualPlanMainRepository
    {
        /// <summary>
        /// 获取年度计划业务主表View
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<APMainView> GetAnnualPlanMain(string mainID);

        /// <summary>
        /// 获取年度计划业务主表
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        Task<AnnualPlanMainInfo> GetInfoByMainID(string mainID);

        /// <summary>
        /// 获取年度计划主表ID
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        Task<string> GetMainIDByDeptIDAndYear(int departmentID, int year);

        /// <summary>
        /// 获取不跟踪的年度计划主表信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        Task<List<AnnualPlanMainInfo>> GetInfoByMainIDAndYearAsNoTracking(int? departmentID, int year);

        /// <summary>
        /// 获取年度计划主表信息
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <returns></returns>
        Task<APMainView[]> GetPlanMainViewsByYearAndDepartmentIDs(int year, IEnumerable<int> departmentIDs);

        /// <summary>
        /// 获取年度计划查询信息
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <returns></returns>
        Task<AnnualPlanQueryView[]> GetAnnualPlanQueryViews(int year, IEnumerable<int> departmentIDs);

        /// <summary>
        /// 根据部门ID和制定年份获取年度计划
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        Task<AnnualPlanGeneralView[]> GetAPGeneralViewsByYear(int departmentID, int year);

        /// <summary>
        /// 获取已有对应年份年度计划的部门ID集合
        /// </summary>
        /// <param name="year">年份</param>
        /// <returns></returns>
        Task<List<int>> GetPlanMainExistedDepartmentIDs(int year);

        /// <summary>
        /// 获取部门ID键值对
        /// </summary>
        /// <param name="mainIDs">计划主表ID</param>
        /// <returns></returns>
        Task<Dictionary<string, int>> GetDepartmentIDsByMainIDs(string[] mainIDs);

        /// <summary>
        /// 获取年度计划状态
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        Task<(string, bool)> GetAnnualPlanMainIDAndStatus(int departmentID, int year);
    }
}
