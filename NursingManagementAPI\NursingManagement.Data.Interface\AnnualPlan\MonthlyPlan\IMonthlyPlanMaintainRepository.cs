﻿using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.AnnualPlan;
using NursingManagement.ViewModels.AnnualPlan.MonthlyPlan;
using NursingManagement.ViewModels.AnnualPlan.QuarterPlan;

namespace NursingManagement.Data.Interface;

public interface IMonthlyPlanMaintainRepository
{

    /// <summary>
    /// 获取月度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">年度计划主表iD</param>
    /// <param name="quarter">月度</param>
    /// <returns></returns>
    Task<string> GetMonthlyPlanMainID(string annualPlanMainID, int quarter);

    /// <summary>
    /// 获取月度计划
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    Task<MonthlyPlanMainInfo> GetMonthlyPlanMain(string monthlyPlanMainID);

    /// <summary>
    /// 获取月度计划状态
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    Task<AnnualPlanEnums.PlanStatus> GetMonthlyPlanStatus(string monthlyPlanMainID);

    /// <summary>
    /// 获取月度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">年度计划主表ID</param>
    /// <returns></returns>
    Task<Dictionary<string, Dictionary<int, string>>> GetMonthToID(string[] annualPlanMainIDs);
    /// <summary>
    /// 获取月度计划
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    Task<PlanAndWorksVo[]> GetMonthlyWorks(string monthlyPlanMainID);

    /// <summary>
    /// 获取工作ID与工作内容
    /// </summary>
    /// <param name="workIDs">工作ID集合</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetIDAndWorkContent(string[] workIDs);

    /// <summary>
    /// 获取选择工作视图
    /// </summary>
    /// <param name="month">月份</param>
    /// <param name="apMainIDs">年度计划主键集合</param>
    /// <param name="excludeApInterventionIDs">需排除的工作字典ID</param>
    /// <returns></returns>
    Task<List<PlanWorkImportVo>> GetMonthlyWorkImportViews(int month, string[] apMainIDs, int[] excludeApInterventionIDs);

    /// <summary>
    /// 获取快捷引用列表
    /// </summary>
    /// <param name="apMainIDs">年度计划主键集合</param>
    /// <param name="month">月度</param>
    /// <param name="apInterventionID">工作字典ID</param>
    /// <returns></returns>
    Task<List<PlanWorkImportVo>> GetMonthlyPlanWorkQuickRefVos(string[] apMainIDs, int month, int apInterventionID);

    /// <summary>
    /// 获取月度计划工作
    /// </summary>
    /// <param name="workIDs">工作ID集合</param>
    /// <returns></returns>
    Task<List<MonthlyPlanDetailInfo>> GetMonthlyPlanWorks(string[] workIDs);
    /// <summary>
    /// 获取月度计划工作
    /// </summary>
    /// <param name="quarterWorkID">主键</param>
    /// <returns></returns>
    Task<MonthlyPlanDetailInfo> GetMonthlyWork(string quarterWorkID);

    /// <summary>
    /// 获取月度计划已参考过的分解目标任务字典ID
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主键</param>
    /// <returns></returns>
    Task<int[]> GetMonthlyPlanWorkInterventionIDs(string monthlyPlanMainID);
    /// <summary>
    /// 获取月度计划工作关联字典ID集合
    /// </summary>
    /// <param name="year">年</param>
    /// <param name="departmentIDs">病区集合</param>
    /// <returns></returns>
    Task<List<MonthlyPlanQueryVo>> GetMonthlyPlanQueryViews(int year, IEnumerable<int> departmentIDs);
    /// <summary>
    /// 获取大于给定Sort值的工作
    /// </summary>
    /// <param name="monthlyPlanMainID">主表ID</param>
    /// <param name="typeID">分类ID</param>
    /// <param name="sort">序号</param>
    /// <returns></returns>
    Task<List<MonthlyPlanDetailInfo>> GetGtSortWorks(string monthlyPlanMainID, int typeID, int sort);
    /// <summary>
    /// 获取大于等于给定Sort值的工作
    /// </summary>
    /// <param name="typeIDAndSort">分类及对应Sort</param>
    /// <returns></returns>
    Task<List<MonthlyPlanDetailInfo>> GetGteMinSortMonthlyWorksByTypeID(Dictionary<int, int> typeIDAndSort);

    /// <summary>
    /// 获取月度计划预览视图
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划ID</param>
    /// <returns></returns>
    Task<MonthlyPlanPreview> GetMonthlyPlanPreview(string monthlyPlanMainID);

    /// <summary>
    /// 获取导出视图
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    Task<MonthlyPlanExportView> GetExportView(string monthlyPlanMainID);

}
