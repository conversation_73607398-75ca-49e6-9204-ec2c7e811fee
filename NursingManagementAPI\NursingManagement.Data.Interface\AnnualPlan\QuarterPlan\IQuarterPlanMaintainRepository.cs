﻿using NursingManagement.Models;
using NursingManagement.ViewModels.AnnualPlan;
using NursingManagement.ViewModels.AnnualPlan.QuarterPlan;

namespace NursingManagement.Data.Interface;

public interface IQuarterPlanMaintainRepository
{

    /// <summary>
    /// 获取季度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">年度计划主表iD</param>
    /// <param name="quarter">季度</param>
    /// <returns></returns>
    Task<string> GetQuarterPlanMainID(string annualPlanMainID, int quarter);

    /// <summary>
    /// 获取季度计划
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    Task<QuarterPlanMainInfo> GetQuarterPlanMain(string quarterPlanMainID);

    /// <summary>
    /// 获取季度计划状态
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    Task<AnnualPlanEnums.PlanStatus> GetQuarterPlanStatus(string quarterPlanMainID);

    /// <summary>
    /// 获取季度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">年度计划主表ID</param>
    /// <returns></returns>
    Task<Dictionary<string, Dictionary<int, string>>> GetQuarterToID(string[] annualPlanMainIDs);
    /// <summary>
    /// 获取季度计划
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    Task<PlanWorksVo[]> GetQuarterWorks(string quarterPlanMainID);
    /// <summary>
    /// 获取工作ID与工作内容
    /// </summary>
    /// <param name="workIDs">工作ID集合</param>
    /// <returns></returns>
    Task<Dictionary<string, string>> GetIDAndWorkContent(string[] workIDs);

    /// <summary>
    /// 获取选择工作视图
    /// </summary>
    /// <param name="apMainIDs">年度计划主键集合</param>
    /// <param name="quarter">季度</param>
    /// <param name="excludeApInterventionIDs">需排除的工作字典ID</param>
    /// <returns></returns>
    Task<List<PlanWorkImportVo>> GetQuarterWorkImportViews(string[] apMainIDs, int quarter, int[] excludeApInterventionIDs);

    /// <summary>
    /// 获取快捷引用列表
    /// </summary>
    /// <param name="apMainIDs">年度计划主键集合</param>
    /// <param name="quarter">季度</param>
    /// <param name="apInterventionID">工作字典ID</param>
    /// <returns></returns>
    Task<List<PlanWorkImportVo>> GetQuarterPlanWorkQuickRefVos(string[] apMainIDs, int quarter, int apInterventionID);
    /// <summary>
    /// 转换季度计划工作为月度计划导入视图对象
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主键</param>
    /// <param name="quarter">季度</param>
    /// <param name="month">月份</param>
    /// <returns></returns>
    Task<PlanWorkImportVo> GetQuarterPlanWorksForMonthlyPlan(string quarterPlanMainID, int month, int[] refInterventionIDs);
    /// <summary>
    /// 获取季度计划工作
    /// </summary>
    /// <param name="workIDs">工作ID集合</param>
    /// <returns></returns>
    Task<List<QuarterPlanDetailInfo>> GetQuarterPlanWorks(string[] workIDs);
    /// <summary>
    /// 获取季度计划工作
    /// </summary>
    /// <param name="quarterWorkID">主键</param>
    /// <returns></returns>
    Task<QuarterPlanDetailInfo> GetQuarterWork(string quarterWorkID);

    /// <summary>
    /// 获取某季度计划工作关联字典ID集合
    /// </summary>
    /// <param name="qpMainID">季度计划主表ID</param>
    /// <param name="quarter">季度</param>
    /// <returns></returns>
    Task<int[]> GetQpWorkInterventionIDs(string qpMainID, int quarter);
    /// <summary>
    /// 获取季度计划工作关联字典ID集合
    /// </summary>
    /// <param name="year">年</param>
    /// <param name="departmentIDs">病区集合</param>
    /// <returns></returns>
    Task<List<QuarterPlanPreviewVo>> GetQuarterPlanQueryViews(int year, IEnumerable<int> departmentIDs);
    /// <summary>
    /// 获取导出视图
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    Task<QuarterPlanExportView> GetExportView(string quarterPlanMainID);
    /// <summary>
    /// 获取季度计划预览视图
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划ID</param>
    /// <returns></returns>
    Task<QuarterPlanAndWorksPreviewVo> GetQuarterPlanPreview(string quarterPlanMainID);
    /// <summary>
    /// 获取大于等于给定Sort值的工作
    /// </summary>
    /// <param name="typeIDAndSort">分类及对应Sort</param>
    /// <returns></returns>
    Task<List<QuarterPlanDetailInfo>> GetGteMinSortQuarterWorksByTypeID(Dictionary<int, int> typeIDAndSort);
    /// <summary>
    /// 获取季度计划某分类下的工作
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划</param>
    /// <param name="typeID">分类ID</param>
    /// <returns></returns>
    Task<List<QuarterPlanDetailInfo>> GetWorksByQuarterPlanMainIDAndTypeID(string quarterPlanMainID, int typeID);
    /// <summary>
    /// 获取大于给定Sort值的工作
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <param name="typeID">分类ID</param>
    /// <param name="sort">序号</param>
    /// <returns></returns>
    Task<List<QuarterPlanDetailInfo>> GetGtSortWorks(string quarterPlanMainID, int typeID, int sort);
    /// <summary>
    /// 获取每个分类的最大Sort值
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    Task<Dictionary<int, int>> GetTypeIDAndMaxSort(string quarterPlanMainID);
}
