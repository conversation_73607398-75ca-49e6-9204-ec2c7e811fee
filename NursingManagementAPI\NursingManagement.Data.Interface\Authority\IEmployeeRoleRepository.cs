﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface
{
    public interface IEmployeeRoleRepository : ICacheRepository
    {
        /// <summary>
        /// 根据EmployeeID获取角色清单
        /// </summary>
        /// <returns></returns>
        Task<List<int>> GetRolesByEmployeeID(string employeeID);

        /// <summary>
        /// 根据EmployeeID获取相关角色配置，用于角色更新
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeeRoleInfo>> GetEmployeeRoleInfoByEmployeeID(string employeeID);

        /// <summary>
        /// 获取病人所有的角色（包含删除的）
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        Task<List<EmployeeRoleInfo>> GetAllEmployeeRoleByEmployeeIDAsync(string employeeID);
        /// <summary>
        /// 从一组人员工号中筛选出指定角色的工号集合
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="authorityRoleID">角色ID</param>
        /// <returns></returns>
        Task<List<string>> FilterEmployeeByRole(List<string> employeeIDs, int authorityRoleID);
    }
}
