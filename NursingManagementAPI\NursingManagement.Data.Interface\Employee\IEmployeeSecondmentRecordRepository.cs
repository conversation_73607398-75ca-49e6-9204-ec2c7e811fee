﻿using NursingManagement.Models;

namespace NursingManagement.Data.Interface.Employee
{
    public interface IEmployeeSecondmentRecordRepository
    {
        /// <summary>
        /// 根据记录ID（主键）获取数据
        /// </summary>
        /// <param name="employeeSecondmentRecordID"></param>
        /// <returns></returns>
        Task<EmployeeSecondmentRecordInfo> GetDataByID(string employeeSecondmentRecordID);

        /// <summary>
        /// 根据原部门ID获取数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordInfo>> GetDataByDepartmentID(int departmentID, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 根据借调部门ID获取数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordInfo>> GetDataBySecondmentDepartmentID(int departmentID, DateTime startDate, DateTime endDate);

        /// <summary>
        /// 根据部门ID集合获取数据
        /// </summary>
        /// <param name="departmentIDs"></param>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordInfo>> GetDataByDepartmentIDList(List<int> departmentIDs);

        /// <summary>
        /// 获取所有数据
        /// </summary>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordInfo>> GetAllData();

        /// <summary>
        /// 根据员工编号获取借调数据
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordInfo>> GetDataByEmployeeID(string employeeID);

        /// <summary>
        /// 根据主键获取记录
        /// </summary>
        /// <param name="recordIDs">主键ID集合</param>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordInfo>> GetRecordsByIDsAsNoTrackAsync(List<string> recordIDs);

        /// <summary>
        /// 根据用户ID获取在指定时间范围内的借调记录
        /// </summary>
        /// <param name="employeeIDs">患者ID</param>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<List<EmployeeSecondmentRecordInfo>> GetPartDataByEmployeeIDsAndDateRange(List<string> employeeIDs, DateTime startDate, DateTime endDate);
    }
}
