using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    /// <summary>
    /// 监考安排仓储接口
    /// </summary>
    public interface IExaminerScheduleRepository
    {
        /// <summary>
        /// 根据ID获取监考安排
        /// </summary>
        /// <param name="examinerScheduleID">监考安排ID</param>
        /// <returns>监考安排信息</returns>
        Task<ExaminerScheduleInfo> GetByIdAsync(string examinerScheduleID);

        /// <summary>
        /// 根据主键ID集合获取监考计划
        /// </summary>
        /// <param name="examinerScheduleIDs">监考记录主键ID集合</param>
        /// <returns></returns>
        Task<List<ExaminerScheduleInfo>> GetListByIdsAsync(List<string> examinerScheduleIDs);

        /// <summary>
        /// 根据考核计划获取监考计划列表
        /// </summary>
        /// <param name="examinationRecordID">考核计划ID</param>
        /// <param name="scheduleDate">监考日期</param>
        /// <returns></returns>
        Task<List<ExaminerScheduleInfo>> GetListAsync(string examinationRecordID, DateTime scheduleDate);

        /// <summary>
        /// 根据考核计划和时间范围获取监考计划列表
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        Task<List<ExaminerScheduleInfo>> GetListByDateRange(DateTime startDate, DateTime endDate);

        /// <summary>
        /// 根据考核记录获取每一个预约人员的考核位置信息
        /// </summary>
        /// <param name="examinationRecordIDs"></param>
        /// <returns></returns>
        Task<List<ExamineLocationView>> GetExamineLocationListAsync(List<string> examinationRecordIDs);
    }
}
