﻿using NursingManagement.Models;
using NursingManagement.ViewModels.EmployeeGroup;

namespace NursingManagement.Data.Interface;

public interface IEmployeeGroupRepository
{
    /// <summary>
    /// 获取用户组列表
    /// </summary>
    /// <returns></returns>
    Task<EmployeeGroupDto[]> GetEmployeeGroups();
    /// <summary>
    /// 获取用户组列表（选项）
    /// </summary>
    /// <returns></returns>
    Task<EmployeeGroupAsOptionDto[]> GetEmployeeGroupsAsOptions();
    /// <summary>
    /// 获取用户组信息
    /// </summary>
    /// <param name="employeeGroupID">主键</param>
    /// <returns></returns>
    Task<EmployeeGroupInfo> GetEmployeeGroupInfo(int employeeGroupID);
}
