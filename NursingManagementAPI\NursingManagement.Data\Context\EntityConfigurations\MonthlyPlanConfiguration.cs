﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NursingManagement.Models;

namespace NursingManagement.Data.Context.EntityConfigurations;

public class MonthlyPlanMainConfiguration : IEntityTypeConfiguration<MonthlyPlanMainInfo>
{
    public void Configure(EntityTypeBuilder<MonthlyPlanMainInfo> builder)
    {
        builder.ToTable("MonthlyPlanMain");
        builder.HasKey(m => m.MonthlyPlanMainID);
        builder.Property(m => m.MonthlyPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
        builder.Property(m => m.AnnualPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
        builder.HasMany(m => m.MonthlyPlanDetails)
            .WithOne(m => m.MonthlyPlanMainInfo)
            .HasForeignKey(m => m.MonthlyPlanMainID);
    }
}

public class MonthlyPlanDetailConfiguration : IEntityTypeConfiguration<MonthlyPlanDetailInfo>
{
    public void Configure(EntityTypeBuilder<MonthlyPlanDetailInfo> builder)
    {
        builder.ToTable("MonthlyPlanDetail");
        builder.HasKey(m => m.MonthlyPlanDetailID);
        builder.Property(m => m.MonthlyPlanDetailID).HasColumnType("varchar(32)").HasMaxLength(32);
        builder.Property(m => m.MonthlyPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
        builder.Property(m => m.WorkContent).HasColumnType("nvarchar(200)").HasMaxLength(200);
        builder.Property(m => m.Requirement).HasColumnType("nvarchar(200)").HasMaxLength(200);
        builder.Property(m => m.PrincipalName).HasColumnType("varchar(50)").HasMaxLength(50);
        // 一个Work对应多个WorkToTask，一个WorkToTask对应一个Work
        builder.HasMany(m => m.MonthlyWorkToTasks).WithOne(m => m.MonthlyPlanDetail)
            .HasForeignKey(m => m.MonthlyPlanDetailID);
        // 一个Work对应多个Principal，一个Principal对应一个Work
        builder.HasMany(m => m.Principals).WithOne(m => m.MonthlyPlanDetailInfo)
            .HasForeignKey(m => m.MonthlyPlanDetailID);
    }
}

public class MonthlyPlanDetailPrincipalConfiguration : IEntityTypeConfiguration<MonthlyPlanDetailPrincipalInfo>
{
    public void Configure(EntityTypeBuilder<MonthlyPlanDetailPrincipalInfo> builder)
    {
        builder.ToTable("MonthlyPlanDetailPrincipal");
        builder.HasKey(m => m.MonthlyPlanDetailPrincipalID);
        builder.Property(m => m.MonthlyPlanDetailID).HasColumnType("varchar(32)").HasMaxLength(32);
        builder.Property(m => m.EmployeeGroupName).HasColumnType("nvarchar(20)").HasMaxLength(20);
        builder.Property(m => m.EmployeeID).HasColumnType("varchar(20)").HasMaxLength(20);
        builder.Property(m => m.DeleteFlag).HasColumnType("varchar(1)").HasMaxLength(1);
        builder.HasOne(m => m.MonthlyPlanDetailInfo)
            .WithMany(m => m.Principals)
            .HasForeignKey(m => m.MonthlyPlanDetailID);
    }
}
