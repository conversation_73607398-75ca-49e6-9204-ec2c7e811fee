﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Metadata.Builders;
using NursingManagement.Models;

namespace NursingManagement.Data.Context.EntityConfigurations
{
    public class QuarterPlanMainConfiguration : IEntityTypeConfiguration<QuarterPlanMainInfo>
    {
        public void Configure(EntityTypeBuilder<QuarterPlanMainInfo> builder)
        {
            builder.ToTable("QuarterPlanMain");
            builder.HasKey(m => m.QuarterPlanMainID);
            builder.Property(m => m.QuarterPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.AnnualPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.HasMany(m => m.QuarterPlanDetailInfos)
                .WithOne(m => m.QuarterPlanMainInfo)
                .HasForeignKey(m => m.QuarterPlanMainID);
        }
    }

    public class QuarterPlanDetailConfiguration : IEntityTypeConfiguration<QuarterPlanDetailInfo>
    {
        public void Configure(EntityTypeBuilder<QuarterPlanDetailInfo> builder)
        {
            builder.ToTable("QuarterPlanDetail");
            builder.HasKey(m => m.QuarterPlanDetailID);
            builder.Property(m => m.QuarterPlanDetailID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.QuarterPlanMainID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.WorkContent).HasColumnType("nvarchar(200)").HasMaxLength(200);
            builder.Property(m => m.Requirement).HasColumnType("nvarchar(200)").HasMaxLength(200);
            // 一个Work对应多个Principal，一个Principal对应一个Work
            builder.HasMany(m => m.Principals).WithOne(m => m.QuarterPlanDetailInfo)
                .HasForeignKey(m => m.QuarterPlanDetailID);
        }
    }

    public class QuarterPlanDetailPrincipalConfiguration : IEntityTypeConfiguration<QuarterPlanDetailPrincipalInfo>
    {
        public void Configure(EntityTypeBuilder<QuarterPlanDetailPrincipalInfo> builder)
        {
            builder.ToTable("QuarterPlanDetailPrincipal");
            builder.HasKey(m => m.QuarterPlanDetailPrincipalID);
            builder.Property(m => m.QuarterPlanDetailID).HasColumnType("varchar(32)").HasMaxLength(32);
            builder.Property(m => m.EmployeeGroupName).HasColumnType("nvarchar(20)").HasMaxLength(20);
            builder.Property(m => m.EmployeeID).HasColumnType("varchar(20)").HasMaxLength(20);
            builder.Property(m => m.DeleteFlag).HasColumnType("varchar(1)").HasMaxLength(1);
            builder.HasOne(m => m.QuarterPlanDetailInfo)
                .WithMany(m => m.Principals)
                .HasForeignKey(m => m.QuarterPlanDetailID);
        }
    }
}
