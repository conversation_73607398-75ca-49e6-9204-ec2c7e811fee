﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Interface
{
    public class AnnualPlanInterventionMainRepository : IAnnualPlanInterventionMainRepository
    {
        private readonly NursingManagementDbContext _dbContext;

        public AnnualPlanInterventionMainRepository(NursingManagementDbContext dbContext)
        {
            _dbContext = dbContext;
        }

        /// <summary>
        /// 根据计划主表ID获取分解目标任务字典ID集合
        /// </summary>
        /// <param name="annualPlanMainID">年度计划主表ID</param>
        /// <returns></returns>
        public async Task<int[]> GetInterventionIDsByPlanMainID(string annualPlanMainID)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.Where(m => m.AnnualPlanMainID == annualPlanMainID && m.DeleteFlag != "*")
                .Select(m => m.InterventionID).ToArrayAsync();
        }

        /// <summary>
        /// 获取执行项目列表
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="projectDetailID">项目明细ID，可空</param>
        /// <returns></returns>
        public async Task<List<APIntervention>> GetViewsByPlanMainID(string mainID, string projectDetailID = null)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.Where(x => x.AnnualPlanMainID == mainID && x.DeleteFlag != "*")
                .IfWhere(!string.IsNullOrEmpty(projectDetailID), m => m.ProjectDetailID == projectDetailID)
                .Include(m => m.Principals.Where(n => n.DeleteFlag != "*"))
                .Include(m => m.AnnualInterventionDetailInfos.Where(n => n.DeleteFlag != "*"))
                .Select(m => new APIntervention
                {
                    AnnualPlanInterventionMainID = m.AnnualPlanInterventionMainID,
                    AnnualPlanMainGoalID = m.AnnualPlanMainGoalID,
                    ProjectDetailID = m.ProjectDetailID,
                    InterventionID = m.InterventionID,
                    LocalShowName = m.LocalShowName,
                    PrincipalGroupName = m.PrincipalGroupName,
                    PrincipalIDs = m.Principals.Select(n => n.EmployeeID).ToArray(),
                    PlanMonths = m.AnnualInterventionDetailInfos.Select(n => n.PlanMonth).ToArray()
                }).ToListAsync();
        }

        /// <summary>
        /// 获取计划制定主表Model
        /// </summary>
        /// <param name="planMainID">主表ID</param>
        /// <param name="projectDetailIDs">工作项目ID集合</param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionMainInfo>> GetInfosByProjectDetailIDs(string planMainID, params string[] projectDetailIDs)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.Where(x => x.AnnualPlanMainID == planMainID && projectDetailIDs.Contains(x.ProjectDetailID) && x.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据ID获取年度计划-计划制定主表信息
        /// </summary>
        /// <param name="interventionMainID">制定主表ID</param>
        /// <returns></returns>
        public async Task<AnnualInterventionMainInfo> GetInfoByID(string interventionMainID)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.FirstOrDefaultAsync(x => x.AnnualPlanInterventionMainID == interventionMainID && x.DeleteFlag != "*");
        }

        /// <summary>
        /// 根据年度计划主键ID获取明细数据
        /// </summary>
        /// <param name="annualPlanMainID"></param>
        /// <returns></returns>
        public async Task<List<APInterventionDetailView>> GetDetailByMainID(string annualPlanMainID)
        {
            return await (from a in _dbContext.AnnualPlanInterventionMainInfos
                          join b in _dbContext.AnnualPlanProjectDetailInfos on a.ProjectDetailID equals b.DetailID
                          join c in _dbContext.AnnualPlanInterventionDetailInfos on a.AnnualPlanInterventionMainID equals c.AnnualPlanInterventionMainID
                          where a.AnnualPlanMainID == annualPlanMainID
                          select new APInterventionDetailView
                          {
                              AnnualPlanMainGoalID = a.AnnualPlanMainGoalID,
                              ItemContent = b.Content,
                              ProjectDetailID = a.ProjectDetailID,
                              PrincipalGroupName = a.PrincipalGroupName,
                              PlanMonth = c.PlanMonth,
                              Sort = b.Sort,
                              AnnualPlanInterventionMainID = a.AnnualPlanInterventionMainID
                          }).ToListAsync();
        }

        /// <summary>
        /// 依据年度计划措施查询记录
        /// </summary>
        /// <param name="planInterventionMainIDs"></param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionMainInfo>> GetInfoByIDs(List<string> planInterventionMainIDs)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.Where(x => planInterventionMainIDs.Contains(x.AnnualPlanInterventionMainID) && x.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 获取不跟踪的执行项目主表集合
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<APInterventionView[]> GetAPInterventionViews(string annualPlanMainID)
        {
            return await (from a in _dbContext.AnnualPlanInterventionMainInfos.Where(m => m.DeleteFlag != "*" && m.AnnualPlanMainID == annualPlanMainID)
                          join b in _dbContext.AnnualPlanInterventionDetailInfos.Where(m => m.DeleteFlag != "*")
                          on a.AnnualPlanInterventionMainID equals b.AnnualPlanInterventionMainID
                          join c in _dbContext.AnnualInterventionMainPrincipalInfos.Where(m => m.DeleteFlag != "*")
                          on a.AnnualPlanInterventionMainID equals c.AnnualInterventionMainID
                          select new APInterventionView
                          {
                              APInterventionMainID = a.AnnualPlanInterventionMainID,
                              APInterventionDetailID = b.AnnualPlanInterventionDetailID,
                              APInterventionPrincipalID = c.ID,
                              InterventionID = a.InterventionID,
                              LocalShowName = a.LocalShowName,
                              PlanDate = b.PlanDate,
                              PlanMonth = b.PlanMonth,
                              PrincipalID = c.EmployeeID
                          }
                    ).ToArrayAsync();
        }

        /// <summary>
        /// 获取当前计划已有的负责人选项情况
        /// </summary>
        /// <param name="planMainID"></param>
        /// <returns></returns>
        public async Task<List<APInterventionRecPrincipal>> GetRecPrincipalViewByPlanMainID(string planMainID)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.Where(m => m.AnnualPlanMainID == planMainID && !string.IsNullOrEmpty(m.PrincipalGroupName) && m.DeleteFlag != "*")
                .Join(_dbContext.AnnualInterventionMainPrincipalInfos.Where(m => m.DeleteFlag != "*"),
                m => m.AnnualPlanInterventionMainID, m => m.AnnualInterventionMainID,
                (m, n) => new { m.PrincipalGroupName, n.EmployeeID })
                .GroupBy(m => m.PrincipalGroupName)
                .Select(n => new APInterventionRecPrincipal
                {
                    PrincipalGroupName = n.Key,
                    PrincipalIDs = n.Select(m => m.EmployeeID).Distinct().ToArray()
                }).ToListAsync();
        }

        /// <summary>
        /// 获取部分字段数据
        /// </summary>
        /// <param name="planInterventionMainIDs">执行项目主表ID集合</param>
        /// <returns></returns>
        public async Task<List<APInterventionMainView>> GetPartByIDs(IEnumerable<string> planInterventionMainIDs)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.Where(x => planInterventionMainIDs.Contains(x.AnnualPlanInterventionMainID) && x.DeleteFlag != "*")
                .Select(m => new APInterventionMainView
                {
                    APMainGoalID = m.AnnualPlanMainGoalID,
                    AnnualPlanInterventionMainID = m.AnnualPlanInterventionMainID,
                    InterventionID = m.InterventionID,
                    LocalShowName = m.LocalShowName,
                }).ToListAsync();
        }

        /// <summary>
        /// 获取执行项目主表信息
        /// </summary>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualInterventionMainInfo>> GetInfosByPlanMainAsNoTracking(string annualPlanMainID)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.AsNoTracking().Where(x => x.AnnualPlanMainID == annualPlanMainID && x.DeleteFlag != "*").ToListAsync();
        }

        public async Task<List<APInterventionDetailView>> GetViewByInterventionMainIDs(List<string> annualPlanInterventionMainIDs)
        {
            return await (from a in _dbContext.AnnualPlanInterventionMainInfos
                          join b in _dbContext.AnnualPlanProjectDetailInfos on a.ProjectDetailID equals b.DetailID
                          join c in _dbContext.AnnualPlanMainGoalInfos on a.AnnualPlanMainGoalID equals c.AnnualPlanMainGoalID
                          join d in _dbContext.AnnualGoalListInfos on c.AnnualGoalID equals d.AnnualGoalID
                          where annualPlanInterventionMainIDs.Contains(a.AnnualPlanInterventionMainID)
                          select new APInterventionDetailView
                          {
                              AnnualPlanMainGoalID = a.AnnualPlanMainGoalID,
                              ItemContent = b.Content,
                              ProjectDetailID = a.ProjectDetailID,
                              PrincipalGroupName = a.PrincipalGroupName,
                              AnnualPlanInterventionMainID = a.AnnualPlanInterventionMainID,
                              GoalContent = d.GoalContent
                          }).ToListAsync();
        }

        /// <summary>
        /// 获取执行项目ID与自定义名称
        /// </summary>
        /// <param name="interventionIDs">字典ID集合</param>
        /// <param name="annualPlanMainID">计划主表ID</param>
        /// <returns></returns>
        public async Task<Dictionary<int, string[]>> GetInterventionIDAndLocalShowName(int[] interventionIDs, string annualPlanMainID)
        {
             return await _dbContext.AnnualPlanInterventionMainInfos.Where(m => interventionIDs.Contains(m.InterventionID) && m.AnnualPlanMainID == annualPlanMainID && m.DeleteFlag != "*")
                .GroupBy(m => m.InterventionID)
                .ToDictionaryAsync(m => m.Key, m => m.Select(n => n.LocalShowName).ToArray());
        }

        /// <summary>
        /// 获取执行项目转换视图
        /// </summary>
        /// <param name="planMainID">主表ID</param>
        /// <param name="months">月份集合</param>
        /// <returns></returns>
        public async Task<List<APInterventionConvertView>> GetConvertView(string planMainID, int[] months)
        {
            return await _dbContext.AnnualPlanInterventionMainInfos.Where(m => m.AnnualPlanMainID == planMainID && m.DeleteFlag != "*")
                .Include(m => m.AnnualInterventionDetailInfos.Where(n => months.Contains(n.PlanMonth) && n.DeleteFlag != "*"))
                .Select(m => new APInterventionConvertView
                {
                    APInterventionMainID = m.AnnualPlanInterventionMainID,
                    InterventionID = m.InterventionID,
                    APMainGoalID = m.AnnualPlanMainGoalID,
                    LocalShowName = m.LocalShowName,
                    PrincipalGroupName = m.PrincipalGroupName,
                    PlanMonths = m.AnnualInterventionDetailInfos.Select(n => n.PlanMonth).ToList()
                }).ToListAsync();
        }
    }
}
