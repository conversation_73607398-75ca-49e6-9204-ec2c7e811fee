﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 年度计划主表
    /// </summary>
    public class AnnualPlanMainRepository : IAnnualPlanMainRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDb;

        public AnnualPlanMainRepository(NursingManagementDbContext nursingManagementDb)
        {
            _nursingManagementDb = nursingManagementDb;
        }

        /// <summary>
        /// 获取年度计划业务主表View
        /// </summary>
        /// <param name="annualPlanMainID">主表ID</param>
        /// <returns></returns>
        public async Task<APMainView> GetAnnualPlanMain(string annualPlanMainID)
        {
            return await _nursingManagementDb.AnnualPlanMainInfos.Where(m => m.DeleteFlag != "*").Select(m => new APMainView
            {
                MainID = m.AnnualPlanMainID,
                Year = m.Year,
                DepartmentID = m.DepartmentID,
                Planner = m.Planner,
            }).FirstOrDefaultAsync(m => m.MainID == annualPlanMainID);
        }

        /// <summary>
        /// 获取年度计划主表ID
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        public async Task<string> GetMainIDByDeptIDAndYear(int departmentID, int year)
        {
            return await _nursingManagementDb.AnnualPlanMainInfos
                .Where(m => m.DepartmentID == departmentID && m.Year == year && m.DeleteFlag != "*")
                .Select(m => m.AnnualPlanMainID).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 获取不跟踪的年度计划主表信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanMainInfo>> GetInfoByMainIDAndYearAsNoTracking(int? departmentID, int year)
        {
            return await _nursingManagementDb.AnnualPlanMainInfos.AsNoTracking()
                .IfWhere(departmentID.HasValue, m => m.DepartmentID == departmentID)
                .Where(m => m.Year == year && m.DeleteFlag != "*")
                .ToListAsync();
        }

        /// <summary>
        /// 获取年度计划主表信息
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <returns></returns>
        public async Task<APMainView[]> GetPlanMainViewsByYearAndDepartmentIDs(int year, IEnumerable<int> departmentIDs)
        {
            return await _nursingManagementDb.AnnualPlanMainInfos
                .Where(m => m.Year == year && departmentIDs.Contains(m.DepartmentID) && m.DeleteFlag != "*")
                .Select(m => new APMainView
                {
                    MainID = m.AnnualPlanMainID,
                    DepartmentID = m.DepartmentID,
                    Planner = m.Planner,
                    Year = m.Year,
                    AddDateTime = m.AddDateTime,
                    StatusCode = m.StatusCode,
                }).ToArrayAsync();
        }

        /// <summary>
        /// 获取年度计划查询信息
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <returns></returns>
        public async Task<AnnualPlanQueryView[]> GetAnnualPlanQueryViews(int year, IEnumerable<int> departmentIDs)
        {
            return await _nursingManagementDb.AnnualPlanMainInfos
                .Where(m => m.Year == year && departmentIDs.Contains(m.DepartmentID) && m.DeleteFlag != "*")
                .Select(m => new AnnualPlanQueryView
                {
                    Key = m.AnnualPlanMainID,
                    DepartmentID = m.DepartmentID,
                    Planner = m.Planner,
                    ModifyDateTime = m.ModifyDateTime,
                    StatusCode = m.StatusCode,
                }).ToArrayAsync();
        }

        /// <summary>
        /// 根据部门ID组合和制定年份获取年度计划
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        public async Task<AnnualPlanGeneralView[]> GetAPGeneralViewsByYear(int departmentID, int year)
        {
            return await (from a in _nursingManagementDb.AnnualPlanMainInfos
                          join b in _nursingManagementDb.AnnualPlanMainGoalInfos
                          on a.AnnualPlanMainID equals b.AnnualPlanMainID
                          where a.DeleteFlag != "*" && b.DeleteFlag != "*" && departmentID == a.DepartmentID && a.Year == year
                          select new AnnualPlanGeneralView
                          {
                              AnnualPlanMainID = a.AnnualPlanMainID,
                              AnnualPlanMainGoalID = b.AnnualPlanMainGoalID,
                              DepartmentID = a.DepartmentID,
                              GoalID = b.AnnualGoalID,
                              Sort = b.Sort
                          }).ToArrayAsync();
        }

        /// <summary>
        /// 获取已有对应年份年度计划的部门ID集合
        /// </summary>
        /// <param name="year">年份</param>
        /// <returns></returns>
        public async Task<List<int>> GetPlanMainExistedDepartmentIDs(int year)
        {
            return await _nursingManagementDb.AnnualPlanMainInfos
                .Where(m => m.Year == year && m.DeleteFlag != "*")
                .Select(m => m.DepartmentID).Distinct().ToListAsync();
        }

        /// <summary>
        /// 获取年度计划主表信息
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanMainInfo> GetInfoByMainID(string mainID)
        {
            return await _nursingManagementDb.AnnualPlanMainInfos.FirstOrDefaultAsync(m => m.AnnualPlanMainID == mainID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 获取部门ID键值对
        /// </summary>
        /// <param name="mainIDs">计划主表ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, int>> GetDepartmentIDsByMainIDs(string[] mainIDs)
        {
            return await _nursingManagementDb.AnnualPlanMainInfos.Where(m => mainIDs.Contains(m.AnnualPlanMainID) && m.DeleteFlag != "*")
                .ToDictionaryAsync(m => m.AnnualPlanMainID, m => m.DepartmentID);
        }

        /// <summary>
        /// 获取年度计划状态
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        public async Task<(string, bool)> GetAnnualPlanMainIDAndStatus(int departmentID, int year)
        {
            var plan = await _nursingManagementDb.AnnualPlanMainInfos
                .Where(m => m.DepartmentID == departmentID && m.Year == year && m.DeleteFlag != "*")
                .Select(m => new
                {
                    m.AnnualPlanMainID,
                    m.StatusCode
                }).FirstOrDefaultAsync();
            if (plan is null)
            {
                return ("", false);
            }
            return (plan.AnnualPlanMainID, plan.StatusCode == AnnualPlanEnums.PlanStatus.Published);
        }
    }
}
