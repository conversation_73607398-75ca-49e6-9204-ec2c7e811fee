﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.AnnualPlan;
using NursingManagement.ViewModels.AnnualPlan.MonthlyPlan;
using NursingManagement.ViewModels.AnnualPlan.QuarterPlan;
using System.Linq;

namespace NursingManagement.Data;

public class MonthlyPlanMaintainRepository(NursingManagementDbContext dbContext) : IMonthlyPlanMaintainRepository
{
    /// <summary>
    /// 获取月度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">部门ID</param>
    /// <param name="month">月份</param>
    /// <returns></returns>
    public async Task<string> GetMonthlyPlanMainID(string annualPlanMainID, int month)
    {
        return await dbContext.MonthlyPlanMainInfos.Where(m => m.AnnualPlanMainID == annualPlanMainID && m.Month == month && m.DeleteFlag != "*")
            .Select(m => m.MonthlyPlanMainID)
            .FirstOrDefaultAsync();
    }
    /// <summary>
    /// 获取月度计划
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    public async Task<MonthlyPlanMainInfo> GetMonthlyPlanMain(string monthlyPlanMainID)
    {
        return await dbContext.MonthlyPlanMainInfos
            .FirstOrDefaultAsync(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.DeleteFlag != "*");
    }

    /// <summary>
    /// 获取月度计划状态
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    public async Task<AnnualPlanEnums.PlanStatus> GetMonthlyPlanStatus(string monthlyPlanMainID)
    {
        return await dbContext.MonthlyPlanMainInfos.Where(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.DeleteFlag != "*")
            .Select(m => m.StatusCode)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// 获取月度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">年度计划主表ID</param>
    /// <returns></returns>
    public async Task<Dictionary<string, Dictionary<int, string>>> GetMonthToID(string[] annualPlanMainIDs)
    {
        return await dbContext.MonthlyPlanMainInfos.Where(m => annualPlanMainIDs.Contains(m.AnnualPlanMainID) && m.DeleteFlag != "*")
            .GroupBy(m => m.AnnualPlanMainID)
            .ToDictionaryAsync(m => m.Key, m => m.ToDictionary(n => n.Month, n => n.MonthlyPlanMainID));
    }
    /// <summary>
    /// 获取月度计划View
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    public async Task<PlanAndWorksVo[]> GetMonthlyWorks(string monthlyPlanMainID)
    {
        // 第一步：先获取所有符合条件的原始数据
        var monthlyPlanDetailsData = await dbContext.MonthlyPlanDetailInfos
            .Where(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.DeleteFlag != "*")
            .Include(m => m.Principals.Where(p => p.DeleteFlag != "*"))
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var works = monthlyPlanDetailsData
            .GroupBy(m => m.TypeID)
            .Select(m => new PlanAndWorksVo
            {
                TypeID = m.Key,
                Children = m.Select(n => new PlanAndWorksVo.Work
                {
                    MonthlyPlanDetailID = n.MonthlyPlanDetailID,
                    APInterventionID = n.APInterventionID,
                    TypeID = n.TypeID,
                    Sort = n.Sort,
                    WorkContent = n.WorkContent,
                    Requirement = n.Requirement,
                    WorkType = n.WorkType,
                    IsTemp = n.IsTemp,
                    PrincipalName = n.PrincipalName,
                    Principals = n.Principals.Select(q => new AnnualPrincipal(
                        q.EmployeeID,
                        q.EmployeeGroupID,
                        q.EmployeeGroupName
                        )).ToArray()
                }).ToArray()
            }).ToArray();
        return works;
    }

    /// <summary>
    /// 获取工作ID与工作内容
    /// </summary>
    /// <param name="workIDs">工作ID集合</param>
    /// <returns></returns>
    public async Task<Dictionary<string, string>> GetIDAndWorkContent(string[] workIDs)
    {
        return await dbContext.MonthlyPlanDetailInfos.Where(m => m.DeleteFlag != "*" && workIDs.Contains(m.MonthlyPlanDetailID))
            .ToDictionaryAsync(m => m.MonthlyPlanDetailID, m => m.WorkContent);
    }

    /// <summary>
    /// 获取多个部门的月度计划工作
    /// </summary>
    /// <param name="month">月份</param>
    /// <param name="apMainIDs">年度计划主表ID</param>
    /// <param name="excludeApInterventionIDs">要排除的字典项</param>
    /// <returns></returns>
    public async Task<List<PlanWorkImportVo>> GetMonthlyWorkImportViews(int month, string[] apMainIDs, int[] excludeApInterventionIDs)
    {
        // 第一步：先获取所有符合条件的原始数据
        var monthlyPlansData = await dbContext.MonthlyPlanMainInfos
            .Where(m => m.Month == month && apMainIDs.Contains(m.AnnualPlanMainID) && m.StatusCode == AnnualPlanEnums.PlanStatus.Published && m.DeleteFlag != "*")
            .AsSplitQuery()
            .Include(m => m.MonthlyPlanDetails.Where(m => m.DeleteFlag != "*"))
            .ThenInclude(m => m.Principals.Where(n => n.DeleteFlag != "*"))
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var works = monthlyPlansData.Select(m => new PlanWorkImportVo
        {
            DepartmentID = m.DepartmentID,
            Children = m.MonthlyPlanDetails
            .GroupBy(n => n.TypeID)
            .Select(n => new PlanWorkImportVo.TypeGroup
            {
                TypeID = n.Key,
                Children = n.IfWhere(excludeApInterventionIDs.Length > 0, o => o.APInterventionID.HasValue && !excludeApInterventionIDs.Contains(o.APInterventionID.Value))
                .Select(p => new PlanWorkImportVo.TypeGroup.Work
                {
                    APInterventionID = p.APInterventionID,
                    TypeID = p.TypeID,
                    Sort = p.Sort,
                    WorkContent = p.WorkContent,
                    Requirement = p.Requirement,
                    WorkType = p.WorkType,
                    IsTemp = p.IsTemp,
                    PrincipalName = p.PrincipalName,
                    Principals = p.Principals.Select(q => new AnnualPrincipal(
                        q.EmployeeID,
                        q.EmployeeGroupID,
                        q.EmployeeGroupName
                        )).ToArray()
                }).ToArray()
            }).ToArray()
        }).ToList();

        return works;
    }

    public async Task<List<PlanWorkImportVo>> GetMonthlyPlanWorkQuickRefVos(string[] apMainIDs, int month, int apInterventionID)
    {
        // 第一步：先获取所有符合条件的原始数据
        var quarterPlansData = await dbContext.MonthlyPlanMainInfos
            .AsSplitQuery()
            .Include(m => m.MonthlyPlanDetails.Where(n => n.APInterventionID == apInterventionID && !n.IsTemp && n.DeleteFlag != "*"))
            .ThenInclude(m => m.Principals.Where(n => n.DeleteFlag != "*"))
            .Where(m => m.DeleteFlag != "*" && month == m.Month && apMainIDs.Contains(m.AnnualPlanMainID) && m.StatusCode == AnnualPlanEnums.PlanStatus.Published)
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var works = quarterPlansData.Select(m => new PlanWorkImportVo
        {
            DepartmentID = m.DepartmentID,
            Children = m.MonthlyPlanDetails
                .GroupBy(n => n.TypeID)
                .Select(n => new PlanWorkImportVo.TypeGroup
                {
                    TypeID = n.Key,
                    Children = n.Select(p => new PlanWorkImportVo.TypeGroup.Work
                    {
                        APInterventionID = p.APInterventionID,
                        TypeID = p.TypeID,
                        Sort = p.Sort,
                        WorkContent = p.WorkContent,
                        Requirement = p.Requirement,
                        WorkType = p.WorkType,
                        IsTemp = p.IsTemp,
                        PrincipalName = p.PrincipalName,
                        Principals = p.Principals.Select(q => new AnnualPrincipal(
                            q.EmployeeID,
                            q.EmployeeGroupID,
                            q.EmployeeGroupName
                        )).ToArray()
                    }).ToArray()
                }).ToArray()
        }).ToList();
        return works;
    }

    public async Task<List<MonthlyPlanDetailInfo>> GetMonthlyPlanWorks(string[] monthlyPlanDetailIDs)
    {
        return await dbContext.MonthlyPlanDetailInfos.Include(m => m.Principals.Where(n => n.DeleteFlag != "*"))
            .Where(m => m.DeleteFlag != "*" && monthlyPlanDetailIDs.Contains(m.MonthlyPlanDetailID)).ToListAsync();
    }
    /// <summary>
    /// 获取月度计划工作
    /// </summary>
    /// <param name="monthlyPlanDetailID">主键</param>
    /// <returns></returns>
    public async Task<MonthlyPlanDetailInfo> GetMonthlyWork(string monthlyPlanDetailID)
    {
        return await dbContext.MonthlyPlanDetailInfos
            .Include(m => m.Principals.Where(n => n.DeleteFlag != "*"))
            .Where(m => m.MonthlyPlanDetailID == monthlyPlanDetailID).FirstOrDefaultAsync();
    }

    public async Task<int[]> GetMonthlyPlanWorkInterventionIDs(string monthlyPlanMainID)
    {
        return await dbContext.MonthlyPlanDetailInfos
            .Where(m => monthlyPlanMainID == m.MonthlyPlanMainID && m.APInterventionID.HasValue && m.DeleteFlag != "*")
            .Select(m => m.APInterventionID.Value).Distinct().ToArrayAsync();
    }
    /// <summary>
    /// 获取月度计划工作关联字典ID集合
    /// </summary>
    /// <param name="year">年</param>
    /// <param name="departmentIDs">病区集合</param>
    /// <returns></returns>
    public async Task<List<MonthlyPlanQueryVo>> GetMonthlyPlanQueryViews(int year, IEnumerable<int> departmentIDs)
    {
        return await dbContext.MonthlyPlanMainInfos.Where(m => m.Year == year && departmentIDs.Contains(m.DepartmentID) && m.DeleteFlag != "*")
            .Select(m => new MonthlyPlanQueryVo
            {
                Key = m.MonthlyPlanMainID,
                DepartmentID = m.DepartmentID,
                Monthly = m.Month,
                Planner = m.AddEmployeeID,
                ModifyDateTime = m.ModifyDateTime,
                StatusCode = m.StatusCode,
            }).ToListAsync();
    }
    /// <summary>
    /// 获取大于给定Sort值的工作
    /// </summary>
    /// <param name="monthlyPlanMainID">主表ID</param>
    /// <param name="typeID">分类ID</param>
    /// <param name="sort">序号</param>
    /// <returns></returns>
    public async Task<List<MonthlyPlanDetailInfo>> GetGtSortWorks(string monthlyPlanMainID, int typeID, int sort)
    {
        return await dbContext.MonthlyPlanDetailInfos.Where(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.TypeID == typeID && m.Sort > sort && m.DeleteFlag != "*")
            .ToListAsync();
    }
    /// <summary>
    /// 获取大于等于给定Sort值的工作
    /// </summary>
    /// <param name="typeIDAndSort">分类及对应Sort</param>
    /// <returns></returns>
    public async Task<List<MonthlyPlanDetailInfo>> GetGteMinSortMonthlyWorksByTypeID(Dictionary<int, int> typeIDAndSort)
    {
        var works = new List<MonthlyPlanDetailInfo>();
        foreach (var pair in typeIDAndSort)
        {
            var typeWorks = await dbContext.MonthlyPlanDetailInfos.Where(m => m.TypeID == pair.Key && m.Sort >= pair.Value && m.DeleteFlag != "*").ToListAsync();
            works.AddRange(typeWorks);
        }
        return works;
    }

    /// <summary>
    /// 获取月度计划预览视图
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划ID</param>
    /// <returns></returns>
    public async Task<MonthlyPlanPreview> GetMonthlyPlanPreview(string monthlyPlanMainID)
    {
        // 第一步：先获取所有符合条件的原始数据
        var monthlyPlanDetailsData = await dbContext.MonthlyPlanDetailInfos
            .Where(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.DeleteFlag != "*")
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var data = monthlyPlanDetailsData
            .GroupBy(m => m.TypeID)
            .Select(m => new MonthlyPlanPreview.PlanType
            {
                TypeId = m.Key,
                PlanWorks = m.Select(n => new MonthlyPlanPreview.PlanType.PlanWork
                {
                    WorkContent = n.WorkContent,
                    Requirement = n.Requirement,
                    PrincipalName = n.PrincipalName,
                    WorkType = n.WorkType
                }).ToArray()
            }).ToArray();
        return new MonthlyPlanPreview
        {
            MonthlyPlanMainID = monthlyPlanMainID,
            PlanTypes = data
        };
    }

    /// <summary>
    /// 获取导出视图
    /// </summary>
    /// <param name="monthlyPlanMainID">月度计划主表ID</param>
    /// <returns></returns>
    public async Task<MonthlyPlanExportView> GetExportView(string monthlyPlanMainID)
    {
        // 第一步：先获取所有符合条件的原始数据
        var monthlyPlanDetailsData = await dbContext.MonthlyPlanDetailInfos
            .Where(m => m.MonthlyPlanMainID == monthlyPlanMainID && m.DeleteFlag != "*")
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var data = monthlyPlanDetailsData
            .GroupBy(m => m.TypeID)
            .Select(m => new MonthlyPlanExportView.PlanType
            {
                TypeID = m.Key,
                PlanWorks = m.Select(n => new MonthlyPlanExportView.PlanType.PlanWork
                {
                    WorkType = n.WorkType,
                    WorkContent = n.WorkContent,
                    Requirement = n.Requirement,
                    PrincipalName = n.PrincipalName,
                }).ToArray()
            }).ToArray();
        return new MonthlyPlanExportView
        {
            MonthlyPlanMainID = monthlyPlanMainID,
            PlanTypes = data
        };
    }
}
