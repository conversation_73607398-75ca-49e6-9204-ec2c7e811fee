﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels.AnnualPlan;
using NursingManagement.ViewModels.AnnualPlan.QuarterPlan;
using System.Linq;

namespace NursingManagement.Data;

public class QuarterPlanMaintainRepository(NursingManagementDbContext dbContext) : IQuarterPlanMaintainRepository
{

    /// <summary>
    /// 获取季度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">部门ID</param>
    /// <param name="quarter">季度</param>
    /// <returns></returns>
    public async Task<string> GetQuarterPlanMainID(string annualPlanMainID, int quarter)
    {
        return await dbContext.QuarterPlanMainInfos.Where(m => m.AnnualPlanMainID == annualPlanMainID && m.Quarter == quarter && m.DeleteFlag != "*")
            .Select(m => m.QuarterPlanMainID)
            .FirstOrDefaultAsync();
    }
    /// <summary>
    /// 获取季度计划
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    public async Task<QuarterPlanMainInfo> GetQuarterPlanMain(string quarterPlanMainID)
    {
        return await dbContext.QuarterPlanMainInfos
            .FirstOrDefaultAsync(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*");
    }

    /// <summary>
    /// 获取季度计划状态
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    public async Task<AnnualPlanEnums.PlanStatus> GetQuarterPlanStatus(string quarterPlanMainID)
    {
        return await dbContext.QuarterPlanMainInfos.Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
            .Select(m => m.StatusCode)
            .FirstOrDefaultAsync();
    }

    /// <summary>
    /// 获取季度计划主表ID
    /// </summary>
    /// <param name="annualPlanMainID">年度计划主表ID</param>
    /// <returns></returns>
    public async Task<Dictionary<string, Dictionary<int, string>>> GetQuarterToID(string[] annualPlanMainIDs)
    {
        return await dbContext.QuarterPlanMainInfos.Where(m => annualPlanMainIDs.Contains(m.AnnualPlanMainID) && m.DeleteFlag != "*")
            .GroupBy(m => m.AnnualPlanMainID)
            .ToDictionaryAsync(m => m.Key, m => m.ToDictionary(n => n.Quarter, n => n.QuarterPlanMainID));
    }
    /// <summary>
    /// 获取季度计划View
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    public async Task<PlanWorksVo[]> GetQuarterWorks(string quarterPlanMainID)
    {
        // 第一步：先获取所有符合条件的原始数据
        var quarterPlanDetailsData = await dbContext.QuarterPlanDetailInfos
            .Include(m => m.Principals.Where(n => n.DeleteFlag != "*"))
            .Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var views = quarterPlanDetailsData
            .GroupBy(m => m.TypeID)
            .Select(m => new PlanWorksVo
            {
                TypeID = m.Key,
                Children = m.Select(n => new PlanWorksVo.Work
                {
                    QuarterPlanDetailID = n.QuarterPlanDetailID,
                    APInterventionID = n.APInterventionID,
                    TypeID = n.TypeID,
                    Sort = n.Sort,
                    WorkContent = n.WorkContent,
                    WorkType = n.WorkType,
                    IsTemp = n.IsTemp,
                    Requirement = n.Requirement,
                    PrincipalName = n.PrincipalName,
                    Principals = n.Principals.Select(p => new AnnualPrincipal(
                        p.EmployeeID,
                        p.EmployeeGroupID,
                        p.EmployeeGroupName
                    )).ToArray()
                }).OrderBy(m => m.Sort.HasValue ? 0 : 1).ThenBy(m => m.Sort).ToArray()
            }).ToArray();
        return views;
    }

    /// <summary>
    /// 获取工作ID与工作内容
    /// </summary>
    /// <param name="workIDs">工作ID集合</param>
    /// <returns></returns>
    public async Task<Dictionary<string, string>> GetIDAndWorkContent(string[] workIDs)
    {
        return await dbContext.QuarterPlanDetailInfos.Where(m => m.DeleteFlag != "*" && workIDs.Contains(m.QuarterPlanDetailID))
            .ToDictionaryAsync(m => m.QuarterPlanDetailID, m => m.WorkContent);
    }

    public async Task<List<PlanWorkImportVo>> GetQuarterWorkImportViews(string[] apMainIDs, int quarter, int[] excludeApInterventionIDs)
    {
        // 第一步：先获取所有符合条件的原始数据
        var quarterPlansData = await dbContext.QuarterPlanMainInfos
            .Where(m => m.Quarter == quarter && apMainIDs.Contains(m.AnnualPlanMainID) && m.StatusCode == AnnualPlanEnums.PlanStatus.Published && m.DeleteFlag != "*")
            .AsSplitQuery()
            .Include(m => m.QuarterPlanDetailInfos.Where(m => m.DeleteFlag != "*"))
            .ThenInclude(m => m.Principals.Where(n => n.DeleteFlag != "*"))
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var works = quarterPlansData.Select(m => new PlanWorkImportVo
        {
            DepartmentID = m.DepartmentID,
            Children = m.QuarterPlanDetailInfos
                .GroupBy(n => n.TypeID)
                .Select(n => new PlanWorkImportVo.TypeGroup
                {
                    TypeID = n.Key,
                    Children = n.IfWhere(excludeApInterventionIDs.Length > 0, o => o.APInterventionID.HasValue &&
                    !excludeApInterventionIDs.Contains(o.APInterventionID.Value)).Select(p => new PlanWorkImportVo.TypeGroup.Work
                    {
                        APInterventionID = p.APInterventionID,
                        TypeID = p.TypeID,
                        Sort = p.Sort,
                        WorkContent = p.WorkContent,
                        Requirement = p.Requirement,
                        WorkType = p.WorkType,
                        IsTemp = p.IsTemp,
                        PlanMonths = p.PlanMonths,
                        PrincipalName = p.PrincipalName,
                        Principals = p.Principals.Select(q => new AnnualPrincipal(
                            q.EmployeeID,
                            q.EmployeeGroupID,
                            q.EmployeeGroupName
                        )).ToArray()
                    }).ToArray()
                }).ToArray()
        }).ToList();
        return works;
    }

    public async Task<List<PlanWorkImportVo>> GetQuarterPlanWorkQuickRefVos(string[] apMainIDs, int quarter, int apInterventionID)
    {
        // 第一步：先获取所有符合条件的原始数据
        var quarterPlansData = await dbContext.QuarterPlanMainInfos
            .AsSplitQuery()
            .Include(m => m.QuarterPlanDetailInfos.Where(n => n.APInterventionID == apInterventionID && !n.IsTemp && n.DeleteFlag != "*"))
            .ThenInclude(m => m.Principals.Where(n => n.DeleteFlag != "*"))
            .Where(m => m.DeleteFlag != "*" && quarter == m.Quarter && apMainIDs.Contains(m.AnnualPlanMainID) && m.StatusCode == AnnualPlanEnums.PlanStatus.Published)
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var works = quarterPlansData.Select(m => new PlanWorkImportVo
        {
            DepartmentID = m.DepartmentID,
            Children = m.QuarterPlanDetailInfos
                .GroupBy(n => n.TypeID)
                .Select(n => new PlanWorkImportVo.TypeGroup
                {
                    TypeID = n.Key,
                    Children = n.Select(p => new PlanWorkImportVo.TypeGroup.Work
                    {
                        Key = p.QuarterPlanDetailID,
                        APInterventionID = p.APInterventionID,
                        TypeID = p.TypeID,
                        Sort = p.Sort,
                        WorkContent = p.WorkContent,
                        Requirement = p.Requirement,
                        WorkType = p.WorkType,
                        IsTemp = p.IsTemp,
                        PlanMonths = p.PlanMonths,
                        PrincipalName = p.PrincipalName,
                        Principals = p.Principals.Select(q => new AnnualPrincipal(
                            q.EmployeeID,
                            q.EmployeeGroupID,
                            q.EmployeeGroupName
                        )).ToArray()
                    }).ToArray()
                }).ToArray()
        }).ToList();
        return works;
    }

    public async Task<PlanWorkImportVo> GetQuarterPlanWorksForMonthlyPlan(string quarterPlanMainID, int month, int[] refInterventionIDs)
    {
        // 仅获取季度计划中计划执行月份等于当前的 + 在记录计划中无计划执行月份的（临时性工作）
        var main = await dbContext.QuarterPlanMainInfos
            .Where(m => m.DeleteFlag != "*" && m.QuarterPlanMainID == quarterPlanMainID && m.StatusCode == AnnualPlanEnums.PlanStatus.Published)
            .Include(m => m.QuarterPlanDetailInfos.Where(m => m.DeleteFlag != "*"))
            .FirstOrDefaultAsync();
        if (main is null)
        {
            return null;
        }
        var groups = main.QuarterPlanDetailInfos
            .Where(m => m.IsTemp || m.PlanMonths.Contains(month))
            .GroupBy(m => m.TypeID).ToList();
        // 按分类分组
        var view = new PlanWorkImportVo
        {
            DepartmentID = main.DepartmentID,
            Children = [.. groups.Select(m => new PlanWorkImportVo.TypeGroup
            {
                TypeID = m.Key,
                Children = [.. m.Select(n => new PlanWorkImportVo.TypeGroup.Work
                {
                    APInterventionID = n.APInterventionID,
                    TypeID = n.TypeID,
                    Sort = n.Sort,
                    WorkContent = n.WorkContent,
                    WorkType = n.WorkType,
                    IsTemp = n.IsTemp,
                    Requirement = n.Requirement,
                    PrincipalName = n.PrincipalName,
                    Principals = [.. n.Principals.Select(p => new AnnualPrincipal(
                        p.EmployeeID,
                        p.EmployeeGroupID,
                        p.EmployeeGroupName
                        ))],
                })]
            })]
        };
        return view;
    }
    /// <summary>
    /// 获取季度计划工作
    /// </summary>
    /// <param name="workIDs">工作ID集合</param>
    /// <returns></returns>
    public async Task<List<QuarterPlanDetailInfo>> GetQuarterPlanWorks(string[] workIDs)
    {
        return await dbContext.QuarterPlanDetailInfos.Where(m => m.DeleteFlag != "*" && workIDs.Contains(m.QuarterPlanDetailID)).ToListAsync();
    }
    /// <summary>
    /// 获取季度计划工作
    /// </summary>
    /// <param name="qpDetailID">主键</param>
    /// <returns></returns>
    public async Task<QuarterPlanDetailInfo> GetQuarterWork(string qpDetailID)
    {
        return await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanDetailID == qpDetailID).FirstOrDefaultAsync();
    }

    /// <summary>
    /// 获取某季度计划工作关联字典ID集合
    /// </summary>
    /// <param name="qpMainID">季度计划主键</param>
    /// <param name="quarter">季度</param>
    /// <returns></returns>
    public async Task<int[]> GetQpWorkInterventionIDs(string qpMainID, int quarter)
    {
        if (string.IsNullOrEmpty(qpMainID))
        {
            return [];
        }
        return await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == qpMainID && m.APInterventionID.HasValue && m.DeleteFlag != "*")
            .Select(m => m.APInterventionID.Value).Distinct().ToArrayAsync();
    }
    /// <summary>
    /// 获取季度计划工作关联字典ID集合
    /// </summary>
    /// <param name="year">年</param>
    /// <param name="departmentIDs">病区集合</param>
    /// <returns></returns>
    public async Task<List<QuarterPlanPreviewVo>> GetQuarterPlanQueryViews(int year, IEnumerable<int> departmentIDs)
    {
        return await dbContext.QuarterPlanMainInfos.Where(m => m.Year == year && departmentIDs.Contains(m.DepartmentID) && m.DeleteFlag != "*")
            .Select(m => new QuarterPlanPreviewVo
            {
                Key = m.QuarterPlanMainID,
                StatusCode = m.StatusCode,
                Quarter = m.Quarter,
                DepartmentID = m.DepartmentID,
                Planner = m.AddEmployeeID,
                ModifyDateTime = m.ModifyDateTime,
            }).ToListAsync();
    }
    /// <summary>
    /// 获取导出视图
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    public async Task<QuarterPlanExportView> GetExportView(string quarterPlanMainID)
    {
        // 第一步：先获取所有符合条件的原始数据
        var quarterPlanDetailsData = await dbContext.QuarterPlanDetailInfos
            .Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var data = quarterPlanDetailsData
            .GroupBy(m => m.TypeID)
            .Select(m => new QuarterPlanExportView.PlanType
            {
                TypeID = m.Key,
                PlanWorks = m.Select(n => new QuarterPlanExportView.PlanType.PlanWork
                {
                    WorkType = n.WorkType,
                    WorkContent = n.WorkContent,
                    Requirement = n.Requirement,
                }).ToArray()
            }).ToArray();
        return new QuarterPlanExportView
        {
            QuarterPlanMainID = quarterPlanMainID,
            PlanTypes = data
        };
    }
    /// <summary>
    /// 获取季度计划预览视图
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划ID</param>
    /// <returns></returns>
    public async Task<QuarterPlanAndWorksPreviewVo> GetQuarterPlanPreview(string quarterPlanMainID)
    {
        // 第一步：先获取所有符合条件的原始数据
        var quarterPlanDetailsData = await dbContext.QuarterPlanDetailInfos
            .Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
            .ToListAsync();

        // 第二步：在内存中进行 GroupBy 操作和数据转换
        var data = quarterPlanDetailsData
            .GroupBy(m => m.TypeID)
            .Select(m => new QuarterPlanAndWorksPreviewVo.PlanType
            {
                TypeId = m.Key,
                PlanWorks = m.Select(n => new QuarterPlanAndWorksPreviewVo.PlanType.PlanWork
                {
                    WorkContent = n.WorkContent,
                    Requirement = n.Requirement,
                    PrincipalName = n.PrincipalName,
                    WorkType = n.WorkType
                }).ToArray()
            }).ToArray();
        return new QuarterPlanAndWorksPreviewVo
        {
            QuarterPlanMainID = quarterPlanMainID,
            PlanTypes = data
        };
    }
    /// <summary>
    /// 获取大于等于给定Sort值的工作
    /// </summary>
    /// <param name="typeIDAndSort">分类及对应Sort</param>
    /// <returns></returns>
    public async Task<List<QuarterPlanDetailInfo>> GetGteMinSortQuarterWorksByTypeID(Dictionary<int, int> typeIDAndSort)
    {
        var validTypeIDs = typeIDAndSort.Keys.ToList();
        
        var allRecords = await dbContext.QuarterPlanDetailInfos
            .Where(m => m.DeleteFlag != "*" && validTypeIDs.Contains(m.TypeID))
            .ToListAsync();
        
        return allRecords.Where(m => m.Sort >= typeIDAndSort[m.TypeID]).ToList();
    }
    /// <summary>
    /// 获取季度计划某分类下的工作
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划</param>
    /// <param name="typeID">分类ID</param>
    /// <returns></returns>
    public async Task<List<QuarterPlanDetailInfo>> GetWorksByQuarterPlanMainIDAndTypeID(string quarterPlanMainID, int typeID)
    {
        return await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.TypeID == typeID && m.DeleteFlag != "*")
            .ToListAsync();
    }
    /// <summary>
    /// 获取大于给定Sort值的工作
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <param name="typeID">分类ID</param>
    /// <param name="sort">序号</param>
    /// <returns></returns>
    public async Task<List<QuarterPlanDetailInfo>> GetGtSortWorks(string quarterPlanMainID, int typeID, int sort)
    {
        return await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.TypeID == typeID && m.Sort > sort && m.DeleteFlag != "*")
            .ToListAsync();
    }
    /// <summary>
    /// 获取每个分类的最大Sort值
    /// </summary>
    /// <param name="quarterPlanMainID">季度计划主表ID</param>
    /// <returns></returns>
    public async Task<Dictionary<int, int>> GetTypeIDAndMaxSort(string quarterPlanMainID)
    {
        return await dbContext.QuarterPlanDetailInfos.Where(m => m.QuarterPlanMainID == quarterPlanMainID && m.DeleteFlag != "*")
            .GroupBy(m => m.TypeID)
            .ToDictionaryAsync(m => m.Key, m => m.MaxBy(n => n.Sort).Sort ?? 0);
    }
}
