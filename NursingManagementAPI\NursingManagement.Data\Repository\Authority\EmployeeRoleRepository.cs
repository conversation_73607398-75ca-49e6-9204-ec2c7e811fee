﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Common;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class EmployeeRoleRepository : IEmployeeRoleRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext = null;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IRedisService _redisService;

        public EmployeeRoleRepository(
            NursingManagementDbContext db,
            IRedisService redisService, SessionCommonServer sessionCommonServer)
        {
            _nursingManagementDbContext = db;
            _redisService = redisService;
            _sessionCommonServer = sessionCommonServer;
        }

        public async Task<object> GetCacheAsync(dynamic query = null)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            var datas = await _redisService.GetOrCreateAsync(key, 0, hospitalID, async entry =>
            {
                var result = await _nursingManagementDbContext.EmployeeRoleInfos.Where(m => m.HospitalID == hospitalID && m.DeleteFlag != "*").ToListAsync();
                return result;
            });
            return datas;
        }

        public async Task UpdateCache()
        {
            string key = GetCacheType();
            await _redisService.Remove(key);
        }

        public bool HasCache(string cacheType)
        {
            return GetCacheType() == cacheType;
        }

        public string GetCacheType()
        {
            return CacheType.EmployeeRole.GetKey(_sessionCommonServer);
        }

        public async Task<List<int>> GetRolesByEmployeeID(string employeeID)
        {
            var employeeRoles = await GetCacheAsync() as List<EmployeeRoleInfo>;
            if (employeeRoles.Count <= 0)
            {
                return new List<int>();
            }
            return employeeRoles.Where(m => m.EmployeeID == employeeID).Select(m => m.AuthorityRoleID).ToList();
        }

        public async Task<List<EmployeeRoleInfo>> GetEmployeeRoleInfoByEmployeeID(string employeeID)
        {
            var employeeRoles = await GetCacheAsync() as List<EmployeeRoleInfo>;
            if (employeeRoles.Count <= 0)
            {
                return new List<EmployeeRoleInfo>();
            }
            return employeeRoles.Where(m => m.EmployeeID == employeeID).ToList();
        }

        /// <summary>
        /// 获取病人所有的角色（包含删除的）
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<EmployeeRoleInfo>> GetAllEmployeeRoleByEmployeeIDAsync(string employeeID)
        {
            string key = GetCacheType();
            (var hospitalID, var language) = _sessionCommonServer.GetParamsByKey(key);
            return await _nursingManagementDbContext.EmployeeRoleInfos.Where(m => m.HospitalID == hospitalID && m.EmployeeID == employeeID).ToListAsync();
        }
        /// <summary>
        /// 从一组人员工号中筛选出指定角色的工号集合
        /// </summary>
        /// <param name="employeeIDs"></param>
        /// <param name="authorityRoleID">角色ID</param>
        /// <returns></returns>
        public async Task<List<string>> FilterEmployeeByRole(List<string> employeeIDs, int authorityRoleID)
        {
            var employeeRoles = await GetCacheAsync() as List<EmployeeRoleInfo>;
        
            return employeeRoles.Where(m=>m.AuthorityRoleID == authorityRoleID && employeeIDs.Contains(m.EmployeeID)).Select(m => m.EmployeeID).ToList();
        }
    }
}
