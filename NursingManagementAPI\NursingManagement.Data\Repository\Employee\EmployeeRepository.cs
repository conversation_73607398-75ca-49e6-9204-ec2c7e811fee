﻿using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    public class EmployeeRepository : IEmployeeRepository
    {
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly ICapabilityLevelRepository _capabilityLevelRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;

        public EmployeeRepository(
             IEmployeePersonalDataRepository employeePersonalDataRepository
            , IEmployeeStaffDataRepository employeeStaffDataRepository
            , ICapabilityLevelRepository capabilityLevelRepository
        )
        {
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _capabilityLevelRepository = capabilityLevelRepository;
        }

        public async Task<List<EmployeeForSchedulingView>> GetEmployeeDataByDepartmentID(int departmentID, bool includingResignedFlag = false)
        {
            var employeeStaffDataInfos = await _employeeStaffDataRepository.GetEmployeeStaffDataView();
            var employeePersonalDataInfos = await _employeePersonalDataRepository.GetEmployeePersonalDataView();
            employeeStaffDataInfos = employeeStaffDataInfos.Where(m => m.DepartmentID == departmentID && !string.IsNullOrEmpty(m.FileID) && !m.FileID.StartsWith("LC"))
                                                           .IfWhere(!includingResignedFlag, m => m.StatusCode != "0").ToList();
            // 这里使用左连接
            return (from a in employeeStaffDataInfos
                    from b in employeePersonalDataInfos.Where(m => m.EmployeeID == a.EmployeeID)
                    select new EmployeeForSchedulingView
                    {
                        EmployeeID = a.EmployeeID,
                        EmployeeName = b.EmployeeName,
                        CapabilityLevelID = a.CapabilityLevelID,
                        TeacherEmployeeID = a.TeacherEmployeeID,
                        StaffStatusCode = a.StatusCode
                    }).ToList();
        }

        public async Task<List<EmployeeForSchedulingView>> GetEmployeeDataByEmployeeIDs(List<string> employeeIDs)
        {
            var employeeStaffDataInfos = await _employeeStaffDataRepository.GetEmployeeStaffDataView();
            var employeePersonalDataInfos = await _employeePersonalDataRepository.GetEmployeePersonalDataView();
            employeeStaffDataInfos = employeeStaffDataInfos.Where(m => employeeIDs.Contains(m.EmployeeID)).ToList();
            // 这里使用左连接
            return (from a in employeeStaffDataInfos
                    from b in employeePersonalDataInfos.Where(m => m.EmployeeID == a.EmployeeID)
                    select new EmployeeForSchedulingView
                    {
                        EmployeeID = a.EmployeeID,
                        EmployeeName = b.EmployeeName,
                        CapabilityLevelID = a.CapabilityLevelID,
                        TeacherEmployeeID = a.TeacherEmployeeID,
                        StaffStatusCode = a.StatusCode
                    }).ToList();
        }

        /// <summary>
        /// 根据部门ID获取所有员工的工号
        /// </summary>
        /// <param name="departmentID">部门ID（可空）</param>
        /// <returns></returns>
        public async Task<List<EmployeeRoleView>> GetEmployeeIDsByDepartmentIDAsync(int? departmentID)
        {
            var employeeStaffDataInfos = await _employeeStaffDataRepository.GetEmployeeStaffDataView();
            var employeePersonalDataInfos = await _employeePersonalDataRepository.GetEmployeePersonalDataView();
            if (departmentID.HasValue)
            {
                employeeStaffDataInfos = employeeStaffDataInfos.Where(m => m.DepartmentID == departmentID).ToList();
            }
            return (from a in employeeStaffDataInfos
                    from b in employeePersonalDataInfos.Where(m => m.EmployeeID == a.EmployeeID)
                    select new EmployeeRoleView
                    {
                        EmployeeID = a.EmployeeID,
                        EmployeeName = b.EmployeeName,
                    }).Distinct().ToList();
        }
    }
}
