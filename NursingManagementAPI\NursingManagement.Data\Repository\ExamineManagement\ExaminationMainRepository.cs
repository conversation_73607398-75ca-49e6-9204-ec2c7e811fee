﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ExaminationMainRepository : IExaminationMainRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ExaminationMainRepository(
              NursingManagementDbContext nursingManagementDbContex
            )
        {
            _nursingManagementDbContext = nursingManagementDbContex;
        }

        /// <summary>
        /// 根据考核记录ID获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<List<ExaminationMainInfo>> GetListByRecordID(string recordID)
        {
            return await _nursingManagementDbContext.ExaminationMainInfos.Where(m => m.ExaminationRecordID == recordID && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据考核记录ID集合获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<ExaminationMainInfo>> GetListByRecordIDList(List<string> recordIDs)
        {
            return await _nursingManagementDbContext.ExaminationMainInfos.Where(m => recordIDs.Any(n => n == m.ExaminationRecordID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据考核主表ID获取数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<ExaminationMainInfo> GetDataByMainID(string mainID)
        {
            return await _nursingManagementDbContext.ExaminationMainInfos.FirstOrDefaultAsync(m => m.ExaminationMainID == mainID);
        }

        /// <summary>
        /// 根据被考核人和考核状态获取数据
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="statusCodes"></param>
        /// <param name="examinationType">考核类型</param>
        /// <returns></returns>
        public async Task<List<ExaminationMainInfo>> GetListAsNoTrackByEmployeeIDAndType(string employeeID, List<string> statusCodes, string examinationType)
        {
            var query = from main in _nursingManagementDbContext.ExaminationMainInfos.Where(m => m.EmployeeID == employeeID && statusCodes.Any(n => n == m.StatusCode) && m.DeleteFlag != "*")
                        join record in _nursingManagementDbContext.ExaminationRecordInfos.Where(m => m.DeleteFlag != "*" && m.Type == examinationType)
                        on main.ExaminationRecordID equals record.ExaminationRecordID
                        select main;
            return await query.AsNoTracking().ToListAsync();
        }

        public async Task<List<ExaminationMainInfo>> GetListByExaminer(string examineEmployeeID, List<string> statusCodes, string examineType)
        {
            IQueryable<ExaminationMainInfo> query;
            if (statusCodes == null || statusCodes.Count <= 0)
            {
                query = from m in _nursingManagementDbContext.ExaminationMainInfos.Where(m => m.DeleteFlag != "*")
                        join n in _nursingManagementDbContext.ExaminerInfos.Where(m => m.DeleteFlag != "*" && m.EmployeeID == examineEmployeeID && m.SourceType == "ExaminationRecord")
                        on m.ExaminationRecordID equals n.SourceID
                        join r in _nursingManagementDbContext.ExaminationRecordInfos.Where(m => m.DeleteFlag != "*" && m.Type == examineType)
                        on m.ExaminationRecordID equals r.ExaminationRecordID
                        select m;
                return await query.ToListAsync();
            }
            query = from m in _nursingManagementDbContext.ExaminationMainInfos.Where(m => m.DeleteFlag != "*" && statusCodes.Any(n => n == m.StatusCode))
                    join n in _nursingManagementDbContext.ExaminerInfos.Where(m => m.DeleteFlag != "*" && m.EmployeeID == examineEmployeeID && m.SourceType == "ExaminationRecord")
                    on m.ExaminationRecordID equals n.SourceID
                    join r in _nursingManagementDbContext.ExaminationRecordInfos.Where(m => m.DeleteFlag != "*" && m.Type == examineType)
                    on m.ExaminationRecordID equals r.ExaminationRecordID
                    select m;
            return await query.ToListAsync();
        }

        /// <summary>
        /// 根据考核记录ID和人员ID获取人员考核记录
        /// </summary>
        /// <param name="recordID">考核主记录ID</param>
        /// <param name="employeeID">人员工号</param>
        /// <returns></returns>
        public async Task<ExaminationMainInfo> GetEmployeeExaminationMainInfo(string recordID, string employeeID)
        {
            return await _nursingManagementDbContext.ExaminationMainInfos.Where(m => m.ExaminationRecordID == recordID && m.EmployeeID == employeeID && m.DeleteFlag != "*").OrderByDescending(m=>m.AddDateTime).FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据考核类别筛选数据
        /// </summary>
        /// <param name="employeeID">考核人工号</param>
        /// <param name="statusCodes">考核状态</param>
        /// <param name="examineType">考核类型</param>
        /// <returns></returns>
        public async Task<List<ExaminationMainInfo>> GetListByType(string employeeID, List<string> statusCodes, string examineType)
        {
            if (statusCodes == null || statusCodes.Count <= 0)
            {
                return await (from main in _nursingManagementDbContext.ExaminationMainInfos
                              join record in _nursingManagementDbContext.ExaminationRecordInfos on main.ExaminationRecordID equals record.ExaminationRecordID
                              where record.Type == examineType && main.EmployeeID == employeeID && main.DeleteFlag != "*"
                              select main).ToListAsync();
            }
            return await (from main in _nursingManagementDbContext.ExaminationMainInfos
                          join record in _nursingManagementDbContext.ExaminationRecordInfos on main.ExaminationRecordID equals record.ExaminationRecordID
                          where record.Type == examineType && main.EmployeeID == employeeID && statusCodes.Any(n => n == main.StatusCode) && main.DeleteFlag != "*"
                          select main).ToListAsync();
        }

        /// <summary>
        /// 根据时间范围获取数据
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <param name="examineEmployeeID">主考人</param>
        /// <returns></returns>
        public async Task<List<ExaminationMainInfo>> GetListByDate(DateTime startDate, DateTime endDate)
        {
            // 确保仅比较日期部分
            startDate = startDate.Date;
            endDate = endDate.Date;
            return await _nursingManagementDbContext.ExaminationMainInfos
                .Where(m => m.DeleteFlag != "*" &&
                    // 情况1：两个日期均为空
                    (!m.StartDateTime.HasValue && !m.EndDateTime.HasValue) ||
                    // 情况2：开始时间为空，且结束日期在查询范围内
                    (!m.StartDateTime.HasValue && m.EndDateTime.HasValue && m.EndDateTime.Value.Date >= startDate) ||
                    // 情况3：结束时间为空，且开始日期在查询范围内
                    (!m.EndDateTime.HasValue && m.StartDateTime.HasValue && m.StartDateTime.Value.Date <= endDate) ||
                    // 情况4：两个日期均存在，且时间段有重叠
                    (m.StartDateTime.HasValue && m.EndDateTime.HasValue &&
                     m.StartDateTime.Value.Date <= endDate &&
                     m.EndDateTime.Value.Date >= startDate)).ToListAsync();
        }

        public async Task<List<ExaminationMainInfo>> GetListAsNoTrackByRecordIDAndStatus(List<string> recordIDs, List<string> statusCodes)
        {
            return await _nursingManagementDbContext.ExaminationMainInfos.AsNoTracking().
                 Where(m => recordIDs.Contains(m.ExaminationRecordID) && m.DeleteFlag != "*" && statusCodes.Any(n => n == m.StatusCode)).ToListAsync();
        }
       
        public async Task<List<ExaminationMainInfo>> GetNotRetakeMainByRecordIDAndEmployeeIDs(string recordID, List<string> employeeIDs)
        {
            return await _nursingManagementDbContext.ExaminationMainInfos.Where(m => m.ExaminationRecordID == recordID && employeeIDs.Contains(m.EmployeeID) && m.DeleteFlag != "*" && !m.RetakeFlag).ToListAsync();
        }
    }
}
