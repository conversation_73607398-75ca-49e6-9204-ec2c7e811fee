﻿using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;

namespace NursingManagement.Data.Repository
{
    public class ExaminationRecordRepository : IExaminationRecordRepository
    {
        private readonly NursingManagementDbContext _nursingManagementDbContext;

        public ExaminationRecordRepository(
              NursingManagementDbContext nursingManagementDbContext
            )
        {
            _nursingManagementDbContext = nursingManagementDbContext;
        }

        /// <summary>
        /// 根据考核开始结束时间和考核类型获取数据
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="type"></param>
        /// <param name="departmentIDs">部门ID集合</param>
        /// <returns></returns>
        public async Task<List<ExaminationRecordInfo>> GetListByDate(DateTime startDate, DateTime endDate, string type, List<int> departmentIDs)
        {
            startDate = startDate.Date;
            endDate = endDate.Date.AddDays(1).AddTicks(-1);
            var query = _nursingManagementDbContext.ExaminationRecordInfos
                .Where(m => m.DeleteFlag != "*" && m.StartDateTime <= endDate && m.EndDateTime >= startDate);
            if (!string.IsNullOrEmpty(type))
            {
                query = query.Where(m => m.Type == type);
            }
            if (departmentIDs != null && departmentIDs.Count > 0)
            {
                query = query.Where(m => m.DepartmentID == null || departmentIDs.Contains(m.DepartmentID.Value));
            }
            return await query.ToListAsync();
        }

        /// <summary>
        /// 根据主记录ID获取数据
        /// </summary>
        /// <param name="recordID"></param>
        /// <returns></returns>
        public async Task<ExaminationRecordInfo> GetDataByID(string recordID)
        {
            return await _nursingManagementDbContext.ExaminationRecordInfos.FirstOrDefaultAsync(m => m.ExaminationRecordID == recordID && m.DeleteFlag != "*");
        }

        /// <summary>
        /// 根据主记录ID集合获取数据
        /// </summary>
        /// <param name="recordIDs"></param>
        /// <returns></returns>
        public async Task<List<ExaminationRecordInfo>> GetListByIDs(List<string> recordIDs)
        {
            return await _nursingManagementDbContext.ExaminationRecordInfos.Where(m => recordIDs.Any(n => n == m.ExaminationRecordID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据试卷ID获取数据
        /// </summary>
        /// <param name="paperMainIDs"></param>
        /// <returns></returns>
        public async Task<List<ExaminationRecordInfo>> GetListByPaperMainIDs(List<string> paperMainIDs)
        {
            return await _nursingManagementDbContext.ExaminationRecordInfos.Where(m => !string.IsNullOrEmpty(m.ExaminationPaperMainID) && paperMainIDs.Any(n => n == m.ExaminationPaperMainID) && m.DeleteFlag != "*").ToListAsync();
        }

        /// <summary>
        /// 根据主键获取考核记录ID和考核名称的关系dict
        /// </summary>
        /// <param name="recordIDs">考核主记录集合</param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetRecordIDAndNameDictByIDs(List<string> recordIDs)
        {
            return await _nursingManagementDbContext.ExaminationRecordInfos.Where(m => recordIDs.Any(n => n == m.ExaminationRecordID) && m.DeleteFlag != "*")
                .ToDictionaryAsync(m => m.ExaminationRecordID, n => n.ExaminationName);
        }

        /// <summary>
        /// 根据考核类型和主考人获取所有相关的考核记录主键ID
        /// </summary>
        /// <param name="examinationType">考核类型</param>
        /// <param name="examinerID">监考人</param>
        /// <returns></returns>
        public async Task<List<string>> GetRecordIDsByTypeAndExaminer(string examinationType, string examinerID)
        {
            var query = from record in _nursingManagementDbContext.ExaminationRecordInfos.Where(m => m.Type == examinationType && m.DeleteFlag != "*")
                        join examiner in _nursingManagementDbContext.ExaminerInfos.Where(m => m.DeleteFlag != "*" && m.EmployeeID == examinerID && m.SourceType == "ExaminationRecord")
                        on record.ExaminationRecordID equals examiner.SourceID
                        select record.ExaminationRecordID;
            return await query.ToListAsync();
        }

        public async Task<List<ExaminationRecordInfo>> GetNotFinishedPracticalRecords()
        {
            return await _nursingManagementDbContext.ExaminationRecordInfos.Where(
                    m => m.PublishFlag && m.EndDateTime > DateTime.Now.Date
                    && m.Type == "2"
                ).ToListAsync();
        }
        /// <summary>
        /// 根据考核类型和主考人获取所有相关的考核记录
        /// </summary>
        /// <param name="examinationType">考核类型</param>
        /// <param name="examinerID">监考人</param>
        /// <returns></returns>
        public async Task<List<ExaminationRecordInfo>> GetListByTypeAndExaminerAsNoTrack(string examinationType, string examinerID)
        {
            var query = from record in _nursingManagementDbContext.ExaminationRecordInfos.Where(m => m.Type == examinationType && m.DeleteFlag != "*")
                        join examiner in _nursingManagementDbContext.ExaminerInfos.Where(m => m.DeleteFlag != "*" && m.EmployeeID == examinerID && m.SourceType == "ExaminationRecord")
                        on record.ExaminationRecordID equals examiner.SourceID
                        select record;
            return await query.ToListAsync();
        }
    }
}
