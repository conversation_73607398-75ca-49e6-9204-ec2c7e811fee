using Microsoft.EntityFrameworkCore;
using NursingManagement.Data.Context;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.ViewModels;

namespace NursingManagement.Data.Repository
{
    /// <summary>
    /// 监考安排仓储实现类
    /// </summary>
    public class ExaminerScheduleRepository : IExaminerScheduleRepository
    {
        private readonly NursingManagementDbContext _context;

        public ExaminerScheduleRepository(NursingManagementDbContext context)
        {
            _context = context;
        }

        /// <summary>
        /// 监考计划已被预约
        /// </summary>
        private static readonly string EXAMINER_SCHEDULE_STATUS_1 = "1";
        /// <summary>
        /// 完成预约
        /// </summary>
        private const string COMPLETE_APPOINTMENT = "1";
        /// <summary>
        /// 根据主键获取监考计划
        /// </summary>
        /// <param name="examinerScheduleID"></param>
        /// <returns></returns>
        public async Task<ExaminerScheduleInfo> GetByIdAsync(string examinerScheduleID)
        {
            return await _context.ExaminerScheduleInfos
                .Where(x => x.ExaminerScheduleID == examinerScheduleID && x.DeleteFlag != "*")
                .FirstOrDefaultAsync();
        }

        /// <summary>
        /// 根据主键ID集合获取监考计划
        /// </summary>
        /// <param name="examinerScheduleIDs">监考记录主键ID集合</param>
        /// <returns></returns>
        public async Task<List<ExaminerScheduleInfo>> GetListByIdsAsync(List<string> examinerScheduleIDs)
        {
            return await _context.ExaminerScheduleInfos
                .Where(x => examinerScheduleIDs.Contains(x.ExaminerScheduleID) && x.DeleteFlag != "*")
                .ToListAsync();
        }

        /// <summary>
        /// 根据考核计划获取监考计划列表
        /// </summary>
        /// <param name="examinationRecordID">考核计划ID</param>
        /// <param name="scheduleDate">监考日期</param>
        /// <returns></returns>
        public async Task<List<ExaminerScheduleInfo>> GetListAsync(string examinationRecordID, DateTime scheduleDate)
        {
            return await (from schedule in _context.ExaminerScheduleInfos.Where(m => m.ScheduleDate.Date >= scheduleDate.Date && m.DeleteFlag != "*")
                          join item in _context.ExaminerScheduleItemInfos.Where(m => m.ExaminationRecordID == examinationRecordID && m.DeleteFlag != "*")
                          on schedule.ExaminerScheduleID equals item.ExaminerScheduleID
                          orderby schedule.ScheduleDate
                          select schedule).ToListAsync();
        }

        /// <summary>
        /// 根据考核计划和时间范围获取监考计划列表
        /// </summary>
        /// <param name="startDate">开始时间</param>
        /// <param name="endDate">结束时间</param>
        /// <returns></returns>
        public async Task<List<ExaminerScheduleInfo>> GetListByDateRange(DateTime startDate, DateTime endDate)
        {
            return await _context.ExaminerScheduleInfos.Where(x => x.ScheduleDate.Date >= startDate.Date && x.ScheduleDate.Date <= endDate.Date && x.DeleteFlag != "*").ToListAsync();
        }
        /// <summary>
        /// 根据考核记录获取每一个预约人员的考核位置信息
        /// </summary>
        /// <param name="examinationRecordIDs"></param>
        /// <returns></returns>
        public async Task<List<ExamineLocationView>> GetExamineLocationListAsync(List<string> examinationRecordIDs)
        {
            return await(from appointment in _context.ExaminationAppointmentInfos.Where(m=>m.StatusCode == COMPLETE_APPOINTMENT && m.DeleteFlag != "*")
                         join item in _context.ExaminerScheduleItemInfos.Where(m => examinationRecordIDs.Contains(m.ExaminationRecordID) && m.DeleteFlag != "*")
                         on appointment.ExaminationRecordID equals item.ExaminationRecordID
                         join schedule in _context.ExaminerScheduleInfos.Where(m => m.StatusCode == EXAMINER_SCHEDULE_STATUS_1 && m.DeleteFlag != "*")
                         on item.ExaminerScheduleID equals schedule.ExaminerScheduleID
                         select new ExamineLocationView
                         { 
                            ExaminationRecordID =item.ExaminationRecordID,
                            EmployeeID = appointment.EmployeeID,
                            Location = schedule.Location
                         }).ToListAsync();
        }
    }
}
