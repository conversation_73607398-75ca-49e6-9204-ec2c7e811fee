﻿using System.Text.Json.Serialization;
using static NursingManagement.Models.AnnualPlanEnums;

namespace NursingManagement.Models
{
    [Serializable]
    public class QuarterPlanDetailInfo : MutiModifyInfo
    {
        /// <summary>
        /// 季度计划明细表ID，主键
        /// </summary>
        public string QuarterPlanDetailID { get; set; }

        /// <summary>
        /// 季度计划主表ID，外键
        /// </summary>
        public string QuarterPlanMainID { get; set; }

        /// <summary>
        /// 分类字典ID
        /// </summary>
        public int TypeID { get; set; }

        /// <summary>
        /// 参考执行项目字典ID，取自APInterventionMain；为空表示无参考
        /// </summary>
        public int? APInterventionID { get; set; }

        /// <summary>
        /// 分类内重点工作排序序号
        /// </summary>
        public int? Sort { get; set; }

        /// <summary>
        /// 工作类型
        /// </summary>
        public WorkType WorkType { get; set; }

        /// <summary>
        /// 临时性工作标记
        /// </summary>
        public bool IsTemp { get; set; }

        /// <summary>
        /// 工作重点
        /// </summary>
        public string WorkContent { get; set; }

        /// <summary>
        /// 要求
        /// </summary>
        public string Requirement { get; set; }

        /// <summary>
        /// 负责人分组名称
        /// </summary>
        public string PrincipalName { get; set; }

        /// <summary>
        /// 计划执行月份
        /// </summary>
        public List<int> PlanMonths { get; set; } = [];

        /// <summary>
        /// 导航属性
        /// </summary>
        public QuarterPlanMainInfo QuarterPlanMainInfo { get; set; }

        /// <summary>
        /// 导航属性 - 负责人列表
        /// </summary>
        [JsonIgnore]
        public ICollection<QuarterPlanDetailPrincipalInfo> Principals { get; set; } = [];
    }
}
