using System.Text.Json.Serialization;

namespace NursingManagement.Models;

/// <summary>
/// 季度计划工作负责人表
/// </summary>
[Serializable]
public class QuarterPlanDetailPrincipalInfo
{
    /// <summary>
    /// 自增主键
    /// </summary>
    public int QuarterPlanDetailPrincipalID { get; set; }
    /// <summary>
    /// 外键，季度计划明细表ID
    /// </summary>
    public string QuarterPlanDetailID { get; set; }
    /// <summary>
    /// 分组ID，单用户为空
    /// </summary>
    public int? EmployeeGroupID { get; set; }
    /// <summary>
    /// 分组名称，单用户为空
    /// </summary>
    public string EmployeeGroupName { get; set; }
    /// <summary>
    /// 人员工号
    /// </summary>
    public string EmployeeID { get; set; }
    /// <summary>
    /// 序号
    /// </summary>
    public int Sort { get; set; }
    /// <summary>
    /// 新增时间
    /// </summary>
    public DateTime AddDateTime { get; set; } = DateTime.Now;
    /// <summary>
    /// 修改时间
    /// </summary>
    public DateTime ModifyDateTime { get; set; } = DateTime.Now;
    /// <summary>
    /// 删除标记
    /// </summary>
    public string DeleteFlag { get; set; } = "";
    /// <summary>
    /// 导航属性
    /// </summary>
    [JsonIgnore]
    public QuarterPlanDetailInfo QuarterPlanDetailInfo { get; set; }
}