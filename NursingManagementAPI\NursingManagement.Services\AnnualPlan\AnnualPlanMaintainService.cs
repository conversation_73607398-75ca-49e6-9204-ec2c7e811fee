﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    /// <summary>
    /// 年度计划-计划维护Service
    /// </summary>
    public class AnnualPlanMaintainService : IAnnualPlanMaintainService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAnnualPlanTypeListRepository _typeListRepository;
        private readonly IAnnualGoalListRepository _goalListRepository;
        private readonly IAnnualIndicatorListRepository _indicatorListRepository;
        private readonly IAnnualPlanMainRepository _mainRepository;
        private readonly IAnnualPlanMainGoalRepository _mainGoalRepository;
        private readonly IAnnualPlanGoalGroupRepository _annualPlanGoalGroupRepository;
        private readonly IAnnualPlanIndicatorDetailRepository _annualPlanIndicatorDetailRepository;
        private readonly IAnnualPlanProjectDetailRepository _annualPlanProjectDetailRepository;
        private readonly IAnnualPlanInterventionService _annualPlanInterventionService;
        private readonly IQuarterPlanMaintainRepository _quarterPlanMaintainRepository;
        private readonly IMonthlyPlanMaintainRepository _monthlyPlanMaintainRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IAnnualSettingService _annualIndicatorListService;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDictionaryService _dictionaryService;
        private readonly IAnnualPlanInterventionMainRepository _annualPlanInterventionMainRepository;
        private readonly IAnnualPlanInterventionDetailRepository _interventionDetailRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IFileService _fileService;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeeToDepartmentRepository _employeeToDepartmentRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;

        public AnnualPlanMaintainService(
            IAnnualPlanTypeListRepository annualPlanTypeListRepository,
            IAnnualGoalListRepository annualGoalListRepository,
            IAnnualIndicatorListRepository annualIndicatorListRepository,
            IAnnualPlanMainRepository mainRepository,
            IAnnualPlanMainGoalRepository mainGoalRepository,
            IAnnualPlanGoalGroupRepository annualPlanGoalGroupRepository,
            IAnnualPlanIndicatorDetailRepository annualPlanIndicatorDetailRepository,
            IAnnualPlanProjectDetailRepository annualPlanProjectDetailRepository,
            IAnnualPlanInterventionService annualPlanInterventionService,
            IQuarterPlanMaintainRepository quarterPlanMaintainRepository,
            IMonthlyPlanMaintainRepository monthlyPlanMaintainRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IAnnualSettingService annualIndicatorListService,
            IUnitOfWork unitOfWork,
            IDictionaryService dictionaryService,
            IAnnualPlanInterventionMainRepository annualPlanInterventionMainRepository,
            IAppConfigSettingRepository appConfigSettingRepository,
            IFileService fileService,
            IDepartmentListRepository departmentListRepository,
            IEmployeeToDepartmentRepository employeeToDepartmentRepository,
            ISettingDictionaryService settingDictionaryService
            )
        {
            _typeListRepository = annualPlanTypeListRepository;
            _goalListRepository = annualGoalListRepository;
            _indicatorListRepository = annualIndicatorListRepository;
            _mainRepository = mainRepository;
            _mainGoalRepository = mainGoalRepository;
            _annualPlanGoalGroupRepository = annualPlanGoalGroupRepository;
            _annualPlanIndicatorDetailRepository = annualPlanIndicatorDetailRepository;
            _annualPlanProjectDetailRepository = annualPlanProjectDetailRepository;
            _annualPlanInterventionService = annualPlanInterventionService;
            _quarterPlanMaintainRepository = quarterPlanMaintainRepository;
            _monthlyPlanMaintainRepository = monthlyPlanMaintainRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _annualIndicatorListService = annualIndicatorListService;
            _unitOfWork = unitOfWork;
            _dictionaryService = dictionaryService;
            _annualPlanInterventionMainRepository = annualPlanInterventionMainRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _fileService = fileService;
            _departmentListRepository = departmentListRepository;
            _employeeToDepartmentRepository = employeeToDepartmentRepository;
            _settingDictionaryService = settingDictionaryService;
        }

        #region 常量

        /// <summary>
        /// 图标类型
        /// </summary>
        private const string ICON_MODELTYPE_ANNUALPLAN = "AnnualPlan";

        /// <summary>
        /// 护理部ID
        /// </summary>
        private const int DEPARTMENT_ID_405 = 405;

        #endregion

        #region 主表

        /// <summary>
        /// 查询本人及上下级已制定的年度计划
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<AnnualPlanQueryView[]> GetBrowseAPViews(int year, string employeeID)
        {
            var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var mainDepartment = employeeToDepartments.FirstOrDefault(m => m.IsMainDepartment ?? false)?.DepartmentID;
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var departIDs = await GetDepartmentIDs(employeeToDepartments, departmentList);

            var queryViews = await _mainRepository.GetAnnualPlanQueryViews(year, departIDs);
            foreach (var queryView in queryViews)
            {
                queryView.IsMainDepartment = queryView.DepartmentID == mainDepartment;
                queryView.DepartmentName = departmentList.Find(m => m.DepartmentID == queryView.DepartmentID)?.LocalShowName;
                queryView.PlannerName = await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(queryView.Planner, m => m.EmployeeName);
            }
            return queryViews.OrderBy(m => departmentList.Find(n => n.DepartmentID == m.DepartmentID)?.Level).ToArray();
        }

        /// <summary>
        /// 查询本人部门及上下级部门
        /// </summary>
        /// <param name="year">年份</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<List<CascaderView<int>>> GetBrowseAPDepartments(int year, string employeeID)
        {
            var organizationTypes = await _settingDictionaryService.GetOrganizationTypes();
            var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var departIDs = await GetDepartmentIDs(employeeToDepartments, departmentList);
            var options = departmentList
                .Where(m => departIDs.Contains(m.DepartmentID))
                .GroupBy(m => m.OrganizationType)
                .Select(m => new CascaderView<int>
                {
                    Label = organizationTypes[m.Key],
                    Value = Convert.ToInt32(m.Key.Trim()),
                    Disabled = false,
                    Children = m.Select(m => new CascaderView<int>()
                    {
                        Label = departmentList.FirstOrDefault(n => n.DepartmentID == m.DepartmentID)?.DepartmentContent ?? "",
                        Value = m.DepartmentID,
                        Disabled = false,
                        Children = []
                    }).ToList()
                }).ToList();
            return options;
        }

        /// <summary>
        /// 获取科室集合
        /// </summary>
        /// <param name="employeeToDepartments">当前用户的权限科室</param>
        /// <param name="departmentList">科室字典</param>
        /// <returns></returns>
        private async Task<IEnumerable<int>> GetDepartmentIDs(EmployeeToDepartmentInfo[] employeeToDepartments, List<DepartmentListInfo> departmentList)
        {
            if (employeeToDepartments.Any(m => (m.IsMainDepartment ?? false) && m.DepartmentID == DEPARTMENT_ID_405))
            {
                return departmentList.Where(m => m.OrganizationType == "1").Select(m => m.DepartmentID);
            }

            var departIDs = new HashSet<int>() { DEPARTMENT_ID_405 };
            foreach (var empToDepart in employeeToDepartments)
            {
                // 获取此部门的连续上下级部门ID集合
                var departments = await _dictionaryService.GetSuperAndSubDepartmentsByID(empToDepart.DepartmentID, false);
                departments.ForEach(m => departIDs.Add(m.DepartmentID));
            }

            return departIDs;
        }

        /// <summary>
        /// 获取年度计划状态
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        public async Task<(string mainID, bool isPublished)> GetAnnualPlanMainIDAndStatus(int departmentID, int year)
        {
            var tuple = await _mainRepository.GetAnnualPlanMainIDAndStatus(departmentID, year);
            return tuple;
        }

        /// <summary>
        /// 获取年度计划附件列表
        /// </summary>
        /// <param name="annualPlanMainID">主表ID</param>
        /// <returns></returns>
        public async Task<List<FileView>> GetAnnualPlanAttachments(string annualPlanMainID)
        {
            return await _fileService.GetFileListByClassAndSourceAsync(null, annualPlanMainID);
        }

        #endregion

        #region 计划分类

        /// <summary>
        /// 分类排序
        /// </summary>
        /// <param name="resetSortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> ResetAnnualPlanTypesSort(ResetAnnualPlanTypesSortDto resetSortView, string employeeID)
        {
            await ResetPlanGoalsSort(resetSortView.PlanGoalIDAndSort, resetSortView.MainID, employeeID);
            await ResetPlanGroupsSort(resetSortView.PlanGroupIDAndSort, resetSortView.MainID, employeeID);
            await ResetPlanIndicatorsSort(resetSortView.PlanIndicatorIDAndSort, resetSortView.MainID, employeeID);
            await ResetPlanProjectsSort(resetSortView.PlanProjectIDAndSort, resetSortView.MainID, employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        #endregion

        #region 策略目标

        /// <summary>
        /// 获取年度计划详情
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanMaintainResponse> GetAnnualPlan(string mainID)
        {
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var goalList = await _goalListRepository.GetAll<AnnualGoalListInfo>();

            var goals = await _mainGoalRepository.GetAPGoalViews(mainID);
            var groups = await _annualPlanGoalGroupRepository.GetAnnualPlanGroups(mainID);
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetIndicatorDetailsByPlanMainID(mainID);
            var projectDetails = await _annualPlanProjectDetailRepository.GetProjectDetailsByPlanMainID(mainID);

            goals.ForEach(goal => goal.GoalContent = goalList.Find(item => item.AnnualGoalID == goal.GoalID)?.GoalContent);
            var annualPlanMaintain = new AnnualPlanMaintainResponse
            {
                PlanTypes = [.. goals.GroupBy(m => m.TypeID).Select(m => new APType
                {
                    TypeID = m.Key,
                    TypeContent = typeList.Find(n => n.AnnualPlanTypeID == m.Key)?.AnnualPlanTypeContent,
                })],
                GoalsByTypeID = goals.GroupBy(m => m.TypeID).ToDictionary(m => m.Key, m => m.ToArray()),
                PlanGroups = groups,
                IndicatorsByGroupID = indicatorDetails.GroupBy(m => m.GroupID).ToDictionary(m => m.Key, m => m.ToArray()),
                ProjectsByGroupID = projectDetails.GroupBy(m => m.GroupID).ToDictionary(m => m.Key, m => m.ToArray()),
            };
            return annualPlanMaintain;
        }

        /// <summary>
        /// 更新目标排序
        /// </summary>
        /// <param name="resetSortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> ResetAnnualPlanPlanGoalsSort(AnnualPlanGoalResetSortDto resetSortView, string employeeID)
        {
            var planGoals = await ResetPlanGoalsSort(resetSortView.PlanGoalIDAndSort, resetSortView.MainID, employeeID);
            await ResetPlanGroupsSort(resetSortView.PlanGroupIDAndSort, resetSortView.MainID, employeeID);
            await ResetPlanIndicatorsSort(resetSortView.PlanIndicatorIDAndSort, resetSortView.MainID, employeeID);
            await ResetPlanProjectsSort(resetSortView.PlanProjectIDAndSort, resetSortView.MainID, employeeID);

            var draggedPlanGoalID = resetSortView.DraggedPlanGoalIDAndTypeID.FirstOrDefault().Key;
            planGoals.Find(m => m.AnnualPlanMainGoalID == draggedPlanGoalID)
                .AnnualPlanTypeID = resetSortView.DraggedPlanGoalIDAndTypeID.FirstOrDefault().Value;

            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        private async Task<List<AnnualPlanMainGoalInfo>> ResetPlanGoalsSort(Dictionary<string, int> planGoalIDAndSort, string mainID, string employeeID)
        {
            var planGoals = await _mainGoalRepository.GetInfosByPlanMainID(mainID, true);
            planGoals.ForEach(planGoal =>
            {
                planGoalIDAndSort.TryGetValue(planGoal.AnnualPlanMainGoalID, out var sort);
                if (sort != planGoal.Sort)
                {
                    planGoal.Sort = sort;
                    planGoal.Modify(employeeID);
                }
            });
            return planGoals;
        }

        #endregion

        #region 策略分组

        /// <summary>
        /// 获取负责部门候选项
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<string[]> GetDepartmentOptions(string mainID)
        {
            var groups = await _annualPlanGoalGroupRepository.GetAPGroupDepartmentsByMainID(mainID);
            var separator = new[] { "\r\n", "\r", "\n", "、" };
            var departmentOptions = groups.Where(m => m is not null).SelectMany(m =>
            m.Split(separator, StringSplitOptions.RemoveEmptyEntries)).Where(m => !string.IsNullOrEmpty(m)).Distinct().ToArray();
            return departmentOptions;
        }

        /// <summary>
        /// 分组新增
        /// </summary>
        /// <param name="saveView">分组新增View</param>
        /// <returns>新GroupID</returns>
        private async Task<string> AddAnnualGoalGroup(SaveGroupView saveView)
        {
            var groupID = saveView.GroupID.GetRealID(out _);
            if (string.IsNullOrEmpty(groupID))
            {
                throw new Exception("新增失败！缺少必要参数！");
            }
            var newGroupInfo = new AnnualPlanGoalGroupInfo
            {
                AnnualPlanGoalGroupID = groupID,
                AnnualPlanMainID = saveView.MainID,
                AnnualPlanMainGoalID = saveView.MainGoalID,
                HospitalID = saveView.HospitalID,
                ResponsibleDepartments = saveView.ResponsibleDepartments != null ? string.Join("\n", saveView.ResponsibleDepartments) : "",
                Sort = saveView.Sort,
            };
            newGroupInfo.Add(saveView.EmployeeID);
            newGroupInfo.Modify(saveView.EmployeeID);
            await _unitOfWork.GetRepository<AnnualPlanGoalGroupInfo>().InsertAsync(newGroupInfo);
            // 更新其后的分组序号
            var groups = await _annualPlanGoalGroupRepository.GetAfterSortGroup(saveView.MainID, saveView.Sort);
            foreach (var group in groups)
            {
                group.Sort += 1;
                group.Modify(saveView.EmployeeID);
            }
            return newGroupInfo.AnnualPlanGoalGroupID;
        }

        /// <summary>
        /// 分组保存
        /// </summary>
        /// <param name="saveView">分组更新View</param>
        /// <returns></returns>
        public async Task<string> SaveAnnualPlanGroup(SaveGroupView saveView)
        {
            // 若groupID带temp_，则走新增分组逻辑
            if (saveView.GroupID.Contains("temp"))
            {
                var groupID = await AddAnnualGoalGroup(saveView);
                await _unitOfWork.SaveChangesAsync();
                return groupID;
            }

            var goalGroupInfo = await _annualPlanGoalGroupRepository.GetGoalGroup(saveView.GroupID) ?? throw new CustomException("发生内部异常，请联系管理员！");
            var isUpdate = false;
            var newResponsibleDepartments = string.Join("\n", saveView.ResponsibleDepartments);
            if (goalGroupInfo.ResponsibleDepartments != newResponsibleDepartments)
            {
                goalGroupInfo.ResponsibleDepartments = newResponsibleDepartments;
                isUpdate = true;
            }
            if (goalGroupInfo.Sort != saveView.Sort)
            {
                goalGroupInfo.Sort = saveView.Sort;
                // 更新其后的分组序号
                var groups = await _annualPlanGoalGroupRepository.GetAfterSortGroup(saveView.MainID, saveView.Sort);
                foreach (var group in groups)
                {
                    group.Sort += 1;
                    group.Modify(saveView.EmployeeID);
                }
                isUpdate = true;
            }
            if (isUpdate)
            {
                goalGroupInfo.Modify(saveView.EmployeeID);
                await _unitOfWork.SaveChangesAsync();
                return goalGroupInfo.AnnualPlanGoalGroupID;
            }
            return goalGroupInfo.AnnualPlanGoalGroupID;
        }

        /// <summary>
        /// 分组删除
        /// </summary>
        /// <param name="groupID">分组ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteAnnualGoalGroup(string groupID, string employeeID)
        {
            // 删除分组信息
            var goalGroupInfo = await _annualPlanGoalGroupRepository.GetGoalGroup(groupID);
            goalGroupInfo.Delete(employeeID);
            // 其后的分组序号减1
            var groups = await _annualPlanGoalGroupRepository.GetAfterSortGroup(goalGroupInfo.AnnualPlanMainID, goalGroupInfo.Sort);
            foreach (var group in groups)
            {
                group.Sort -= 1;
                group.Modify(employeeID);
            }
            // 删除关联的指标明细
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetIndicatorInfosByGroupID(groupID);
            indicatorDetails.ForEach(m => m.Delete(employeeID));
            // 删除关联的项目明细
            var projectDetails = await _annualPlanProjectDetailRepository.GetProjectInfosByGroupID(groupID);
            projectDetails.ForEach(m => m.Delete(employeeID));
            // 删除关联的项目明细所对应的执行项目及其明细
            var projectDetailIDs = await _annualPlanProjectDetailRepository.GetProjectDetailIDsByGroupID(groupID);
            var planMainID = projectDetails.FirstOrDefault()?.AnnualPlanMainID;
            await _annualPlanInterventionService.DeleteInterventionsByProjectDetailID(planMainID, employeeID, projectDetailIDs);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 分组排序
        /// </summary>
        /// <param name="sortView">排序View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> ResetAnnualPlanGroupsSort(ResetAnnualPlanGroupSortDto sortView, string employeeID)
        {
            await ResetPlanGroupsSort(sortView.PlanGroupIDAndSort, sortView.MainID, employeeID);
            await ResetPlanIndicatorsSort(sortView.PlanIndicatorIDAndSort, sortView.MainID, employeeID);
            await ResetPlanProjectsSort(sortView.PlanProjectIDAndSort, sortView.MainID, employeeID);

            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        private async Task ResetPlanGroupsSort(Dictionary<string, int> planGroupIDAndSort, string mainID, string employeeID)
        {
            var planGroups = await _annualPlanGoalGroupRepository.GetInfosByMainID(mainID);
            planGroups.ForEach(planGroup =>
            {
                planGroupIDAndSort.TryGetValue(planGroup.AnnualPlanGoalGroupID, out var sort);
                if (sort != planGroup.Sort)
                {
                    planGroup.Sort = sort;
                    planGroup.Modify(employeeID);
                }
            });
        }

        #endregion

        #region 策略指标

        /// <summary>
        /// 获取某计划的指标明细
        /// </summary>
        /// <param name="view">查询View</param>
        /// <returns></returns>
        public async Task<List<APIndicatorDetail>> GetIndicatorDetails(APDetailsSearchView view)
        {
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetViewsByMainIDAndMainGoalIDs(view.MainID, view.MainGoalIDs);
            return indicatorDetails;
        }

        /// <summary>
        /// 获取某分组的指标明细
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        /// <returns></returns>
        public async Task<List<APIndicatorDetail>> GetIndicatorDetailsByGroupID(string mainID, string groupID)
        {
            var views = await _annualPlanIndicatorDetailRepository.GetViewsByGroupID(mainID, groupID);
            return views.OrderBy(m => m.Sort).ToList();
        }

        /// <summary>
        /// 获取计划已参考的指标ID集合
        /// </summary>
        /// <param name="mainID">年度计划主表ID</param>
        /// <returns></returns>
        public async Task<int[]> GetRefIndicatorIDs(string mainID)
        {
            var mainInfo = await _mainRepository.GetAnnualPlanMain(mainID);
            if (mainInfo == null)
            {
                return null;
            }
            var refIndicatorIDs = await _annualPlanIndicatorDetailRepository.GetRefIndicatorIDs(mainID);
            return refIndicatorIDs;
        }

        /// <summary>
        /// 新增策略指标
        /// </summary>
        /// <param name="addView">新增View</param>
        /// <returns></returns>
        public async Task<AnnualPlanIndicatorDetailInfo> AddIndicatorDetail(SaveIndicatorDetailView addView)
        {
            // 若没有AnnualIndicatorID，新增指标字典
            if (addView.Detail.AnnualIndicatorID == 0)
            {
                var indicatorListView = new AnnualIndicatorListView
                {
                    DepartmentID = addView.DepartmentID,
                    IndicatorContent = addView.Detail.LocalShowName,
                    EnableYear = addView.Detail.Year,
                    HospitalID = addView.HospitalID,
                    Language = addView.Language,
                    AddEmployeeID = addView.EmployeeID,
                    EmployeeID = addView.EmployeeID
                };
                (_, addView.Detail.AnnualIndicatorID) = await _annualIndicatorListService.SaveAnnualIndicatorList(indicatorListView);
            }

            addView.Detail.GroupID = addView.Detail.GroupID.GetRealID(out _);
            var newIndicatorDetailInfo = CreateIndicatorDetailInfo(addView);
            await _unitOfWork.GetRepository<AnnualPlanIndicatorDetailInfo>().InsertAsync(newIndicatorDetailInfo);

            // 更新新增明细之后的序号
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetAfterSortDetail(addView.Detail.MainID, addView.Detail.Sort);
            foreach (var indicatorDetail in indicatorDetails)
            {
                indicatorDetail.Sort += 1;
                indicatorDetail.Modify(addView.EmployeeID);
            }
            await _unitOfWork.SaveChangesAsync();
            return newIndicatorDetailInfo;
        }

        /// <summary>
        /// 创建指标明细Model实例
        /// </summary>
        /// <param name="addView">新增View</param>
        /// <returns></returns>
        private static AnnualPlanIndicatorDetailInfo CreateIndicatorDetailInfo(SaveIndicatorDetailView addView)
        {
            var newIndicatorDetailInfo = new AnnualPlanIndicatorDetailInfo
            {
                LocalShowName = addView.Detail.LocalShowName,
                AnnualIndicatorID = addView.Detail.AnnualIndicatorID,
                AnnualPlanMainID = addView.Detail.MainID,
                AnnualPlanMainGoalID = addView.Detail.MainGoalID,
                AnnualPlanGoalGroupID = addView.Detail.GroupID,
                DepartmentID = addView.DepartmentID,
                Year = addView.Detail.Year,
                HospitalID = addView.HospitalID,
                Operator = addView.Detail.Operator ?? "",
                ReferenceValue = addView.Detail.ReferenceValue,
                Unit = addView.Detail.Unit ?? "",
                MarkID = addView.Detail.MarkID,
                Sort = addView.Detail.Sort,
                Remark = addView.Detail.Remark,
            };
            newIndicatorDetailInfo.Add(addView.EmployeeID).Modify(addView.EmployeeID);
            newIndicatorDetailInfo.DetailID = newIndicatorDetailInfo.GetId();
            return newIndicatorDetailInfo;
        }

        /// <summary>
        /// 更新指标明细
        /// </summary>
        /// <param name="updateView">更新View</param>
        /// <returns></returns>
        public async Task<bool> UpdateIndicatorDetail(SaveIndicatorDetailView updateView)
        {
            updateView.Detail.GroupID = updateView.Detail.GroupID.GetRealID(out _);
            var oldDetailInfo = await _annualPlanIndicatorDetailRepository.GetDetailByID(updateView.Detail.DetailID);
            if (oldDetailInfo == null)
            {
                return false;
            }
            var isInfoUpdate = ObjUpdater<AnnualPlanIndicatorDetailInfo, APIndicatorDetail>.UpdateWithSource(oldDetailInfo, updateView.Detail);
            if (!isInfoUpdate)
            {
                return true;
            }
            oldDetailInfo.Modify(updateView.EmployeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 更新指标明细
        /// </summary>
        /// <param name="resetSortView">更新View</param>
        /// <returns></returns>
        public async Task<bool> ResetAnnualPlanIndicatorsSort(ResetIndicatorsSortDto resetSortView)
        {
            var newDetail = resetSortView.DraggedPlanIndicator;
            newDetail.GroupID = newDetail.GroupID.GetRealID(out _);
            var oldDetailInfo = await _annualPlanIndicatorDetailRepository.GetDetailByID(newDetail.DetailID);
            if (oldDetailInfo == null)
            {
                _logger.Error($"指标明细ID：{newDetail.DetailID}不存在！");
                throw new CustomException("发生内部异常，请联系管理员！");
            }
            var isInfoUpdate = false;
            if (oldDetailInfo.AnnualPlanGoalGroupID != newDetail.GroupID)
            {
                isInfoUpdate = true;
                oldDetailInfo.AnnualPlanGoalGroupID = newDetail.GroupID;
            }
            if (oldDetailInfo.AnnualPlanMainGoalID != newDetail.MainGoalID)
            {
                isInfoUpdate = true;
                oldDetailInfo.AnnualPlanMainGoalID = newDetail.MainGoalID;
            }
            if (!isInfoUpdate)
            {
                return true;
            }
            oldDetailInfo.Modify(resetSortView.EmployeeID);

            await ResetPlanIndicatorsSort(resetSortView.PlanIndicatorIDAndSort, newDetail.MainID, resetSortView.EmployeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 重排序策略指标
        /// </summary>
        /// <param name="planIndicatorIDAndSort">策略指标ID与新序号</param>
        /// <param name="mainID">年度计划主表ID</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task ResetPlanIndicatorsSort(Dictionary<string, int> planIndicatorIDAndSort, string mainID, string employeeID)
        {
            var planIndicators = await _annualPlanIndicatorDetailRepository.GetInfosByMainID(mainID);
            // 前端仅传递渲染出来的项的新序号，后端对于那些未渲染出的项，仍需更新序号
            var maxSort = planIndicatorIDAndSort.Values.Max();
            planIndicators.ForEach(planIndicator =>
            {
                if (planIndicatorIDAndSort.TryGetValue(planIndicator.DetailID, out var sort))
                {
                    if (sort != planIndicator.Sort)
                    {
                        planIndicator.Sort = sort;
                        planIndicator.Modify(employeeID);
                    }
                    return;
                }
                maxSort += 1;
                if (maxSort != planIndicator.Sort)
                {
                    planIndicator.Sort = sort;
                    planIndicator.Modify(employeeID);
                }
            });
        }

        /// <summary>
        /// 指标明细删除
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="indicatorDetailID">指标明细ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteIndicatorDetail(string mainID, string indicatorDetailID, string employeeID)
        {
            // 删除指标明细
            var detailInfo = await _annualPlanIndicatorDetailRepository.GetDetailByID(indicatorDetailID);
            detailInfo.Delete(employeeID);

            // 更新排序
            var details = await _annualPlanIndicatorDetailRepository.GetAfterSortDetail(mainID, detailInfo.Sort);
            // 不包括已被删除的指标明细，所以要从1开始
            for (int i = 1; i < details.Count; i++)
            {
                details[i].Sort -= 1;
                details[i].Modify(employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        #endregion

        #region 目标任务

        /// <summary>
        /// 获取按MainGoalID分组的目标任务
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="mainGoalIDs">计划目标表IDs</param>
        /// <returns></returns>
        public async Task<List<APProjectDetail>> GetProjectDetails(string mainID, string[] mainGoalIDs = null)
        {
            var projectDetails = await _annualPlanProjectDetailRepository.GetViewsByPlanMainID(mainID, mainGoalIDs);
            return projectDetails;
        }

        /// <summary>
        /// 获取某分组的目标任务
        /// </summary>
        /// <param name="mainID">计划主表ID</param>
        /// <param name="groupID">分组ID</param>
        public async Task<List<APProjectDetail>> GetProjectDetailsByGroupID(string mainID, string groupID)
        {
            var views = await _annualPlanProjectDetailRepository.GetViewsByGroupID(mainID, groupID);
            return views.OrderBy(m => m.Sort).ToList();
        }

        /// <summary>
        /// 新增目标任务
        /// </summary>
        /// <param name="addView">新增View</param>
        /// <param name="hospitalID">医院序号</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<string> AddProjectDetail(APProjectDetail addView, string hospitalID, string employeeID)
        {
            addView.GroupID = addView.GroupID.GetRealID(out _);
            // 实例化项目明细Model实例
            var newProjectDetailInfo = new AnnualPlanProjectDetailInfo
            {
                DetailID = addView.DetailID.GetRealID(out _),
                Content = addView.Content,
                AnnualPlanMainID = addView.MainID,
                AnnualPlanMainGoalID = addView.MainGoalID,
                AnnualPlanGoalGroupID = addView.GroupID,
                HospitalID = hospitalID,
                MarkID = addView.MarkID,
                Sort = addView.Sort
            };
            newProjectDetailInfo.Add(employeeID).Modify(employeeID);
            await _unitOfWork.GetRepository<AnnualPlanProjectDetailInfo>().InsertAsync(newProjectDetailInfo);

            // 更新序号
            var projectDetails = await _annualPlanProjectDetailRepository.GetAfterSortDetail(addView.MainID, addView.Sort);
            foreach (var projectDetail in projectDetails)
            {
                projectDetail.Sort += 1;
                projectDetail.Modify(employeeID);
            }
            await _unitOfWork.SaveChangesAsync();
            return newProjectDetailInfo.DetailID;
        }

        /// <summary>
        /// 更新目标任务
        /// </summary>
        /// <param name="updateView">更新View</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> UpdateProjectDetail(APProjectDetail updateView, string employeeID)
        {
            updateView.GroupID = updateView.GroupID.GetRealID(out _);
            // 获取项目明细信息
            var projectDetailInfo = await _annualPlanProjectDetailRepository.GetDetailByID(updateView.DetailID);
            var isUpdate = ObjUpdater<AnnualPlanProjectDetailInfo, APProjectDetail>.UpdateWithSource(projectDetailInfo, updateView);
            if (projectDetailInfo.AnnualPlanGoalGroupID != updateView.GroupID)
            {
                isUpdate = true;
                projectDetailInfo.AnnualPlanGoalGroupID = updateView.GroupID;
            }
            if (projectDetailInfo.AnnualPlanMainGoalID != updateView.MainGoalID)
            {
                isUpdate = true;
                projectDetailInfo.AnnualPlanMainGoalID = updateView.MainGoalID;
            }
            if (isUpdate)
            {
                projectDetailInfo.Modify(employeeID);
            }

            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 目标任务重排序
        /// </summary>
        /// <param name="resortView">重排序参数</param>
        /// <returns></returns>
        public async Task<bool> ResetAnnualPlanProjectsSort(ResetProjectsSortDto resetSortView)
        {
            var newDetail = resetSortView.DraggedPlanProject;
            newDetail.GroupID = newDetail.GroupID.GetRealID(out _);
            // 获取项目明细信息
            var projectDetailInfo = await _annualPlanProjectDetailRepository.GetDetailByID(newDetail.DetailID);
            var isUpdate = false;
            if (projectDetailInfo.AnnualPlanGoalGroupID != newDetail.GroupID)
            {
                isUpdate = true;
                projectDetailInfo.AnnualPlanGoalGroupID = newDetail.GroupID;
            }
            if (projectDetailInfo.AnnualPlanMainGoalID != newDetail.MainGoalID)
            {
                isUpdate = true;
                projectDetailInfo.AnnualPlanMainGoalID = newDetail.MainGoalID;
            }
            if (isUpdate)
            {
                projectDetailInfo.Modify(resetSortView.EmployeeID);
            }
            await ResetPlanProjectsSort(resetSortView.PlanProjectIDAndSort, newDetail.MainID, resetSortView.EmployeeID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 重排序目标任务
        /// </summary>
        /// <param name="planProjectIDAndSort">目标任务ID与新序号</param>
        /// <param name="mainID">主表ID</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task ResetPlanProjectsSort(Dictionary<string, int> planProjectIDAndSort, string mainID, string employeeID)
        {
            var planProjects = await _annualPlanProjectDetailRepository.GetInfosByMainID(mainID);
            // 前端仅传递渲染出来的项的新序号，后端对于那些未渲染出的项，仍需更新序号
            var maxSort = planProjectIDAndSort.Values.Max();
            planProjects.ForEach(planProject =>
            {
                if (planProjectIDAndSort.TryGetValue(planProject.DetailID, out var sort))
                {
                    if (sort != planProject.Sort)
                    {
                        planProject.Sort = sort;
                        planProject.Modify(employeeID);
                    }
                    return;
                }
                maxSort += 1;
                if (planProject.Sort != maxSort)
                {
                    planProject.Sort = maxSort;
                    planProject.Modify(employeeID);
                }
            });
        }

        /// <summary>
        /// 明细顺序重排
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="repository">查询仓储</param>
        /// <param name="mainID">主表ID</param>
        /// <param name="reSortDetails">新明细顺序</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private static async Task UpdateDetailsSort<T>(IBaseAPDetailRepository repository, string mainID, T[] reSortDetails, string employeeID) where T : IBaseAPDetail
        {
            // 按照前端的Sort更新明细
            var minSort = reSortDetails.First().Sort;
            var maxSort = reSortDetails.Last().Sort;
            var oldDetails = await repository.GetRangeInfosByMainID(mainID, minSort, maxSort);
            foreach (var reSortDetail in reSortDetails)
            {
                var oldDetail = oldDetails.FirstOrDefault(m => m.DetailID == reSortDetail.DetailID);
                if (oldDetail == null || oldDetail.Sort == reSortDetail.Sort)
                {
                    continue;
                }
                oldDetail.Sort = reSortDetail.Sort;
                (oldDetail as dynamic).Modify(employeeID);
            }
        }

        /// <summary>
        /// 项目明细删除
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="projectDetailID">项目明细ID</param>
        /// <param name="employeeID">HR工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteProjectDetail(string mainID, string projectDetailID, string employeeID)
        {
            // 删除项目明细
            var detail = await _annualPlanProjectDetailRepository.GetDetailByID(projectDetailID);
            detail?.Delete(employeeID);
            // 删除关联执行项目
            await _annualPlanInterventionService.DeleteInterventionsByProjectDetailID(detail?.AnnualPlanMainID, employeeID, projectDetailID);
            // 重新排序
            var details = await _annualPlanProjectDetailRepository.GetAfterSortDetail(mainID, detail.Sort);
            // 不包括已被删除的明细，所以要从1开始
            for (int i = 1; i < details.Count; i++)
            {
                details[i].Sort -= 1;
                details[i].Modify(employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        #endregion

        #region 年度计划分类目标维护

        /// <summary>
        /// 获取年度计划分类-目标
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年份</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanMainGoalView>> GetAnnualPlanMainGoalList(int departmentID, int year)
        {
            var returnViews = new List<AnnualPlanMainGoalView>();
            var annualPlanMainID = await _mainRepository.GetMainIDByDeptIDAndYear(departmentID, year);
            if (string.IsNullOrEmpty(annualPlanMainID))
            {
                return returnViews;
            }
            var annualPlanMainGoalList = await _mainGoalRepository.GetInfosByPlanMainID(annualPlanMainID);
            if (annualPlanMainGoalList == null || annualPlanMainGoalList.Count() <= 0)
            {
                return returnViews;
            }
            //获取年度计划字典
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var goalList = await _goalListRepository.GetAll<AnnualGoalListInfo>();
            var employeeIDs = annualPlanMainGoalList.Select(m => m.AddEmployeeID).Concat(annualPlanMainGoalList.Select(m => m.ModifyEmployeeID)).Distinct().ToList();
            var employees = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            foreach (var annualPlanMainGoal in annualPlanMainGoalList)
            {
                var returnView = new AnnualPlanMainGoalView()
                {
                    AnnualPlanMainID = annualPlanMainGoal.AnnualPlanMainID,
                    AnnualPlanMainGoalID = annualPlanMainGoal.AnnualPlanMainGoalID,
                    AnnualPlanTypeID = annualPlanMainGoal.AnnualPlanTypeID,
                    AnnualPlanGoalID = annualPlanMainGoal.AnnualGoalID,
                    Sort = annualPlanMainGoal.Sort,
                    AddDateTime = annualPlanMainGoal.AddDateTime,
                    AddEmployeeID = annualPlanMainGoal.AddEmployeeID,
                    ModifyDateTime = annualPlanMainGoal.ModifyDateTime,
                    ModifyEmployeeID = annualPlanMainGoal.ModifyEmployeeID,
                    AnnualPlanGoalContent = goalList.Find(m => m.AnnualGoalID == annualPlanMainGoal.AnnualGoalID)?.GoalContent ?? "",
                    DepartmentID = goalList.Find(m => m.AnnualGoalID == annualPlanMainGoal.AnnualGoalID)?.DepartmentID ?? 0,
                    AnnualPlanTypeContent = typeList.Find(m => m.AnnualPlanTypeID == annualPlanMainGoal.AnnualPlanTypeID)?.AnnualPlanTypeContent ?? "",
                    AddEmployeeName = employees.TryGetValue(annualPlanMainGoal.AddEmployeeID, out var addEmployeeName) ? addEmployeeName : string.Empty,
                    ModifyEmployeeName = employees.TryGetValue(annualPlanMainGoal.ModifyEmployeeID, out var modifyEmployeeName) ? modifyEmployeeName : string.Empty,
                };
                returnViews.Add(returnView);
            }
            return returnViews.OrderBy(m => m.AnnualPlanTypeContent).ToList();
        }

        /// <summary>
        /// 获取上级部门针对某一目标制定的项目内容集合
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="year">年度</param>
        /// <param name="mainGoalID">目标ID</param>
        /// <returns></returns>
        public async Task<List<DictItem>> GetSuperiorProjectDetail(int departmentID, int year, string mainGoalID)
        {
            // "4" 为红色三角标识
            var markIDs = new List<string> { "4" };
            // 默认 没有连续上级部门的（例如片区） - 取护理部
            var goalID = await _mainGoalRepository.GetGoalIDByMainGoalID(mainGoalID);
            var upperDepartmentID = 405;
            var upperDepartment = await _departmentListRepository.GetUpperDepartment(departmentID);
            if (upperDepartment != null && upperDepartment.DepartmentID != 0)
            {
                upperDepartmentID = upperDepartment.DepartmentID;
            }
            var planMainID = await _mainRepository.GetMainIDByDeptIDAndYear(upperDepartmentID, year);
            if (string.IsNullOrEmpty(planMainID))
            {
                return [];
            }
            var upperDepartmentMainGoalID = await _mainGoalRepository.GetAnnualPlanMainGoalIDByGoalIDAndMainIDAsync(planMainID, goalID);
            if (string.IsNullOrEmpty(upperDepartmentMainGoalID))
            {
                return [];
            }
            //根据记录ID和目标获取
            var projectDetailContent = await _annualPlanProjectDetailRepository
                    .GetProjectDetailContentAsNoTracking(planMainID, markIDs, upperDepartmentMainGoalID);
            return projectDetailContent.Select(m => new DictItem { Key = 0, Value = m }).ToList();
        }

        /// <summary>
        /// 发布年度计划
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> PublishAnnualPlan(string mainID, string employeeID)
        {
            var mainInfo = await _mainRepository.GetInfoByMainID(mainID);
            if (mainInfo == null)
            {
                return false;
            }
            mainInfo.StatusCode = AnnualPlanEnums.PlanStatus.Published;
            mainInfo.Modify(employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        #endregion
    }
}
