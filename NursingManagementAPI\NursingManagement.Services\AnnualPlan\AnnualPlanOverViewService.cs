﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Drawing;
using DocumentFormat.OpenXml.Drawing.Wordprocessing;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Wordprocessing;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.Services.Interface.AnnualPlan;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.AnnualPlan.MonthlyPlan;
using NursingManagement.ViewModels.AnnualPlan.QuarterPlan;

using A = DocumentFormat.OpenXml.Drawing;
using Bold = DocumentFormat.OpenXml.Wordprocessing.Bold;
using BottomBorder = DocumentFormat.OpenXml.Wordprocessing.BottomBorder;
using Break = DocumentFormat.OpenXml.Wordprocessing.Break;
using Color = DocumentFormat.OpenXml.Wordprocessing.Color;
using File = System.IO.File;
using LeftBorder = DocumentFormat.OpenXml.Wordprocessing.LeftBorder;
using Paragraph = DocumentFormat.OpenXml.Wordprocessing.Paragraph;
using PIC = DocumentFormat.OpenXml.Drawing.Pictures;
using RightBorder = DocumentFormat.OpenXml.Wordprocessing.RightBorder;
using Run = DocumentFormat.OpenXml.Wordprocessing.Run;
using RunProperties = DocumentFormat.OpenXml.Wordprocessing.RunProperties;
using Table = DocumentFormat.OpenXml.Wordprocessing.Table;
using TableCell = DocumentFormat.OpenXml.Wordprocessing.TableCell;
using TableCellProperties = DocumentFormat.OpenXml.Wordprocessing.TableCellProperties;
using TableProperties = DocumentFormat.OpenXml.Wordprocessing.TableProperties;
using TableRow = DocumentFormat.OpenXml.Wordprocessing.TableRow;
using Text = DocumentFormat.OpenXml.Wordprocessing.Text;
using TopBorder = DocumentFormat.OpenXml.Wordprocessing.TopBorder;

namespace NursingManagement.Services
{
    public class AnnualPlanOverViewService : IAnnualPlanOverViewService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IAnnualPlanMainGoalRepository _mainGoalRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDictionaryService _dictionaryService;
        private readonly IAnnualPlanInterventionMainRepository _annualPlanInterventionMainRepository;
        private readonly IAnnualInterventionMainPrincipalRepository _annualInterventionMainPrincipalRepository;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly IAnnualPlanTypeListRepository _typeListRepository;
        private readonly IAnnualGoalListRepository _goalListRepository;
        private readonly IAnnualPlanGoalGroupRepository _annualPlanGoalGroupRepository;
        private readonly IAnnualPlanIndicatorDetailRepository _annualPlanIndicatorDetailRepository;
        private readonly IAnnualPlanProjectDetailRepository _annualPlanProjectDetailRepository;
        private readonly IQuarterPlanMaintainRepository _quarterPlanMaintainRepository;
        private readonly IMonthlyPlanMaintainRepository _monthlyPlanMaintainRepository;

        public AnnualPlanOverViewService(
            IAnnualPlanMainGoalRepository mainGoalRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IDictionaryService dictionaryService,
            IAnnualPlanInterventionMainRepository annualPlanInterventionMainRepository,
            IAnnualInterventionMainPrincipalRepository annualInterventionMainPrincipalRepository,
            IAppConfigSettingRepository appConfigSettingRepository,
            IAnnualPlanTypeListRepository annualPlanTypeListRepository,
            IAnnualGoalListRepository annualGoalListRepository,
            IAnnualPlanGoalGroupRepository annualPlanGoalGroupRepository,
            IAnnualPlanIndicatorDetailRepository annualPlanIndicatorDetailRepository,
            IAnnualPlanProjectDetailRepository annualPlanProjectDetailRepository,
            IQuarterPlanMaintainRepository quarterPlanMaintainRepository,
            IMonthlyPlanMaintainRepository monthlyPlanMaintainRepository
            )
        {
            _mainGoalRepository = mainGoalRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _dictionaryService = dictionaryService;
            _annualPlanInterventionMainRepository = annualPlanInterventionMainRepository;
            _annualInterventionMainPrincipalRepository = annualInterventionMainPrincipalRepository;
            _appConfigSettingRepository = appConfigSettingRepository;
            _typeListRepository = annualPlanTypeListRepository;
            _goalListRepository = annualGoalListRepository;
            _annualPlanGoalGroupRepository = annualPlanGoalGroupRepository;
            _annualPlanIndicatorDetailRepository = annualPlanIndicatorDetailRepository;
            _annualPlanProjectDetailRepository = annualPlanProjectDetailRepository;
            _quarterPlanMaintainRepository = quarterPlanMaintainRepository;
            _monthlyPlanMaintainRepository = monthlyPlanMaintainRepository;
        }

        #region 常量

        /// <summary>
        /// 图标类型
        /// </summary>
        private const string ICON_MODELTYPE_ANNUALPLAN = "AnnualPlan";

        /// <summary>
        /// 表格总宽度
        /// </summary>
        private const int TotalTableWidth = 11000;

        /// <summary>
        /// 定义列宽比例
        /// </summary>
        private readonly int[] _columnWidths =
        [
        (int)(TotalTableWidth * 0.20), // 类型列 20%
        (int)(TotalTableWidth * 0.50), // 工作重点列 50%
        (int)(TotalTableWidth * 0.15), // 完成要求列 15%
        (int)(TotalTableWidth * 0.15)  // 负责人列 15%
    ];

        #endregion

        /// <summary>
        /// 获取计划总览视图
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<List<AnnualPlanExportView>> GetAnnualPlanExportViews(string mainID)
        {
            var returnDatas = new List<AnnualPlanExportView>();
            //获取年度计划目标
            var mainGoals = await _mainGoalRepository.GetAPGoalViews(mainID);
            if (mainGoals == null || mainGoals.Count() <= 0)
            {
                return returnDatas;
            }
            //获取分组信息
            var mainGoalGroups = await _annualPlanGoalGroupRepository.GetAnnualPlanGroups(mainID);
            if (mainGoalGroups == null || mainGoalGroups.Count <= 0)
            {
                return returnDatas;
            }
            //获取年度计划字典
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            var goalList = await _goalListRepository.GetAll<AnnualGoalListInfo>();
            //获取明细
            var indicatorDetails = await _annualPlanIndicatorDetailRepository.GetViewsByMainIDAndMainGoalIDs(mainID);
            var projectDetails = await GetProjectDetails(mainID);
            var typeIDs = mainGoals.Select(m => m.TypeID).Distinct();
            foreach (var typeID in typeIDs)
            {
                var tempData = new AnnualPlanExportView()
                {
                    AnnualPlanTypeID = typeID,
                    AnnualPlanTypeName = typeList.FirstOrDefault(m => m.AnnualPlanTypeID == typeID)?.AnnualPlanTypeContent,
                };
                var goals = mainGoals.Where(m => m.TypeID == typeID).ToList();
                foreach (var goal in goals)
                {
                    goal.GoalContent = goalList.Find(m => m.AnnualGoalID == goal.GoalID)?.GoalContent;
                    var tempMainGoalGroups = mainGoalGroups.Where(m => m.MainGoalID == goal.MainGoalID).ToList();
                    foreach (var tempMainGoalGroup in tempMainGoalGroups)
                    {
                        tempMainGoalGroup.IndicatorDetails = indicatorDetails.Where(m => m.GroupID == tempMainGoalGroup.GroupID).OrderBy(m => m.Sort).ToList();
                        tempMainGoalGroup.ProjectDetails = projectDetails.FirstOrDefault(m => m.Key == tempMainGoalGroup.GroupID).Value;
                    }
                    goal.Groups = tempMainGoalGroups;
                }
                tempData.MainGoals = goals;
                returnDatas.Add(tempData);
            }
            return returnDatas;
        }

        /// <summary>
        /// 获取年度计划预览视图
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<AnnualPlanPreview> GetAnnualPlanPreview(string mainID)
        {
            //获取年度计划目标
            var planGoals = await _mainGoalRepository.GetBrowseView(mainID);
            if (planGoals == null || planGoals.Count <= 0)
            {
                return null;
            }
            var annualPlanPreview = new AnnualPlanPreview
            {
                AnnualPlanMainId = mainID,
                PlanTypes = planGoals.GroupBy(m => m.TypeId)
                .Select(m => new AnnualPlanPreview.PlanType
                {
                    TypeId = m.Key,
                    PlanGoals = [.. m]
                }).ToList()
            };
            return annualPlanPreview;
        }

        /// <summary>
        /// 获取项目明细
        /// </summary>
        /// <param name="mainID">主表ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, List<APProjectDetail>>> GetProjectDetails(string mainID)
        {
            var projectDetails = await _annualPlanProjectDetailRepository.GetViewsByPlanMainID(mainID);
            var projectDetailKeyPairs = projectDetails.GroupBy(m => m.GroupID).ToDictionary(k => k.Key, v => v.OrderBy(m => m.Sort).ToList());
            return projectDetailKeyPairs;
        }

        public async Task<string> CombineWordDoc(string mainID)
        {
            string url = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "AnnualPlanDocumentPath");
            if (string.IsNullOrEmpty(url))
            {
                return null;
            }
            var mainDocPath = AppContext.BaseDirectory + "AnnualPlanDocumentMainPath.docx";
            var docuName = Guid.NewGuid().ToString().Replace("-", "") + ".docx";
            var outputPath = AppContext.BaseDirectory + @"\files\" + docuName;
            //生成年度计划文档
            string annualPlanPath = await CreateAnnualPlanPart(mainID);
            if (string.IsNullOrEmpty(annualPlanPath))
            {
                _logger.Warn("下载年度计划文档路径获取失败settingCode = AnnualPlanDocumentPath");
                return null;
            }
            // 确保输出文件不是已存在的文件
            if (File.Exists(outputPath))
            {
                File.Delete(outputPath);
            }

            // 将主文档复制到输出路径
            File.Copy(mainDocPath, outputPath);

            // 打开输出文档
            using (WordprocessingDocument mainDoc = WordprocessingDocument.Open(outputPath, true))
            {
                MainDocumentPart mainPart = mainDoc.MainDocumentPart;

                // 打开要合并的文档
                using (WordprocessingDocument docToMerge = WordprocessingDocument.Open(annualPlanPath, false))
                {
                    MainDocumentPart mergePart = docToMerge.MainDocumentPart;

                    // 复制图片
                    foreach (ImagePart imagePart in mergePart.ImageParts)
                    {
                        ImagePart newImagePart = mainPart.AddImagePart(imagePart.ContentType);
                        using Stream stream = imagePart.GetStream();
                        using Stream newStream = newImagePart.GetStream(FileMode.Create, FileAccess.Write);
                        stream.CopyTo(newStream);
                    }

                    // 复制文档内容
                    foreach (var element in mergePart.Document.Body.Elements())
                    {
                        mainPart.Document.Body.Append(element.CloneNode(true));
                    }
                }

                // 保存并关闭文档
                mainDoc.Save();
                mainDoc.Dispose();
                //删除文档临时生成的文档
                File.Delete(annualPlanPath);
            }
            return url + docuName;
        }

        /// <summary>
        /// 创建文档年度计划部分
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task<string> CreateAnnualPlanPart(string mainID)
        {
            //获取生成路径配置
            var path = "AnnualPlanDocument.docx";
            if (string.IsNullOrEmpty(path))
            {
                return "";
            }
            using (WordprocessingDocument wordDoc = WordprocessingDocument.Create(path, WordprocessingDocumentType.Document))
            {
                MainDocumentPart mainPart = wordDoc.AddMainDocumentPart();
                mainPart.Document = new Document(new Body());
                var body = mainPart.Document.Body;
                //指标文字部分
                await GetAnnualPlanWord(mainID, body);
                AddPageBreakToDocument(body);
                //年度计划表格部分
                await GetAnnualPlanWordTable(mainID, body);
                AddPageBreakToDocument(body);
                //甘特图部分
                await GetAnnualPlanGanttWord(mainID, body);
                //设置行距
                CreateSpacingBetweenLines(body);
                //设置字体
                SetFontForAllTextInBody(body, "华文仿宋");
                SetFontSizeForAllTextInBody(body, "24");
                wordDoc.Save();
                wordDoc.Dispose();
            }
            return path;
        }

        private static void SetFontSizeForAllTextInBody(Body body, string fontSize)
        {
            // 遍历所有段落
            foreach (var paragraph in body.Descendants<Paragraph>())
            {
                // 遍历每个段落中的所有运行
                foreach (var run in paragraph.Descendants<Run>())
                {
                    // 创建或获取现有的RunProperties对象
                    RunProperties runProperties = run.RunProperties;
                    if (runProperties == null)
                    {
                        runProperties = new RunProperties();
                        run.PrependChild(runProperties);
                    }

                    // 设置字体大小
                    var size = runProperties.Elements<DocumentFormat.OpenXml.Wordprocessing.FontSize>().FirstOrDefault();
                    if (size == null)
                    {
                        size = new DocumentFormat.OpenXml.Wordprocessing.FontSize()
                        {
                            Val = fontSize
                        };
                        runProperties.Append(size);
                    }
                    else
                    {
                        size.Val = fontSize;
                    }
                }
            }
        }

        private static void SetFontForAllTextInBody(Body body, string fontName)
        {
            // 遍历所有段落
            foreach (var paragraph in body.Descendants<Paragraph>())
            {
                // 遍历每个段落中的所有运行
                foreach (var run in paragraph.Descendants<Run>())
                {
                    // 创建或获取现有的RunProperties对象
                    RunProperties runProperties = run.RunProperties;
                    if (runProperties == null)
                    {
                        runProperties = new RunProperties();
                        run.PrependChild(runProperties);
                    }

                    // 设置字体
                    RunFonts runFonts = runProperties.Elements<RunFonts>().FirstOrDefault();
                    if (runFonts == null)
                    {
                        runFonts = new RunFonts() { Ascii = fontName, HighAnsi = fontName, ComplexScript = fontName, EastAsia = fontName };
                        runProperties.Append(runFonts);
                    }
                    else
                    {
                        runFonts.Ascii = fontName;
                        runFonts.HighAnsi = fontName;
                        runFonts.ComplexScript = fontName;
                        runFonts.EastAsia = fontName;
                    }
                }
            }
        }

        public static void CreateSpacingBetweenLines(Body body)
        {
            foreach (Paragraph paragraph in body.Elements<Paragraph>())
            {
                // 创建段落属性对象，如果不存在
                paragraph.ParagraphProperties ??= new DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties();

                // 设置行距（1.5 倍行距）
                SpacingBetweenLines spacing = new SpacingBetweenLines()
                {
                    Line = "240", // 1.5 倍行距（240为单倍，因此1.5倍是240*1.5=360）
                    LineRule = LineSpacingRuleValues.Auto // 自动行间距
                };

                // 将行距添加到段落属性
                paragraph.ParagraphProperties.SpacingBetweenLines = spacing;
            }
        }

        /// <summary>
        /// 创建年度计划段落部分
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        private async Task GetAnnualPlanWord(string mainID, Body body)
        {
            //获取配置数据
            var icons = await _dictionaryService.GetIconsByModuleType(ICON_MODELTYPE_ANNUALPLAN);
            var AnnualPlanDatas = await GetAnnualPlanExportViews(mainID);
            CreateAnnualPlanWordTableTitle(body, "年度工作目标及项目指标");
            foreach (var annualPlan in AnnualPlanDatas)
            {
                body.AppendChild(CreateTitleParagraph(annualPlan.AnnualPlanTypeID + "、" + annualPlan.AnnualPlanTypeName));
                body = CreateMainGoal(annualPlan.MainGoals, body, icons);
            }
        }

        /// <summary>
        /// 创建Run中文字属性
        /// </summary>
        /// <param name="bold"></param>
        /// <param name="colorFlag"></param>
        /// <param name="properties"></param>
        /// <param name="fontSize"></param>
        /// <param name="fonts"></param>
        /// <returns></returns>
        private static RunProperties CreateRunProperties(bool bold, bool colorFlag, string colorValue, string fontSize, string fonts)
        {
            var runProperties = new RunProperties();
            if (bold)
            {
                runProperties.Append(new Bold());
            }
            if (colorFlag && !string.IsNullOrEmpty(colorValue))
            {
                runProperties.Append(new Color() { Val = colorValue });
            }
            runProperties.Append(new DocumentFormat.OpenXml.Wordprocessing.FontSize() { Val = string.IsNullOrEmpty(fontSize) ? "24" : fontSize });
            if (!string.IsNullOrEmpty(fonts))
            {
                runProperties.Append(new RunFonts() { Ascii = fonts, HighAnsi = fonts });
            }
            return runProperties;
        }

        /// <summary>
        /// 创建标题段落
        /// </summary>
        /// <param name="title"></param>
        /// <returns></returns>
        private static Paragraph CreateTitleParagraph(string title)
        {
            DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties paragraphProperties = new(new SpacingBetweenLines() { Line = "360" });
            var titleRun = new Run()
            {
                RunProperties = CreateRunProperties(true, true, "#FF0000", null, "")
            };
            titleRun.AppendChild(new Text(title));
            return new Paragraph(paragraphProperties, titleRun);
        }

        /// <summary>
        /// 创建年度计划表格部分
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task GetAnnualPlanWordTable(string mainID, Body body)
        {
            //获取配置数据
            var icons = await _dictionaryService.GetIconsByModuleType(ICON_MODELTYPE_ANNUALPLAN);
            var AnnualPlanDatas = await GetAnnualPlanExportViews(mainID);
            try
            {
                CreateAnnualPlanWordTableTitle(body, "2024年护理工作计划框架表");
                foreach (var AnnualPlanData in AnnualPlanDatas)
                {
                    body.AppendChild(CreateTitleParagraph(AnnualPlanData.AnnualPlanTypeID + "、" + AnnualPlanData.AnnualPlanTypeName));
                    foreach (var mainGoal in AnnualPlanData.MainGoals)
                    {
                        // 目标
                        var goalParagraph = new Paragraph();
                        var goalRun = new Run()
                        {
                            RunProperties = CreateRunProperties(true, false, null, null, "")
                        };
                        goalRun.AppendChild(new Text($"目标{mainGoal.Sort}：{mainGoal.GoalContent}"));
                        goalParagraph.AppendChild(goalRun);
                        body.AppendChild(goalParagraph);
                        //创建表
                        var table = InitializeGanttTable();
                        //标题行
                        var rowTitle = CreateTableTitleRow();
                        table.Append(rowTitle);
                        //添加数据
                        foreach (var group in mainGoal.Groups)
                        {
                            table.Append(CreateContentRow(group, icons));
                        }
                        body.AppendChild(table);
                    }
                }
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
            }
        }

        private void CreateAnnualPlanWordTableTitle(Body body, string text)
        {
            // 创建段落属性，并设置为居中
            DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties paragraphProperties = new DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties();
            Justification justification = new() { Val = JustificationValues.Center };
            paragraphProperties.Append(justification);

            // 创建文本运行属性，并设置为加粗
            RunProperties runProperties = new RunProperties();
            Bold bold = new Bold();
            // 12磅字体大小
            DocumentFormat.OpenXml.Wordprocessing.FontSize fontSize = new() { Val = "24" };
            runProperties.Append(bold);
            runProperties.Append(fontSize);

            // 创建文本运行并将文本添加到其中
            Run run = new Run();
            run.Append(runProperties);
            run.Append(new Text(text));

            // 创建段落并应用居中样式
            Paragraph paragraph = new Paragraph();
            paragraph.Append(paragraphProperties);
            paragraph.Append(run);

            // 将段落添加到文档主体
            body.AppendChild(paragraph);
        }

        /// <summary>
        /// 创建行
        /// </summary>
        /// <param name="group"></param>
        /// <param name="icons"></param>
        /// <returns></returns>
        private static TableRow CreateContentRow(APGroup group, List<AdministrationIconInfo> icons)
        {
            var row = new DocumentFormat.OpenXml.Wordprocessing.TableRow();
            // 创建单元格并填充内容
            DocumentFormat.OpenXml.Wordprocessing.TableCell cell1 = CreateCell(group.IndicatorDetails.Select(m => new KeyValueString { Key = $"{m.Sort}.{m.LocalShowName}", Value = m.MarkID }).ToList(), (9 * 567), icons);
            group.ProjectDetails = group.ProjectDetails == null ? [] : group.ProjectDetails;
            DocumentFormat.OpenXml.Wordprocessing.TableCell cell2 = CreateCell(group.ProjectDetails.Select(m => new KeyValueString { Key = $"{m.Sort}.{m.Content}", Value = m.MarkID }).ToList(), (14 * 567), icons);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cell3 = CreateCell(group.ResponsibleDepartments.Select(m => new KeyValueString { Key = m, Value = null }).ToList(), (4 * 567), icons);
            // 将单元格添加到行中
            row.Append(cell1, cell2, cell3);
            return row;
        }

        /// <summary>
        /// 创建表头列
        /// </summary>
        /// <returns></returns>
        private TableRow CreateTableTitleRow()
        {
            var rowTitle = new TableRow();
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle1 = CreateCellTitle("工作指标", (9 * 567), true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle2 = CreateCellTitle("工作项目", (14 * 567), true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle3 = CreateCellTitle("负责部门", (4 * 567), true, true);
            rowTitle.Append(cellTitle1, cellTitle2, cellTitle3);
            return rowTitle;
        }

        /// <summary>
        /// 创建单元格内容
        /// </summary>
        /// <param name="textLines"></param>
        /// <param name="width"></param>
        /// <param name="iconInfos"></param>
        /// <returns></returns>
        public static DocumentFormat.OpenXml.Wordprocessing.TableCell CreateCell(List<KeyValueString> textLines, int width, List<AdministrationIconInfo> iconInfos)
        {
            var cell = new DocumentFormat.OpenXml.Wordprocessing.TableCell();
            var tcp = new DocumentFormat.OpenXml.Wordprocessing.TableCellProperties(new TableCellWidth() { Type = TableWidthUnitValues.Dxa, Width = width.ToString() });
            cell.Append(tcp);

            foreach (var textLine in textLines)
            {
                var run = new Run();
                run.Append(new Text(textLine.Key));
                var paragraph = new Paragraph();

                if (!string.IsNullOrEmpty(textLine.Value))
                {
                    var iconInfo = iconInfos.FirstOrDefault(m => m.AdministrationIconID.ToString() == textLine.Value.Trim());
                    if (iconInfo != null)
                    {
                        string colorNum = iconInfos.FirstOrDefault(m => m.AdministrationIconID.ToString() == textLine.Value.Trim()).Color;
                        string iconImg = iconInfos.FirstOrDefault(m => m.AdministrationIconID.ToString() == textLine.Value.Trim()).Icon;
                        Run starRun = new Run();
                        starRun.Append(new Text(iconImg) { Space = SpaceProcessingModeValues.Preserve });
                        starRun.RunProperties = CreateRunProperties(false, true, colorNum, null, "");
                        paragraph.Append(run, starRun);
                    }
                }
                else
                {
                    paragraph.Append(run);
                }

                cell.Append(paragraph);
            }
            return cell;
        }

        /// <summary>
        /// 创建目标
        /// </summary>
        /// <param name="mainGoals"></param>
        /// <param name="body"></param>
        /// <param name="paragraphProperties"></param>
        private static Body CreateMainGoal(List<APMainGoal> mainGoals, Body body, List<AdministrationIconInfo> icons)
        {
            foreach (var mainGoal in mainGoals)
            {
                // 目标
                var goalParagraph = new Paragraph();
                // 创建加粗样式
                var goalRun = new Run()
                {
                    RunProperties = CreateRunProperties(true, false, null, null, "")
                };
                goalRun.AppendChild(new Text($"目标{mainGoal.Sort}：{mainGoal.GoalContent}"));
                goalParagraph.AppendChild(goalRun);
                body.AppendChild(goalParagraph);
                body = CreateIndicators(mainGoal.Groups, body, icons);
            }
            return body;
        }

        /// <summary>
        /// 创建指标
        /// </summary>
        /// <param name="group"></param>
        /// <param name="body"></param>
        /// <returns></returns>
        private static Body CreateIndicators(List<APGroup> group, Body body, List<AdministrationIconInfo> icons)
        {
            List<KeyValueString> indicators = [];
            int i = 1;
            foreach (var item in group)
            {
                foreach (var indicatorDetail in item.IndicatorDetails)
                {
                    indicators.Add(new KeyValueString { Key = i + ". " + indicatorDetail.LocalShowName + indicatorDetail.Operator + indicatorDetail.ReferenceValue + indicatorDetail.Unit, Value = indicatorDetail.MarkID });
                    i++;
                }
            }
            if (indicators.Count > 0)
            {
                foreach (var indicator in indicators)
                {
                    Paragraph indicatorParagraph = new()
                    {
                        ParagraphProperties = new DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties(new Indentation() { Start = "360" })
                    };

                    var indicatorRun = new Run();
                    indicatorRun.Append(new Text(indicator.Key));

                    var iconInfo = icons.FirstOrDefault(m => m.AdministrationIconID.ToString() == indicator.Value);
                    if (iconInfo != null)
                    {
                        string colorNum = iconInfo.Color;
                        string iconImg = iconInfo.Icon;
                        Run starRun = new Run();
                        starRun.Append(new Text(iconImg) { Space = SpaceProcessingModeValues.Preserve });
                        starRun.RunProperties = CreateRunProperties(false, true, colorNum, null, "");
                        indicatorParagraph.Append(indicatorRun, starRun);
                    }
                    else
                    {
                        indicatorParagraph.AppendChild(indicatorRun);
                    }
                    body.AppendChild(indicatorParagraph);
                }
            }
            return body;
        }

        /// <summary>
        /// 创建甘特图Word
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        public async Task GetAnnualPlanGanttWord(string mainID, Body body)
        {
            var datas = await GetPlanGanttData(mainID);
            try
            {
                CreateAnnualPlanWordTableTitle(body, "2024年护理工作推进甘特图");
                var datasGroup = datas.GroupBy(m => m.AnnualPlanInterventionMainID).ToList();
                //切页
                var chunkList = datas.Chunk(15);
                bool isFirstChunk = true;
                int itemSort = 1;
                foreach (var chunk in chunkList)
                {
                    if (!isFirstChunk)
                    {
                        // 在每个新的数据块之前添加分页符
                        var pageBreakParagraph = new Paragraph(new Run(new Break() { Type = BreakValues.Page }));
                        body.AppendChild(pageBreakParagraph);
                    }
                    //设置表格属性
                    var table = InitializeGanttTable();
                    //创建表头
                    table.Append(CreateTableHeader());
                    string previousContent = null;
                    foreach (var data in chunk)
                    {
                        var row = CreateGanttRow(data, previousContent, itemSort);
                        table.Append(row);
                        previousContent = data.MainGoalContent;
                        itemSort++;
                    }
                    body.AppendChild(table);
                    isFirstChunk = false;
                }
                //添加备注内容
                CreateGanttRemarkContent(body);
            }
            catch (Exception ex)
            {
                _logger.Error(ex);
            }
        }

        /// <summary>
        /// 创建备注内容
        /// </summary>
        /// <param name="body"></param>
        /// <returns></returns>
        private static Body CreateGanttRemarkContent(Body body)
        {
            Paragraph indicatorParagraph = new Paragraph
            {
                ParagraphProperties = new DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties()
            };
            var indicatorRun = new Run
            {
                RunProperties = CreateRunProperties(true, false, null, null, "")
            };
            indicatorRun.Append(new Text("备注：\"○\"表示计划项目，\"●\"表示已完成项目。"));
            indicatorParagraph.Append(indicatorRun);
            body.AppendChild(indicatorParagraph);

            return body;
        }

        /// <summary>
        /// 创建行数据
        /// </summary>
        /// <param name="data"></param>
        /// <param name="previousContent"></param>
        /// <param name="itemSort"></param>
        /// <param name="employees"></param>
        /// <returns></returns>
        private static TableRow CreateGanttRow(APInterventionDetailView data, string previousContent, int itemSort)
        {
            var row = new TableRow();
            bool isMergeStart = previousContent != data.MainGoalContent;
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellGoal = CreateCellWithVerticalMerge($"目标{data.Sort}：{data.MainGoalContent}", isMergeStart, 50);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellItem = CreateCellTitle($"{itemSort}.{data.ItemContent}", 15000, false, false);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellPrincipal = CreateCellTitle(
                string.IsNullOrEmpty(data.PrincipalGroupName) ? data.PrincipalName : data.PrincipalGroupName, 3000, false, false);
            row.Append(cellGoal, cellItem);
            foreach (var item in data.PlanMonthDictionary)
            {
                DocumentFormat.OpenXml.Wordprocessing.TableCell cellDic = CreateGanttCell(item.Value, 1000);
                row.Append(cellDic);
            }
            // 将单元格添加到行中
            row.Append(cellPrincipal);
            return row;
        }

        /// <summary>
        /// 初始化表格
        /// </summary>
        /// <returns></returns>
        private static Table InitializeGanttTable()
        {
            var table = new Table();
            //设置表格属性
            table.Append(new DocumentFormat.OpenXml.Wordprocessing.TableProperties(
                new TableBorders(
                    new TopBorder() { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new BottomBorder() { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new LeftBorder() { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new RightBorder() { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new DocumentFormat.OpenXml.Wordprocessing.InsideHorizontalBorder() { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 },
                    new DocumentFormat.OpenXml.Wordprocessing.InsideVerticalBorder() { Val = new EnumValue<BorderValues>(BorderValues.Single), Size = 12 }
                )
            ));
            return table;
        }

        /// <summary>
        /// 创建甘特图表头
        /// </summary>
        /// <returns></returns>
        private TableRow CreateTableHeader()
        {
            var rowTitle = new TableRow();
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitleGoal = CreateCellTitle("所属目标", 50, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitleItem = CreateCellTitle("工作项目", 15000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle1 = CreateCellTitle("1月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle2 = CreateCellTitle("2月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle3 = CreateCellTitle("3月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle4 = CreateCellTitle("4月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle5 = CreateCellTitle("5月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle6 = CreateCellTitle("6月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle7 = CreateCellTitle("7月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle8 = CreateCellTitle("8月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle9 = CreateCellTitle("9月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle10 = CreateCellTitle("10月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle11 = CreateCellTitle("11月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitle12 = CreateCellTitle("12月", 1000, true, true);
            DocumentFormat.OpenXml.Wordprocessing.TableCell cellTitlePerson = CreateCellTitle("负责人", 3000, true, true);
            rowTitle.Append(cellTitleGoal, cellTitleItem, cellTitle1, cellTitle2, cellTitle3, cellTitle4, cellTitle5, cellTitle6, cellTitle7, cellTitle8, cellTitle9, cellTitle10, cellTitle11, cellTitle12, cellTitlePerson);
            return rowTitle;
        }

        /// <summary>
        /// 创建表头单元格
        /// </summary>
        /// <param name="text"></param>
        /// <param name="width"></param>
        /// <param name="isBold"></param>
        /// <param name="isCentered"></param>
        /// <returns></returns>
        public static DocumentFormat.OpenXml.Wordprocessing.TableCell CreateCellTitle(string text, int width, bool isBold, bool isCentered)
        {
            var cell = new DocumentFormat.OpenXml.Wordprocessing.TableCell();

            // 设置单元格宽度
            var tcp = new DocumentFormat.OpenXml.Wordprocessing.TableCellProperties(
                new TableCellWidth { Type = TableWidthUnitValues.Dxa, Width = width.ToString() }
            );

            // 设置垂直对齐为居中
            var verticalAlignment = new TableCellVerticalAlignment() { Val = TableVerticalAlignmentValues.Center };
            tcp.Append(verticalAlignment);

            cell.Append(tcp);

            // 创建RunProperties元素来设置格式
            var run = new Run()
            {
                RunProperties = CreateRunProperties(isBold, false, null, null, "")
            };

            run.Append(new Text(text));
            // 设置段落属性
            Paragraph paragraph = new Paragraph(run);

            // 设置水平对齐为居中
            if (isCentered)
            {
                paragraph.ParagraphProperties = new DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties(
                    new Justification { Val = JustificationValues.Center }
                );
            }

            cell.Append(paragraph);

            return cell;
        }

        /// <summary>
        /// 合并单元格
        /// </summary>
        /// <param name="content"></param>
        /// <param name="isMergeStart"></param>
        /// <param name="width"></param>
        /// <returns></returns>

        private static DocumentFormat.OpenXml.Wordprocessing.TableCell CreateCellWithVerticalMerge(string content, bool isMergeStart, int width)
        {
            // 创建单元格和段落属性
            var cell = new DocumentFormat.OpenXml.Wordprocessing.TableCell();
            var tcp = new DocumentFormat.OpenXml.Wordprocessing.TableCellProperties();
            var tcWidth = new TableCellWidth() { Type = TableWidthUnitValues.Dxa, Width = width.ToString() };
            tcp.Append(tcWidth);

            var vm = new VerticalMerge();
            if (isMergeStart)
            {
                vm.Val = MergedCellValues.Restart;
            }
            else
            {
                vm.Val = MergedCellValues.Continue;
            }
            tcp.Append(vm);

            // 设置单元格对齐方式为居中
            var verticalAlignment = new TableCellVerticalAlignment() { Val = TableVerticalAlignmentValues.Center };
            tcp.Append(verticalAlignment);

            // 创建并设置文本样式
            var run = new Run()
            {
                RunProperties = CreateRunProperties(true, false, null, null, "")
            };

            var text = new Text(content);
            run.Append(text);

            var paragraph = new Paragraph(run);
            var paragraphProperties = new DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties();

            // 设置段落水平居中
            var justification = new Justification() { Val = JustificationValues.Center };
            paragraphProperties.Append(justification);
            paragraph.Append(paragraphProperties);

            cell.Append(tcp);
            cell.Append(paragraph);

            return cell;
        }

        /// <summary>
        /// 创建甘特图单元格
        /// </summary>
        /// <param name="pair"></param>
        /// <param name="width"></param>
        /// <returns></returns>
        public static DocumentFormat.OpenXml.Wordprocessing.TableCell CreateGanttCell(bool pair, int width)
        {
            var cell = new DocumentFormat.OpenXml.Wordprocessing.TableCell();
            var tcp = new DocumentFormat.OpenXml.Wordprocessing.TableCellProperties(
                new TableCellWidth() { Type = TableWidthUnitValues.Dxa, Width = width.ToString() },
                new TableCellVerticalAlignment() { Val = TableVerticalAlignmentValues.Center } // 垂直居中
            );
            cell.Append(tcp);

            var run = new Run();
            run.Append(new Text(pair ? "○" : ""));
            var paragraph = new Paragraph
            {
                ParagraphProperties = new DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties(
                new Justification { Val = JustificationValues.Center } // 水平居中
            )
            };
            paragraph.Append(run);
            cell.Append(paragraph);

            return cell;
        }

        /// <summary>
        /// 获取年度计划甘特图数据
        /// </summary>
        /// <param name="mainID"></param>
        /// <returns></returns>
        private async Task<List<APInterventionDetailView>> GetPlanGanttData(string mainID)
        {
            var goalKeyValues = await _mainGoalRepository.GetAnnualPlanMainList(mainID);
            if (!goalKeyValues.Any())
            {
                return null;
            }

            var detailViews = await _annualPlanInterventionMainRepository.GetDetailByMainID(mainID);
            var interventionMainIDs = detailViews.Select(m => m.AnnualPlanInterventionMainID).Distinct().ToList();
            var interventionMainPrincipals = await _annualInterventionMainPrincipalRepository.GetPrincipalIDsByMainIDs(interventionMainIDs);
            var excelContainer = new List<APInterventionDetailView>();

            int goalSort = 1;
            foreach (var goalKeyValue in goalKeyValues)
            {
                var itemsUnderGoal = detailViews.Where(m => m.AnnualPlanMainGoalID == goalKeyValue.Key).OrderBy(m => m.Sort);
                var groupedItems = itemsUnderGoal.GroupBy(m => m.ProjectDetailID);

                foreach (var group in groupedItems)
                {
                    var firstItem = group.First();
                    var excelModel = new APInterventionDetailView
                    {
                        MainGoalContent = goalKeyValue.Value,
                        ItemContent = firstItem.ItemContent,
                        PrincipalGroupName = firstItem.PrincipalGroupName,
                        Sort = goalSort,
                        PlanMonthDictionary = Enumerable.Range(1, 12).ToDictionary(month => month, month => group.Any(item => item.PlanMonth == month))
                    };
                    interventionMainPrincipals.TryGetValue(firstItem.AnnualPlanInterventionMainID, out var principalIDs);
                    if (string.IsNullOrEmpty(excelModel.PrincipalGroupName) && principalIDs != null && principalIDs.Length != 0)
                    {
                        var principals = await _employeePersonalDataRepository.GetDataByEmployeeIDs(principalIDs);
                        excelModel.PrincipalName = principals.Count != 0 ? string.Join("，", principals.Select(m => m.Value)) : string.Empty;
                    }

                    excelContainer.Add(excelModel);
                }
                if (groupedItems.Any())
                {
                    goalSort++;
                }
            }

            return excelContainer;
        }

        /// <summary>
        /// 插入分页符
        /// </summary>
        /// <param name="mainPart"></param>
        private static void AddPageBreakToDocument(Body body)
        {
            Paragraph pageBreakParagraph = new(new Run(new Break() { Type = BreakValues.Page }));
            body.Append(pageBreakParagraph);
        }

        /// <summary>
        /// 添加图片到文档（没用到先不删，后续如果有图片需要插入可以直接用）
        /// </summary>
        /// <param name="mainPart"></param>
        /// <param name="imagePath"></param>
        /// <param name="imageCounter"></param>
        private static void AddImageToDocument(MainDocumentPart mainPart, string imagePath, int imageCounter)
        {
            // 添加图片部分
            ImagePart imagePart = mainPart.AddImagePart(ImagePartType.Png);

            // 将图片数据复制到 ImagePart
            using (var stream = new FileStream(imagePath, FileMode.Open))
            {
                imagePart.FeedData(stream);
            }
            var key = mainPart.GetIdOfPart(imagePart);
            // 添加图片到文档主体
            AddImageToBody(mainPart, key, imageCounter);
        }

        /// <summary>
        /// 添加图片到文档
        /// </summary>
        /// <param name="mainPart"></param>
        /// <param name="relationshipId"></param>
        /// <param name="imageCounter"></param>
        private static void AddImageToBody(MainDocumentPart mainPart, string relationshipId, int imageCounter)
        {
            long widthInEmus = (long)(21.0 * 360000); // A4纸宽度
            long heightInEmus = (long)(29.7 * 360000); // A4纸高度

            // 获取文档主体部分
            Body body = mainPart.Document.Body;
            // 创建一个图像元素
            var image = new DocumentFormat.OpenXml.Office.Drawing.Drawing();
            A.Wordprocessing.Anchor anchor = new()
            {
                DistanceFromTop = 0U,
                DistanceFromBottom = 0U,
                DistanceFromLeft = 0U,
                DistanceFromRight = 0U,
                SimplePos = false,
                RelativeHeight = (UInt32Value)0U,
                BehindDoc = true,
                Locked = false,
                LayoutInCell = true,
                AllowOverlap = true,
                HorizontalPosition = new HorizontalPosition()
                {
                    RelativeFrom = HorizontalRelativePositionValues.Page
                },
                VerticalPosition = new VerticalPosition()
                {
                    RelativeFrom = VerticalRelativePositionValues.Page
                },
                Extent = new Extent() { Cx = widthInEmus, Cy = heightInEmus },
                EffectExtent = new EffectExtent()
                {
                    LeftEdge = 0L,
                    TopEdge = 0L,
                    RightEdge = 0L,
                    BottomEdge = 0L
                }
            };
            // 创建图形框架，设置图片位置
            var docProps = new DocProperties() { Id = UInt32Value.FromUInt32((uint)imageCounter), Name = $"Picture {imageCounter}" };
            anchor.Append(docProps);

            var graphicFrameLocks = new GraphicFrameLocks() { NoChangeAspect = true };
            anchor.Append(graphicFrameLocks);

            anchor.Append(new A.Graphic(
                new A.GraphicData(
                    new PIC.Picture(
                        new PIC.NonVisualPictureProperties(
                            new PIC.NonVisualDrawingProperties() { Id = UInt32Value.FromUInt32((uint)imageCounter), Name = $"Picture {imageCounter}" }
                            , new PIC.NonVisualPictureDrawingProperties()), new PIC.BlipFill(
                                new A.Blip() { Embed = relationshipId }, new A.Stretch(new A.FillRectangle())), new PIC.ShapeProperties(new A.Transform2D(new A.Offset() { X = 0L, Y = 0L },
                                new A.Extents()
                                {
                                    Cx = widthInEmus,
                                    Cy = heightInEmus
                                }), new A.PresetGeometry(new A.AdjustValueList()) { Preset = A.ShapeTypeValues.Rectangle })))
                { Uri = "http://schemas.openxmlformats.org/drawingml/2006/picture" }));
            image.Append(anchor);
            // 添加图像到文档
            Paragraph paragraph = new(new Run(image));
            body.Append(paragraph);
        }

        /// <summary>
        /// 生成季度计划Word文档
        /// </summary>
        /// <param name="quarterPlanMainID">季度计划主表ID</param>
        /// <param name="showRoutineWorks">是否显示常规工作</param>
        /// <returns></returns>
        public async Task<string> GenerateQuarterPlanWordDocAsync(string quarterPlanMainID, bool showRoutineWorks)
        {
            string url = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "AnnualPlanDocumentPath");
            if (string.IsNullOrEmpty(url))
            {
                return null;
            }
            var quarterPlan = await _quarterPlanMaintainRepository.GetExportView(quarterPlanMainID);
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            foreach (var typeGroup in quarterPlan.PlanTypes)
            {
                typeGroup.TypeName = typeList.Find(m => m.AnnualPlanTypeID == typeGroup.TypeID)?.AnnualPlanTypeContent;
            }
            var documentName = Guid.NewGuid().ToString().Replace("-", "") + ".docx";
            var outputPath = AppContext.BaseDirectory + @"\files\" + documentName;
            using (var document = WordprocessingDocument.Create(outputPath, WordprocessingDocumentType.Document))
            {
                var mainPart = document.AddMainDocumentPart();
                mainPart.Document = new Document(new Body());
                SetPageMargins(mainPart.Document.Body);
                var table = CreateTableWithGrid();
                mainPart.Document.Body.Append(table);
                table.Append(CreateHeaderRow());
                await FillTableData(table, quarterPlan, showRoutineWorks);
                mainPart.Document.Save();
            }
            return url + documentName;
        }

        /// <summary>
        /// 设置页面边距
        /// </summary>
        /// <returns></returns>
        private Table CreateTableWithGrid()
        {
            var table = new Table();
            table.Append(new TableProperties(
                new TableWidth() { Width = TotalTableWidth.ToString(), Type = TableWidthUnitValues.Dxa },
                new TableBorders(
                    new TopBorder() { Val = BorderValues.Single, Size = 4 },
                    new BottomBorder() { Val = BorderValues.Single, Size = 4 },
                    new LeftBorder() { Val = BorderValues.Single, Size = 4 },
                    new RightBorder() { Val = BorderValues.Single, Size = 4 },
                    new DocumentFormat.OpenXml.Wordprocessing.InsideHorizontalBorder() { Val = BorderValues.Single, Size = 4 },
                    new DocumentFormat.OpenXml.Wordprocessing.InsideVerticalBorder() { Val = BorderValues.Single, Size = 4 }
                )
            ));
            table.Append(new DocumentFormat.OpenXml.Wordprocessing.TableGrid(
                new DocumentFormat.OpenXml.Wordprocessing.GridColumn() { Width = _columnWidths[0].ToString() },
                new DocumentFormat.OpenXml.Wordprocessing.GridColumn() { Width = _columnWidths[1].ToString() },
                new DocumentFormat.OpenXml.Wordprocessing.GridColumn() { Width = _columnWidths[2].ToString() },
                new DocumentFormat.OpenXml.Wordprocessing.GridColumn() { Width = _columnWidths[3].ToString() }
            ));
            return table;
        }

        /// <summary>
        /// 创建表头行
        /// </summary>
        /// <returns></returns>
        private static TableRow CreateHeaderRow()
        {
            var headerRow = new TableRow();
            string[] headers = { "分类", "工作重点", "完成要求", "负责人" };
            foreach (var headerText in headers)
            {
                var cell = new TableCell();
                cell.Append(new TableCellProperties(
                    new TableCellVerticalAlignment()
                    {
                        Val = TableVerticalAlignmentValues.Center
                    }
                ));
                var paragraph = new Paragraph(
                    new DocumentFormat.OpenXml.Wordprocessing.ParagraphProperties(
                        new Justification()
                        {
                            Val = JustificationValues.Center // 水平居中
                        }
                    ),
                    new Run(
                        new Text(headerText),
                    new RunProperties(new Bold())
                    )
                );
                cell.Append(paragraph);
                headerRow.Append(cell);
            }
            headerRow.PrependChild(new TableRowProperties(
                new TableRowHeight()
                {
                    Val = 450,
                    HeightType = HeightRuleValues.Exact
                }
            ));
            return headerRow;
        }

        /// <summary>
        /// 填充表格数据
        /// </summary>
        /// <param name="table">表格</param>
        /// <param name="data">数据</param>
        /// <param name="showRoutineWorks">是否显示常规工作</param>
        private async Task FillTableData(Table table, QuarterPlanExportView exportView, bool showRoutineWorks)
        {
            var principals = await _employeePersonalDataRepository.GetAllEmployees();
            var planTypes = exportView.PlanTypes;
            foreach (var typeGroup in planTypes)
            {
                if (typeGroup.PlanWorks == null || typeGroup.PlanWorks.Length == 0) continue;
                if (!showRoutineWorks)
                {
                    typeGroup.PlanWorks = [.. typeGroup.PlanWorks.Where(m => m.WorkType == AnnualPlanEnums.WorkType.Key)];
                }
                if (typeGroup.PlanWorks == null || typeGroup.PlanWorks.Length == 0) continue;
                var firstRow = CreateDataRow(
                    typeGroup.TypeName,
                    typeGroup.PlanWorks[0].WorkContent,
                    typeGroup.PlanWorks[0].Requirement,
                    typeGroup.PlanWorks[0].PrincipalName,
                    isFirstRow: true
                );
                table.Append(firstRow);
                for (int i = 1; i < typeGroup.PlanWorks.Length; i++)
                {
                    var row = CreateDataRow(
                        verticalMerge: true,
                        workContent: typeGroup.PlanWorks[i].WorkContent,
                        requirement: typeGroup.PlanWorks[i].Requirement,
                        principal: typeGroup.PlanWorks[i].PrincipalName
                    );
                    table.Append(row);
                }
            }
        }

        /// <summary>
        /// 创建数据行
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="workContent">工作重点</param>
        /// <param name="requirement">完成要求</param>
        /// <param name="principal">负责人</param>
        /// <param name="isFirstRow">是否第一行</param>
        /// <param name="verticalMerge">是否合并</param>
        /// <returns></returns>
        private TableRow CreateDataRow(string type = "", string workContent = "", string requirement = "", string principal = "", bool isFirstRow = false, bool verticalMerge = false)
        {
            var row = new TableRow();
            var typeCell = verticalMerge ?
                CreateMergedCell() :
                CreateCell(type, _columnWidths[0], mergeStart: isFirstRow);
            row.Append(
                typeCell,
                CreateCell(workContent, _columnWidths[1]),
                CreateCell(requirement, _columnWidths[2]),
                CreateCell(principal, _columnWidths[3])
            );

            return row;
        }

        /// <summary>
        /// 创建单元格
        /// </summary>
        /// <param name="text">文本内容</param>
        /// <param name="width">宽度</param>
        /// <param name="isHeader">是否标题</param>
        /// <param name="mergeStart">合并开始列</param>
        /// <returns></returns>
        private static TableCell CreateCell(string text, int width, bool isHeader = false, bool mergeStart = false)
        {
            var cell = new TableCell();
            cell.Append(new TableCellProperties(
                new TableCellWidth() { Type = TableWidthUnitValues.Dxa, Width = width.ToString() }
            ));
            if (mergeStart)
            {
                cell.Append(new TableCellProperties(
                    new VerticalMerge() { Val = MergedCellValues.Restart }
                ));
            }
            var paragraph = new Paragraph();
            var run = new Run();
            if (isHeader)
            {
                run.PrependChild(new RunProperties(new Bold()));
            }
            run.AppendChild(new Text(text));
            paragraph.Append(run);
            cell.Append(paragraph);
            return cell;
        }

        /// <summary>
        /// 创建合并单元格
        /// </summary>
        /// <returns></returns>
        private static TableCell CreateMergedCell()
        {
            return new TableCell(
                new TableCellProperties(
                    new VerticalMerge() { Val = MergedCellValues.Continue }
                )
            );
        }

        /// <summary>
        /// 设置页面边距
        /// </summary>
        /// <param name="body">页面主题</param>
        private static void SetPageMargins(Body body)
        {
            var sectionProps = new SectionProperties(
                new PageMargin()
                {
                    Top = 720,
                    Bottom = 720,
                    Left = 720,
                    Right = 720,
                    Header = 360,
                    Footer = 360
                }
            );
            body.Append(sectionProps);
        }

        /// <summary>
        /// 生成月度计划Word文档
        /// </summary>
        /// <param name="monthlyPlanMainID">月度计划主表ID</param>
        /// <param name="showRoutineWorks">是否显示常规工作</param>
        /// <returns></returns>
        public async Task<string> GenerateMonthlyPlanWordDocAsync(string monthlyPlanMainID, bool showRoutineWorks)
        {
            string url = await _appConfigSettingRepository.GetConfigSettingValue("Configs", "AnnualPlanDocumentPath");
            if (string.IsNullOrEmpty(url))
            {
                return null;
            }
            var monthlyPlan = await _monthlyPlanMaintainRepository.GetExportView(monthlyPlanMainID);
            var typeList = await _typeListRepository.GetAll<AnnualPlanTypeListInfo>();
            foreach (var typeGroup in monthlyPlan.PlanTypes)
            {
                typeGroup.TypeName = typeList.Find(m => m.AnnualPlanTypeID == typeGroup.TypeID)?.AnnualPlanTypeContent;
            }
            var documentName = Guid.NewGuid().ToString().Replace("-", "") + ".docx";
            var outputPath = AppContext.BaseDirectory + @"\files\" + documentName;
            using (var document = WordprocessingDocument.Create(outputPath, WordprocessingDocumentType.Document))
            {
                var mainPart = document.AddMainDocumentPart();
                mainPart.Document = new Document(new Body());
                SetPageMargins(mainPart.Document.Body);
                var table = CreateTableWithGrid();
                mainPart.Document.Body.Append(table);
                table.Append(CreateHeaderRow());
                await FillMonthlyTableData(table, monthlyPlan, showRoutineWorks);
                mainPart.Document.Save();
            }
            return url + documentName;
        }

        /// <summary>
        /// 填充月度计划表格数据
        /// </summary>
        /// <param name="table">表格</param>
        /// <param name="data">数据</param>
        /// <param name="showRoutineWorks">是否显示常规工作</param>
        private async Task FillMonthlyTableData(Table table, MonthlyPlanExportView exportView, bool showRoutineWorks)
        {
            var planTypes = exportView.PlanTypes;
            foreach (var typeGroup in planTypes)
            {
                if (typeGroup.PlanWorks == null || typeGroup.PlanWorks.Length == 0) continue;
                if (!showRoutineWorks)
                {
                    typeGroup.PlanWorks = [.. typeGroup.PlanWorks.Where(m => m.WorkType == AnnualPlanEnums.WorkType.Key)];
                }
                if (typeGroup.PlanWorks == null || typeGroup.PlanWorks.Length == 0) continue;
                var firstRow = CreateDataRow(
                    typeGroup.TypeName,
                    typeGroup.PlanWorks[0].WorkContent,
                    typeGroup.PlanWorks[0].Requirement,
                    typeGroup.PlanWorks[0].PrincipalName,
                    isFirstRow: true
                );
                table.Append(firstRow);
                for (int i = 1; i < typeGroup.PlanWorks.Length; i++)
                {
                    var row = CreateDataRow(
                        verticalMerge: true,
                        workContent: typeGroup.PlanWorks[i].WorkContent,
                        requirement: typeGroup.PlanWorks[i].Requirement,
                        principal: typeGroup.PlanWorks[i].PrincipalName
                    );
                    table.Append(row);
                }
            }
        }
    }
}
