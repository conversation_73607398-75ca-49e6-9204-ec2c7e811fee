﻿using Arch.EntityFrameworkCore.UnitOfWork;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Commands;
using System.Text;

namespace NursingManagement.Services;

public class AnnualScheduleService(
    ITieredTaskRepository mainRepository,
    IAnnualScheduleDetailRepository detailRepository,
    IAnnualPlanMainRepository annualPlanMainRepository,
    IUnitOfWork unitOfWork,
    IMessageService messageService,
    IEmployeePersonalDataRepository employeePersonalDataRepository,
    IEmployeeStaffDataRepository employeeStaffDataRepository,
    IDepartmentListRepository departmentListRepository,
    IAnnualPlanTypeListRepository annualPlanTypeListRepository
        ) : IAnnualScheduleService
{

    private readonly Logger _logger = LogManager.GetCurrentClassLogger();

    /// <summary>
    /// 护理部ID
    /// </summary>
    private const int DEPARTMENT_ID_405 = 405;
    /// <summary>
    /// 护理委员会
    /// </summary>
    private const int DEPARTMENT_ID_53 = 53;

    #region 查询
    public async Task<List<TaskStatisticsView>> GetAnnualScheduleStatisticsAsync(int scheduleYear, string schedulePerformer)
    {
        if (scheduleYear == 0 || string.IsNullOrEmpty(schedulePerformer))
        {
            _logger.Error($"查询年度计划执行统计信息错误，入参异常。scheduleYear = {scheduleYear}，schedulePerformer = {schedulePerformer}");
            throw new CustomException("获取年度计划执行统计信息失败，请联系管理员！");
        }

        var list = await mainRepository.GetTasksMonthlyCountViews(schedulePerformer, scheduleYear);
        var nonTasksMonthStatisticsView = Enumerable.Range(1, 12).Except(list.Select(m => m.Month))
            .Select(m => new TaskStatisticsView
            {
                Month = m,
                ProcessedCount = 0,
                TotalCount = 0
            });
        var views = list.Concat(nonTasksMonthStatisticsView).ToList();
        return views;
    }

    public async Task<AnnualScheduleMainView[]> GetTasksByMonth(int scheduleYear, int scheduleMonth, int currentDepartmentID, string employeeID)
    {
        if (scheduleMonth == 0)
        {
            throw new ArgumentException($"参数检查异常：scheduleMonth = {scheduleMonth}");
        }
        var tasks = await mainRepository.GetTieredTaskViews(scheduleYear, scheduleMonth, currentDepartmentID, employeeID);
        if (tasks.Length == 0)
        {
            return [];
        }
        var typeList = await annualPlanTypeListRepository.GetAll<AnnualPlanTypeListInfo>();
        var upperDepartmentIDs = await GetUpperDepartmentIDs(currentDepartmentID);
        var departmentList = await departmentListRepository.GetAllDictAsync();
        // 补充名称和要求，优先使用本部门的，没有再选择上级部门的
        foreach (var task in tasks)
        {
            var relation = task.MonthlyWorkToTasks.FirstOrDefault(n => n.DepartmentID == currentDepartmentID);
            // 向上查找，从最近的上级部门开始
            foreach (var upperDepartmentID in upperDepartmentIDs)
            {
                relation ??= task.MonthlyWorkToTasks.FirstOrDefault(n => n.DepartmentID == upperDepartmentID);
            }
            if (relation == null)
            {
                _logger.Error($"任务找不到对应的关系，请确认原因，AnnualScheduleMainID={task.AnnualScheduleMainID}，departmentID={currentDepartmentID}");
                throw new CustomException("找不到任务内容！请联系管理员");
            }
            task.TypeName = typeList.FirstOrDefault(m => m.AnnualPlanTypeID == relation.PlanTypeID)?.AnnualPlanTypeContent ?? "";
            task.TaskContent = relation.WorkContent;
            // 拼接部门及其要求，本部门的不拼接部门名称
            task.Requirement = string.Join("<br/>", task.MonthlyWorkToTasks.Where(m => !string.IsNullOrEmpty(m.Requirement)).Select(m =>
            {
                var departmentName = m.DepartmentID == currentDepartmentID ? null : departmentList.Find(d => d.DepartmentID == m.DepartmentID)?.DepartmentContent;
                return $"{(departmentName is null ? "" : "：")}{m.Requirement}";
            }));
        }
        return tasks;
    }

    public async Task<string> GetTaskSource(string taskID)
    {
        var departmentIDs = await mainRepository.GetTaskSourceDepartmentIDs(taskID) ?? throw new CustomException("查询计划任务发生异常，请联系管理员！");
        // TODO：改为月度计划展出后，可能不再需要手动增加任务的逻辑，此处暂时屏蔽，待沟通后决定是否保留
        //if (interventionID == 0)
        //{
        //    var employee = await _employeePersonalDataRepository.GetEmployeeNameByID(schedule.AddEmployeeID);
        //    var result = $"{(string.IsNullOrEmpty(employee) ? $"{employee}于" : "")}{schedule.AddDateTime:yyyy-MM-dd HH:mm}手动加入";
        //    return [result];
        //}
        if (departmentIDs.Length == 0)
        {
            _logger.Error($"任务ID为{taskID}的任务没有来源信息！");
            throw new CustomException("找不到任务来源，请联系管理员！");
        }
        var departmentList = await departmentListRepository.GetAllDictAsync();
        var sources = departmentIDs.Select(m => $"{departmentList.Find(n => n.DepartmentID == m)?.DepartmentContent}月度计划");
        var sourcesStr = string.Join("<br />", sources);
        return sourcesStr;
    }

    public async Task<List<AnnualScheduleDetailView>> GetAnnualScheduleDetailsAsync(string scheduleMainID)
    {
        return await detailRepository.GetAnnualScheduleDetailViewsAsync(scheduleMainID);
    }

    public async Task<AnnualScheduleMainView[]> GetUnExecTasks(string employeeID, int departmentID, int? scheduleMonth, bool preOrNextFlag)
    {
        // 获取本月之后未执行的年度计划任务
        var unPerformTaskViews = await mainRepository.GetUnPerformTaskViews(employeeID, DateTime.Now.Year, scheduleMonth, departmentID, preOrNextFlag);

        var upperDepartmentIDs = await GetUpperDepartmentIDs(departmentID);
        // 补充名称，优先使用本部门的，没有再选择上级部门的
        foreach (var unPerformTaskView in unPerformTaskViews)
        {
            var relation = unPerformTaskView.MonthlyWorkToTasks.FirstOrDefault(n => n.DepartmentID == departmentID);
            // 向上查找，从最近的上级部门开始
            foreach (var upperDepartmentID in upperDepartmentIDs)
            {
                relation ??= unPerformTaskView.MonthlyWorkToTasks.FirstOrDefault(n => n.DepartmentID == upperDepartmentID);
            }
            unPerformTaskView.TaskContent = relation.WorkContent;
        }

        return [.. unPerformTaskViews.OrderBy(m => m.ScheduleDateTime)];
    }
    
    /// <summary>
    /// 获取上级部门ID集合
    /// </summary>
    /// <param name="departmentID">当前部门ID</param>
    /// <returns></returns>
    private async Task<HashSet<int>> GetUpperDepartmentIDs(int departmentID)
    {
        var upperDepartmentIDs = await departmentListRepository.GetUpperDepartmentRecursion<int, HashSet<int>>(departmentID, m => m.DepartmentID);
        var committeeDepartments = await departmentListRepository.GetLowerDepartments(DEPARTMENT_ID_53);
        upperDepartmentIDs.Add(DEPARTMENT_ID_405);
        committeeDepartments.ForEach(m => upperDepartmentIDs.Add(m.DepartmentID));
        return upperDepartmentIDs;
    }
    #endregion

    #region 命令
    public async Task<bool> PerformTask(PerformTaskDto performTaskDto, string employeeID)
    {
        if (performTaskDto == null)
        {
            _logger.Error("SaveAnnualScheduleAsync方法参数异常,为空");
            throw new CustomException("执行失败，请联系管理员");
        }
        var scheduleMainInfo = await mainRepository.GetTieredTaskByID(performTaskDto.AnnualScheduleMainID);
        if (scheduleMainInfo == null)
        {
            _logger.Error($"无法获取计划任务ScheduleMainID = {performTaskDto.AnnualScheduleMainID}");
            throw new CustomException("执行失败，请联系管理员");
        }

        scheduleMainInfo.StatusCode = performTaskDto.StatusCode;
        scheduleMainInfo.PerformDateTime = performTaskDto.PerformDateTime;
        scheduleMainInfo.Performer = employeeID;
        scheduleMainInfo.DelayReason = performTaskDto.DelayReason;
        scheduleMainInfo.Remark = performTaskDto.Remark;
        scheduleMainInfo.Modify(employeeID);

        var scheduleDetails = CreateScheduleDetailInfos(performTaskDto, employeeID);
        await unitOfWork.GetRepository<AnnualScheduleDetailInfo>().InsertAsync(scheduleDetails);

        return await unitOfWork.SaveChangesAsync() >= 0;
    }

    /// <summary>
    /// 根据执行明细生成Model
    /// </summary>
    /// <param name="scheduleParamsView">入参</param>
    /// <param name="employeeID">执行人</param>
    /// <returns></returns>
    private static List<AnnualScheduleDetailInfo> CreateScheduleDetailInfos(PerformTaskDto scheduleParamsView, string employeeID)
    {
        var detailInfos = new List<AnnualScheduleDetailInfo>();
        scheduleParamsView.ScheduleDetails.ForEach(detail =>
        {
            var detailInfo = new AnnualScheduleDetailInfo
            {
                InterventionDetailID = detail.InterventionDetailID,
                InterventionID = detail.InterventionID,
                AnnualScheduleMainID = scheduleParamsView.AnnualScheduleMainID,
                DeleteFlag = ""
            };
            // TODO 此处存在新增和修改字段，那么修改操作应该如何处理
            detailInfo.Add(employeeID);
            detailInfo.Modify(employeeID);
            detailInfo.AnnualScheduleDetailID = detailInfo.GetId();
            detailInfos.Add(detailInfo);
        });

        return detailInfos;

    }

    public async Task<(bool, string message)> DeleteAnnualScheduleAsync(string scheduleMainID, string employeeID)
    {
        var scheduleMainInfo = await mainRepository.GetTieredTaskByID(scheduleMainID);
        if (scheduleMainInfo == null)
        {
            return (false, "查找不到需要删除的任务，请刷新页面查看一下。");
        }
        if (scheduleMainInfo.PerformDateTime is not null)
        {
            return (false, "任务已执行，不能删除！");
        }
        scheduleMainInfo.Delete(employeeID);
        var scheduleDetailInfos = await detailRepository.GetAnnualScheduleDetailsAsync(scheduleMainID, false);
        scheduleDetailInfos?.ForEach(info => info.Delete(employeeID));
        return (await unitOfWork.SaveChangesAsync() > 0, null);
    }

    public async Task<bool> CreateOrUpdateTasks(CreateOrUpdateTasksCommand cmd)
    {
        // 根据执行人、时间查询任务
        var tasks = await mainRepository.GetMonthlyTasks(cmd.Year, cmd.Month, cmd.HospitalID);
        // 构造创建任务的方法
        var addTask = CreateFuncForAddTask(cmd, tasks);
        // 尝试将计划工作转为任务（或关系）并新增之
        cmd.Works.ForEach(work =>
            work.PrincipalIDs?.ForEach(async principal => await addTask(work, principal))
        );

        return await unitOfWork.SaveChangesAsync() >= 0;
    }

    /// <summary>
    /// 为新增任务创建方法
    /// </summary>
    /// <param name="cmd">新增视图</param>
    /// <param name="tasks">任务列表</param>
    /// <param name="currentTime">当前时间</param>
    /// <returns></returns>
    private Func<MonthlyPlanWorkVo, string, Task> CreateFuncForAddTask(CreateOrUpdateTasksCommand cmd, List<AnnualScheduleMainInfo> tasks)
    {
        var currentTime = DateTime.Now;
        var scheduleDate = new DateTime(cmd.Year, cmd.Month, 1);
        // 无任务创建任务，有任务创建关系
        async Task createScheduleOrRelationFunc(MonthlyPlanWorkVo work, string schedulePerformer)
        {
            // 从已有的任务中查找任务是否已被创建
            // 分两种情况：
            // 不存在关联字典：执行人+关系信息查询，若不存在则说明任务不存在
            // 存在关联字典：先根据字典ID+执行人查询，不存在说明任务不存在，否则再根据关系信息查询关系是否已存在
            var task = tasks.IfWhere(work.ApInterventionID.HasValue, m => m.InterventionID == work.ApInterventionID && m.SchedulePerformer == schedulePerformer)
                .IfWhere(!work.ApInterventionID.HasValue, m => m.SchedulePerformer == schedulePerformer && m.MonthlyWorkToTasks.Any(n => n.MonthlyPlanDetailID == work.Key))
                .FirstOrDefault();
            // 任务不存在，创建任务
            if (task is null)
            {
                await CreateNewTask(cmd, currentTime, work, schedulePerformer, scheduleDate);
                return;
            }
            // 任务存在，进一步检查是否存在关系
            var relation = task.MonthlyWorkToTasks.FirstOrDefault(m => m.MonthlyPlanDetailID == work.Key);
            if (relation is not null)
            {
                return;
            }
            // 关系若为空，仅创建关系
            var newRelation = new MonthlyWorkToTaskInfo
            {
                MonthlyPlanDetailID = work.Key,
                ApScheduleMainID = task.AnnualScheduleMainID,
                DepartmentID = cmd.DepartmentID,
                WorkContent = work.WorkContent,
                Requirement = work.Requirement,
                PlanTypeID = work.TypeID,
            };
            newRelation.Add(cmd.EmployeeID).Modify(cmd.EmployeeID);
            task.MonthlyWorkToTasks.Add(newRelation);
        }
        return createScheduleOrRelationFunc;
    }

    /// <summary>
    /// 创建新任务
    /// </summary>
    /// <param name="cmd">新增视图</param>
    /// <param name="currentTime">当前时间</param>
    /// <param name="work">待转换为任务的计划工作</param>
    /// <param name="schedulePerformer">预计执行人</param>
    /// <param name="scheduleDate">预计执行时间</param>
    /// <returns></returns>
    private async Task CreateNewTask(CreateOrUpdateTasksCommand cmd, DateTime currentTime, MonthlyPlanWorkVo work, string schedulePerformer, DateTime scheduleDate)
    {
        var guid = "".NewGuid();
        var monthlyWorkToTask = new MonthlyWorkToTaskInfo
        {
            MonthlyPlanDetailID = work.Key,
            ApScheduleMainID = guid,
            DepartmentID = cmd.DepartmentID,
            WorkContent = work.WorkContent,
            Requirement = work.Requirement,
            PlanTypeID = work.TypeID
        }.Add(cmd.EmployeeID).Modify(cmd.EmployeeID) as MonthlyWorkToTaskInfo;
        var newTask = new AnnualScheduleMainInfo
        {
            AnnualScheduleMainID = guid,
            HospitalID = cmd.HospitalID,
            InterventionID = work.ApInterventionID,
            ScheduleYear = cmd.Year,
            ScheduleMonth = (byte)cmd.Month,
            ScheduleDateTime = scheduleDate,
            SchedulePerformer = schedulePerformer,
            StatusCode = AnnualPlanEnums.TieredTaskStatus.UnPerform,
            AddDateTime = currentTime,
            AddEmployeeID = cmd.EmployeeID,
            ModifyDateTime = currentTime,
            ModifyEmployeeID = cmd.EmployeeID,
            DeleteFlag = "",
            MonthlyWorkToTasks = [monthlyWorkToTask]
        }.Add(cmd.EmployeeID).Modify(cmd.EmployeeID) as AnnualScheduleMainInfo;
        await unitOfWork.GetRepository<AnnualScheduleMainInfo>().InsertAsync(newTask);
    }

    public async Task<bool> SendAnnualPlanRemindAsync()
    {
        var currentDate = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
        var unPerformTasks = await mainRepository.GetDailyUnPerformTaskRelationWorks(currentDate);
        if (unPerformTasks.Count == 0)
        {
            _logger.Warn("没有需要提醒的年度计划执行");
            return false;
        }
        var employeeIDs = unPerformTasks.Select(m => m.SchedulePerformer).Distinct().ToList();
        var employeeList = await employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
        var staffData = await employeeStaffDataRepository.GetAll<EmployeeStaffDataInfo>();

        foreach (var employeeID in employeeIDs)
        {
            var departmentID = staffData.Find(m => m.EmployeeID == employeeID)?.DepartmentID ?? 0;
            var employeeName = employeeList.TryGetValue(employeeID, out var name) ? $"{name}老师" : "老师";
            var works = unPerformTasks.Find(m => m.SchedulePerformer == employeeID)
                ?.MonthlyWorkToTasks;
            var interventionsStr = BuildMessageContent(works, departmentID);
            var sendParam = new MessageView
            {
                MessageTools = [MessageTool.Dingtalk],
                EmployeeID = employeeID,
                MessageCondition = new MessageConditionView()
                {
                    Message = $"温馨提醒: {employeeName}, 您{currentDate.Year}年{currentDate.Month}月需要按照计划执行的措施：<br>{interventionsStr}"
                }
            };

            if (!await messageService.SendMessage(sendParam))
            {
                _logger.Warn($"年度计划执行钉钉消息发送失败，sendParam：{sendParam}");
            }
        }
        return true;
    }

    /// <summary>
    /// 组装信息Content
    /// </summary>
    /// <param name="relationWorks">当前任务关联计划工作信息</param>
    /// <param name="departmentID">部门ID</param>
    /// <returns></returns>
    private static string BuildMessageContent(ICollection<MonthlyWorkToTaskInfo> relationWorks, int departmentID)
    {
        var sort = 1;
        var messageContent = relationWorks.Aggregate(new StringBuilder(), (current, next) =>
        {
            // 优先取当前部门的任务数据
            var interventionName = relationWorks.FirstOrDefault(m => m.DepartmentID == departmentID)?.WorkContent;
            interventionName ??= relationWorks.FirstOrDefault()?.WorkContent;
            current.AppendLine($"{sort++}. {interventionName}<br>");
            return current;
        });
        return messageContent.ToString();
    }

    public async Task<bool> AddAnnualScheduleManual(DateTime scheduleDate, string annualScheduleContent, string hospitalID, string employeeID, int departmentID)
    {
        var planMainID = await annualPlanMainRepository.GetMainIDByDeptIDAndYear(departmentID, scheduleDate.Year);
        if (string.IsNullOrEmpty(planMainID))
        {
            throw new Exception("该部门没有年度计划，无法添加年度计划");
        }
        var schedule = new AnnualScheduleMainInfo
        {
            AnnualScheduleMainID = "".NewGuid(),
            HospitalID = hospitalID,
            ScheduleYear = scheduleDate.Year,
            ScheduleMonth = (byte)scheduleDate.Month,
            ScheduleDateTime = scheduleDate,
            SchedulePerformer = employeeID,
            StatusCode = AnnualPlanEnums.TieredTaskStatus.UnPerform,
            DeleteFlag = ""
        }.Add(employeeID).Modify(employeeID) as AnnualScheduleMainInfo;
        await unitOfWork.GetRepository<AnnualScheduleMainInfo>().InsertAsync(schedule);

        var monthlyWorkToTask = new MonthlyWorkToTaskInfo
        {
            MonthlyPlanDetailID = "",
            ApScheduleMainID = schedule.AnnualScheduleMainID,
            DepartmentID = departmentID,
            WorkContent = annualScheduleContent,
            Requirement = "",
            DeleteFlag = ""
        }.Add(employeeID).Modify(employeeID) as MonthlyWorkToTaskInfo;
        await unitOfWork.GetRepository<MonthlyWorkToTaskInfo>().InsertAsync(monthlyWorkToTask);
        return await unitOfWork.SaveChangesAsync() >= 0;
    }
    #endregion
}
