﻿using Arch.EntityFrameworkCore.UnitOfWork;
using Microsoft.Extensions.DependencyInjection;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using System.Data;

namespace NursingManagement.Services
{
    /// <summary>
    /// 排班Service
    /// </summary>
    public class AttendanceService : IAttendanceService
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IEmployeeRepository _employeeRepository;
        private readonly IDictionaryService _dictionaryService;
        private readonly IShiftSchedulingRecordRepository _shiftSchedulingRecordRepository;
        private readonly IShiftSchedulingDetailRepository _shiftSchedulingDetailRepository;
        private readonly IEmployeeService _employeeService;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IAttendanceRecordRepository _attendanceRecordRepository;
        private readonly IAttendanceDetailRepository _attendanceDetailRepository;
        private readonly IAttendanceDetailStatisticsRepository _attendanceDetailStatisticsRepository;
        private readonly IAttendanceApproveRecordRepository _attendanceApproveRecordRepository;
        private readonly IServiceProvider _serviceProvider;
        private readonly IDepartmentListRepository _departmentListRepository;
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IRequestApiService _requestApiService;
        private readonly ITemporaryAttendanceRecordRepository _temporaryAttendanceRecordRepository;
        private readonly IPerpetualCalendarRepository _perpetualCalendarRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly IShiftSchedulingEmployeeSortRepository _shiftSchedulingEmployeeSortRepository;
        private readonly IApproveRecordRepository _approveRecordRepository;

        /// <summary>
        /// 借调到本部门
        /// </summary>
        private const int SECONDMENT_TYPE_1 = 1;

        /// <summary>
        /// 本部门借调出去
        /// </summary>
        private const int SECONDMENT_TYPE_2 = 2;

        /// <summary>
        /// 休假岗类型
        /// </summary>
        private static readonly string POST_TYPE_ID_4 = "4";

        /// <summary>
        /// 同步考勤API编码
        /// </summary>
        private const string SYNC_ATTENDANCE_TO_OA_CODE = "SyncAttendanceToOA";

        /// 下夜班岗位 </summary>
        private const int DEPARTMENTPOST_ID_999 = 999;

        /// 公假PostID </summary>
        private const int PUBIC_LEAVE = 189;

        /// 休假PostID </summary>
        private const int REST_LEAVE = 180;

        /// <summary>
        /// 构造函数注入
        /// </summary>
        /// <param name="unitOfWork"></param>
        /// <param name="shiftSchedulingRecordRepository"></param>
        /// <param name="shiftSchedulingDetailRepository"></param>
        /// <param name="employeeRepository"></param>
        /// <param name="dictionaryService"></param>
        /// <param name="employeeService"></param>
        /// <param name="employeePersonalDataRepository"></param>
        /// <param name="attendanceRecordRepository"></param>
        /// <param name="attendanceDetailRepository"></param>
        /// <param name="attendanceDetailStatisticsRepository"></param>
        /// <param name="attendanceApproveRecordRepository"></param>
        /// <param name="serviceProvider"></param>
        /// <param name="departmentListRepository"></param>
        /// <param name="requestApiService"></param>
        /// <param name="temporaryAttendanceRecordRepository"></param>
        /// <param name="perpetualCalendarRepository"></param>
        /// <param name="settingDictionaryRepository"></param>
        /// <param name="settingDictionaryService"></param>
        /// <param name="shiftSchedulingEmployeeSortRepository"></param>
        public AttendanceService(
            IUnitOfWork unitOfWork
            , IShiftSchedulingRecordRepository shiftSchedulingRecordRepository
            , IShiftSchedulingDetailRepository shiftSchedulingDetailRepository
            , IEmployeeRepository employeeRepository
            , IDictionaryService dictionaryService
            , IEmployeeService employeeService
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IAttendanceRecordRepository attendanceRecordRepository
            , IAttendanceDetailRepository attendanceDetailRepository
            , IAttendanceDetailStatisticsRepository attendanceDetailStatisticsRepository
            , IAttendanceApproveRecordRepository attendanceApproveRecordRepository
            , IServiceProvider serviceProvider
            , IDepartmentListRepository departmentListRepository
            , IRequestApiService requestApiService
            , ITemporaryAttendanceRecordRepository temporaryAttendanceRecordRepository
            , IPerpetualCalendarRepository perpetualCalendarRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            , ISettingDictionaryService settingDictionaryService
            , IShiftSchedulingEmployeeSortRepository shiftSchedulingEmployeeSortRepository
            , IApproveRecordRepository approveRecordRepository

        )
        {
            _unitOfWork = unitOfWork;
            _shiftSchedulingRecordRepository = shiftSchedulingRecordRepository;
            _shiftSchedulingDetailRepository = shiftSchedulingDetailRepository;
            _employeeRepository = employeeRepository;
            _dictionaryService = dictionaryService;
            _employeeService = employeeService;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _attendanceRecordRepository = attendanceRecordRepository;
            _attendanceDetailRepository = attendanceDetailRepository;
            _attendanceDetailStatisticsRepository = attendanceDetailStatisticsRepository;
            _attendanceApproveRecordRepository = attendanceApproveRecordRepository;
            _serviceProvider = serviceProvider;
            _departmentListRepository = departmentListRepository;
            _requestApiService = requestApiService;
            _temporaryAttendanceRecordRepository = temporaryAttendanceRecordRepository;
            _perpetualCalendarRepository = perpetualCalendarRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _settingDictionaryService = settingDictionaryService;
            _shiftSchedulingEmployeeSortRepository = shiftSchedulingEmployeeSortRepository;
            _approveRecordRepository = approveRecordRepository;
        }

        #region 检核本月生成的考勤是否被编辑过

        public async Task<bool> CheckAttendanceEdit(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var attendanceRecordList = await _attendanceRecordRepository.GetRecordByDepartmentID(departmentID, attendanceYear, attendanceMonth);
            // 排班生成的考勤表数据SourceID为排班记录序号，手动调整的考勤SourceID为空
            var count = attendanceRecordList.Where(m => string.IsNullOrWhiteSpace(m.SourceID)).Count();
            // count > 0表示生成的考勤已经被编辑过
            return count > 0;
        }

        #endregion

        #region 由排班数据产生考勤信息

        public async Task<bool> CreateAttendanceRecord(int departmentID, int attendanceYear, int attendanceMonth, string employeeID, string hospitalID)
        {
            // 获取所在月的开始时间和结束时间
            var startDate = new DateTime(attendanceYear, attendanceMonth, 1);
            var endDate = DateHelper.GetLastDayOfMonth(startDate);
            var monthDays = (endDate - startDate).Days + 1;
            //获取当月考勤公休日期
            var perpetualCalendarList = await _perpetualCalendarRepository.GetPublicLeaveByDate(startDate, endDate);
            var nowTime = DateTime.Now;
            var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startDate, endDate);
            if (shiftSchedulingRecord == null)
            {
                return false;
            }
            var shiftSchedulingDetailList = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startDate, endDate);
            if (shiftSchedulingDetailList.Count <= 0)
            {
                return false;
            }
            // 午别字典
            var param = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var noonTypeList = await _settingDictionaryService.GetSettingDictionaryDict(param);
            // 多少小时算一天考勤配置
            var oneDayAttendanceHoursParam = new SettingDictionaryParams()
            {
                SettingType = "AttendanceManagement",
                SettingTypeCode = "OneDayAttendanceHours",
                SettingTypeValue = "OneDayAttendanceHours"
            };
            var oneDayAttendanceHoursParamSetting = await _settingDictionaryService.GetSettingDictionaryDict(oneDayAttendanceHoursParam);
            var oneDayAttendanceHours = 8m;
            if (oneDayAttendanceHoursParamSetting?.Count > 0)
            {
                _ = decimal.TryParse(oneDayAttendanceHoursParamSetting[0].Value.ToString(), out oneDayAttendanceHours);
            }
            // 岗位字典包含休假岗
            var departmentPostList = await _dictionaryService.GetDepartmentPostDict(departmentID, true);
            var employeeList = await _employeeRepository.GetEmployeeDataByDepartmentID(departmentID, true);
            // 获取符合本部门考勤的人员编号和排班明细
            (var employeeIDs, shiftSchedulingDetailList, departmentPostList) = await GetAttendanceNeedData(employeeList, shiftSchedulingDetailList, departmentPostList, departmentID, startDate, endDate, monthDays);
            // 根据根据人员集合获取临时出勤数据
            var tempAttendanceRecordList = await _temporaryAttendanceRecordRepository.GetListByEmployeeIDs(employeeIDs, departmentID, startDate, endDate);

            // 前端已询问过是否覆盖，这里直接删除所有历史考勤数据
            await DeleteOldAttendanceData(departmentID, attendanceYear, attendanceMonth, nowTime, employeeID);
            // 组装参数
            var attendanceParams = new AttendanceParams()
            {
                HospitalID = hospitalID,
                DepartmentID = departmentID,
                AttendanceYear = attendanceYear,
                AttendanceMonth = attendanceMonth,
                RequiredAttendanceDays = await _dictionaryService.GetRequiredAttendanceDays(startDate, endDate),
                DepartmentPostList = departmentPostList,
                NoonTypeList = noonTypeList,
                StartDate = startDate,
                EndDate = endDate,
                NowTime = nowTime,
                EmployeeID = employeeID,
                SourceID = shiftSchedulingRecord.ShiftSchedulingRecordID,
            };
            foreach (var schedulingEmployeeID in employeeIDs)
            {
                var employeeSchedulingDetailList = shiftSchedulingDetailList.Where(m => m.EmployeeID == schedulingEmployeeID).ToList();
                // 计算人员临时出勤小时数
                var employeeTempAttendanceHours = tempAttendanceRecordList.Where(m => m.AttendanceEmployeeID == schedulingEmployeeID).Sum(m => m.AttendanceHours);
                // 获取出勤天数、休假天数、差勤天数(岗位考勤天数 * 排班天数 - 排班天数)、小夜天数、大夜天数、通夜天数
                var attendanceAndRestDayView = await GetAttendanceAndRestDays(employeeSchedulingDetailList, departmentPostList, monthDays, departmentID, employeeTempAttendanceHours, oneDayAttendanceHours);
                // 插入AttendanceRecord
                var attendanceRecord = await InsertAttendanceRecord(attendanceParams, attendanceAndRestDayView, schedulingEmployeeID);
                // 补充参数
                attendanceParams.AttendanceRecordID = attendanceRecord.AttendanceRecordID;
                attendanceParams.SchedulingDetailList = employeeSchedulingDetailList;
                attendanceParams.AttendanceDifferenceDays = attendanceAndRestDayView.AttendanceDifferenceDays;
                // 创建考勤明细及考勤明细统计
                await CreateAttendanceDetailAndStatistics(attendanceParams, perpetualCalendarList);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 插入AttendanceRecord
        /// </summary>
        /// <param name="attendanceParams"></param>
        /// <param name="attendanceAndRestDayView"></param>
        /// <param name="schedulingEmployeeID"></param>
        /// <returns></returns>
        private async Task<AttendanceRecordInfo> InsertAttendanceRecord(AttendanceParams attendanceParams, AttendanceAndRestDayView attendanceAndRestDayView, string schedulingEmployeeID)
        {
            var attendanceRecord = new AttendanceRecordInfo()
            {
                HospitalID = attendanceParams.HospitalID,
                EmployeeID = schedulingEmployeeID,
                DepartmentID = attendanceParams.DepartmentID,
                AttendanceYear = attendanceParams.AttendanceYear,
                AttendanceMonth = attendanceParams.AttendanceMonth,
                // 从万年历获取当月应出勤天数
                RequiredAttendanceDays = attendanceParams.RequiredAttendanceDays,
                ActualAttendanceDays = attendanceAndRestDayView.AttendanceDays,
                RestDays = attendanceAndRestDayView.RestDays,
                // 各类型夜班天数
                EveningShftDays = attendanceAndRestDayView.EveningShftDays,
                NightShiftDays = attendanceAndRestDayView.NightShiftDays,
                WholeNightShiftDays = attendanceAndRestDayView.WholeNightShiftDays,
                // 标记由考勤表生成
                SourceID = attendanceParams.SourceID,
                DataPumpDateTime = null,
                DataPumpFlag = null,
                ModifyDateTime = attendanceParams.NowTime,
                ModifyEmployeeID = attendanceParams.EmployeeID,
                AddDateTime = attendanceParams.NowTime,
                AddEmployeeID = attendanceParams.EmployeeID,
                DeleteFlag = ""
            };
            attendanceRecord.AttendanceRecordID = attendanceRecord.GetId();
            await _unitOfWork.GetRepository<AttendanceRecordInfo>().InsertAsync(attendanceRecord);
            return attendanceRecord;
        }

        /// <summary>
        /// 删除历史考勤数据
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <param name="nowTime"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task DeleteOldAttendanceData(int departmentID, int attendanceYear, int attendanceMonth, DateTime nowTime, string employeeID)
        {
            var attendanceRecords = await _attendanceRecordRepository.GetRecordByDepartmentID(departmentID, attendanceYear, attendanceMonth);
            if (attendanceRecords.Count > 0)
            {
                foreach (var attendanceRecord in attendanceRecords)
                {
                    attendanceRecord.ModifyDateTime = nowTime;
                    attendanceRecord.ModifyEmployeeID = employeeID;
                    attendanceRecord.DeleteFlag = "*";
                }
                var attendanceRecordIDs = attendanceRecords.Select(m => m.AttendanceRecordID).ToList();
                var attendanceDetails = await _attendanceDetailRepository.GetDetailByRecordIDs(attendanceRecordIDs);
                if (attendanceDetails.Count > 0)
                {
                    attendanceDetails.ForEach(attendanceDetail =>
                    {
                        attendanceDetail.ModifyDateTime = nowTime;
                        attendanceDetail.ModifyEmployeeID = employeeID;
                        attendanceDetail.DeleteFlag = "*";
                    });
                }
                var attendanceDetailStatistics = await _attendanceDetailStatisticsRepository.GetDetailStatisticsByRecordIDs(attendanceRecordIDs);
                if (attendanceDetailStatistics.Count > 0)
                {
                    attendanceDetailStatistics.ForEach(attendanceDetailStatistic =>
                    {
                        attendanceDetailStatistic.ModifyDateTime = nowTime;
                        attendanceDetailStatistic.ModifyEmployeeID = employeeID;
                        attendanceDetailStatistic.DeleteFlag = "*";
                    });
                }
            }
        }

        /// <summary>
        /// 获取出勤天数、休假天数、差勤天数(岗位考勤天数 * 排班天数 - 排班天数)、小夜天数、大夜天数、通夜天数
        /// </summary>
        /// <param name="shiftSchedulingDetailList">排班明细集合</param>
        /// <param name="departmentPostList">部门岗位集合</param>
        /// <param name="monthDays">半天考勤</param>
        /// <param name="departmentID">部门序号</param>
        /// <param name="employeeTempAttendanceHours">临时出勤小时数</param>
        /// <param name="oneDayAttendanceHours">多少小时算1天考勤</param>
        /// <returns></returns>
        private async Task<AttendanceAndRestDayView> GetAttendanceAndRestDays(List<ShiftSchedulingDetailInfo> shiftSchedulingDetailList, List<PostSelectOptionsView> departmentPostList, int monthDays, int departmentID, decimal employeeTempAttendanceHours, decimal oneDayAttendanceHours)
        {
            // 排班天数
            decimal schedulingDays = 0;
            // 半天天数
            decimal noonDays = 0.5m;
            var tempAttendanceDays = 0m;
            // 对临时出勤天数进行处理，不足半天舍去，即不够0.5的直接舍去，如2.3取2；2.6 取2.5
            if (employeeTempAttendanceHours != 0)
            {
                tempAttendanceDays = Math.Floor(Math.Round(employeeTempAttendanceHours / oneDayAttendanceHours, 1) / noonDays) * noonDays;
            }
            // 将临时出勤天数初始化为实际考勤天数
            var attendanceAndRestDayView = new AttendanceAndRestDayView()
            {
                AttendanceDays = tempAttendanceDays,
                RestDays = 0,
                AttendanceDifferenceDays = 0,
            };
            if (shiftSchedulingDetailList.Count <= 0)
            {
                return attendanceAndRestDayView;
            }
            // 记录考勤计算完成的日期
            var attendanceCalcFinishDates = new List<DateTime>();
            var postShiftDict = new Dictionary<string, decimal>();
            // 月份第一天是否为下夜班
            //var firstNightFlag = false;
            // 月份最后一天是否为下夜班
            //var lastNightFlag = false;
            shiftSchedulingDetailList = shiftSchedulingDetailList.OrderBy(m => m.SchedulingDate).ThenBy(m => m.NoonType).ToList();
            foreach (var shiftSchedulingDetail in shiftSchedulingDetailList)
            {
                var departmentPost = departmentPostList.Find(m => m.Value.ToString() == shiftSchedulingDetail.DepartmentPostID.ToString());
                // 如果找不到岗位，可能该岗位状态关闭或删除了，根据部门岗位ID查找
                departmentPost ??= await _dictionaryService.GetDepartmentPostDictByID(shiftSchedulingDetail.DepartmentPostID, departmentID);
                // 如果找不到岗位直接跳过
                if (departmentPost == null)
                {
                    continue;
                }
                // 如果是下夜班直接跳过
                if (shiftSchedulingDetail.DepartmentPostID == DEPARTMENTPOST_ID_999)
                {
                    // 如果1号是下夜班要补一天考勤
                    if (shiftSchedulingDetail.SchedulingDate.Day == 1)
                    {
                        attendanceAndRestDayView.AttendanceDays += 1;
                    }
                    continue;
                }
                // 出勤
                if (departmentPost.Type != POST_TYPE_ID_4)
                {
                    // 如果当天还未计算完过考勤才需要计算
                    if (!attendanceCalcFinishDates.Contains(shiftSchedulingDetail.SchedulingDate))
                    {
                        // 获取半天岗位出勤天数
                        (var attendanceDays, attendanceCalcFinishDates) = await GetHalfDayAttendanceDays(shiftSchedulingDetail, shiftSchedulingDetailList, departmentPost, departmentPostList, departmentID, oneDayAttendanceHours, attendanceCalcFinishDates, noonDays);
                        attendanceAndRestDayView.AttendanceDays += attendanceDays;
                    }
                    schedulingDays += noonDays;
                    // 如果部门岗位的班别不为空，计算夜班统计天数
                    if (!string.IsNullOrWhiteSpace(departmentPost.PostShiftID))
                    {
                        var key = $"{shiftSchedulingDetail.SchedulingDate:yyyyMMdd}||{departmentPost.PostShiftID}";
                        // 一天相同的岗 算一个 不重复增加
                        if (!postShiftDict.ContainsKey(key))
                        {
                            postShiftDict.Add(key, 1);
                        }
                    }
                }
                // 休假
                else
                {
                    attendanceAndRestDayView.RestDays += noonDays;
                }
            }
            // 算出可出勤天数（当月总天数 - 休假天数）
            var days = monthDays - attendanceAndRestDayView.RestDays;
            // 如果出勤天数大于当月可出勤天数，多出的舍去
            if (attendanceAndRestDayView.AttendanceDays > days)
            {
                attendanceAndRestDayView.AttendanceDays = days;
            }
            // 计算多出的出勤天数（出勤天数 - 排班天数）
            attendanceAndRestDayView.AttendanceDifferenceDays = attendanceAndRestDayView.AttendanceDays - schedulingDays;
            // 设置岗位班别统计数据
            attendanceAndRestDayView = SetPostShiftStatistics(attendanceAndRestDayView, postShiftDict);
            return attendanceAndRestDayView;
        }

        /// <summary>
        /// 获取半天岗出勤天数
        /// </summary>
        /// <param name="shiftSchedulingDetail">排班明细</param>
        /// <param name="shiftSchedulingDetailList">排班明细集合</param>
        /// <param name="departmentPost">部门岗位</param>
        /// <param name="departmentPostList">部门岗位集合</param>
        /// <param name="departmentID">部门序号</param>
        /// <param name="oneDayAttendanceHours">多少小时算1天考勤</param>
        /// <param name="attendanceCalcFinishDates">计算过考勤的日期集合</param>
        /// <param name="noonDays">半天考勤</param>
        /// <returns></returns>
        private async Task<(decimal, List<DateTime>)> GetHalfDayAttendanceDays(ShiftSchedulingDetailInfo shiftSchedulingDetail, List<ShiftSchedulingDetailInfo> shiftSchedulingDetailList, PostSelectOptionsView departmentPost, List<PostSelectOptionsView> departmentPostList, int departmentID, decimal oneDayAttendanceHours, List<DateTime> attendanceCalcFinishDates, decimal noonDays)
        {
            // 默认半天班考勤计算方式为：考勤天数/2
            var halfDayAttendanceDays = departmentPost.AttendanceDays / 2;
            // 查找当天其他的记录
            var sameDayOtherDetail = shiftSchedulingDetailList.Find(m => m.SchedulingDate == shiftSchedulingDetail.SchedulingDate && m.ShiftSchedulingDetailID != shiftSchedulingDetail.ShiftSchedulingDetailID);
            // 如果找不到说明没有第二个岗的排班，正常返回
            if (sameDayOtherDetail == null)
            {
                return (halfDayAttendanceDays, attendanceCalcFinishDates);
            }
            // 如果是下夜班或者两条记录岗位相同，正常返回
            if (sameDayOtherDetail.DepartmentPostID == DEPARTMENTPOST_ID_999 || sameDayOtherDetail.DepartmentPostID == shiftSchedulingDetail.DepartmentPostID)
            {
                return (halfDayAttendanceDays, attendanceCalcFinishDates);
            }
            var post = departmentPostList.Find(m => m.Value.ToString() == sameDayOtherDetail.DepartmentPostID.ToString());
            // 如果找不到岗位，可能该岗位状态关闭或删除了，根据部门岗位ID查找
            post ??= await _dictionaryService.GetDepartmentPostDictByID(sameDayOtherDetail.DepartmentPostID, departmentID);
            // 如果排班岗位对应的字典找不到 或者 是休假岗，正常返回
            if (post == null || post.Type == POST_TYPE_ID_4)
            {
                return (halfDayAttendanceDays, attendanceCalcFinishDates);
            }
            // 如果两个岗的半天计算考勤天数方式都是 考勤天数/2，正常返回
            if (departmentPost.HalfDayAttendanceCalc == "1" && post.HalfDayAttendanceCalc == "1")
            {
                return (halfDayAttendanceDays, attendanceCalcFinishDates);
            }
            // 没有配置工作时间，正常返回
            if (string.IsNullOrWhiteSpace(departmentPost.WorkingTimeRange) || string.IsNullOrWhiteSpace(post.WorkingTimeRange))
            {
                return (halfDayAttendanceDays, attendanceCalcFinishDates);
            }
            // 如果有一个岗的工作时间是两个时间段，正常返回
            if (departmentPost.WorkingTimeRange.Contains(' ') || post.WorkingTimeRange.Contains(' '))
            {
                return (halfDayAttendanceDays, attendanceCalcFinishDates);
            }
            var departmentPostTimes = departmentPost.WorkingTimeRange.Split("-");
            var postTimes = post.WorkingTimeRange.Split("-");
            // 工作时间段不完整，正常返回
            if (departmentPostTimes.Length != 2 || postTimes.Length != 2)
            {
                return (halfDayAttendanceDays, attendanceCalcFinishDates);
            }
            // 如果转换时间失败，正常返回
            if (!TimeSpan.TryParse(departmentPostTimes[0], out TimeSpan startTime) ||
                !TimeSpan.TryParse(departmentPostTimes[1], out TimeSpan endTime) ||
                !TimeSpan.TryParse(postTimes[0], out TimeSpan postStartTime) ||
                !TimeSpan.TryParse(postTimes[1], out TimeSpan postEndTime)
            )
            {
                return (halfDayAttendanceDays, attendanceCalcFinishDates);
            }
            var now = DateTime.Now.Date;
            // 获取第一个岗的工作开始时间和结束时间
            var departmentPostStartTime = new DateTime(now.Year, now.Month, now.Day, startTime.Hours, startTime.Minutes, 0);
            // 判断是否跨天
            var endDate = endTime > startTime ? now : now.AddDays(1).Date;
            var departmentPostEndTime = new DateTime(endDate.Year, endDate.Month, endDate.Day, endTime.Hours, endTime.Minutes, 0);
            // 获取第二个岗的工作开始时间和结束时间
            var postStartDateTime = new DateTime(now.Year, now.Month, now.Day, postStartTime.Hours, postStartTime.Minutes, 0);
            // 判断是否跨天
            var postEndDate = postEndTime > postStartTime ? now : now.AddDays(1).Date;
            var postEndDateTime = new DateTime(postEndDate.Year, postEndDate.Month, postEndDate.Day, postEndTime.Hours, postEndTime.Minutes, 0);
            // 取两个岗开始时间最小的值
            var minStartTime = new DateTime(Math.Min(departmentPostStartTime.Ticks, postStartDateTime.Ticks));
            // 取两个岗结束时间最大的值
            var maxStartTime = new DateTime(Math.Max(departmentPostEndTime.Ticks, postEndDateTime.Ticks));
            // 计算两个岗合起来的考勤天数
            var attendanceDays = (maxStartTime - minStartTime).Hours / oneDayAttendanceHours;
            // 对考勤天数进行处理，不足半天舍去，即不够0.5的直接舍去，如2.3取2；2.6 取2.5
            halfDayAttendanceDays = Math.Floor(Math.Round(attendanceDays, 1) / noonDays) * noonDays;
            // 添加到考勤计算完成集合，避免重复计算
            attendanceCalcFinishDates.Add(shiftSchedulingDetail.SchedulingDate);
            return (halfDayAttendanceDays, attendanceCalcFinishDates);
        }

        /// <summary>
        /// 设置岗位班别统计数据
        /// </summary>
        /// <param name="attendanceAndRestDayView">出勤天数和休假天数</param>
        /// <param name="postShiftDict">岗位班次字典</param>
        /// <returns></returns>
        /// <exception cref="InvalidDataException"></exception>
        private AttendanceAndRestDayView SetPostShiftStatistics(AttendanceAndRestDayView attendanceAndRestDayView, Dictionary<string, decimal> postShiftDict)
        {
            if (postShiftDict.Keys.Count <= 0)
            {
                return attendanceAndRestDayView;
            }
            // 小夜天数
            decimal? eveningShftDays = null;
            // 大夜天数
            decimal? nightShiftDays = null;
            // 通夜天数
            decimal? wholeNightShiftDays = null;
            foreach ((var key, var value) in postShiftDict)
            {
                var postShiftID = key.Split("||")[1];
                _ = postShiftID switch
                {
                    "1" => eveningShftDays = (eveningShftDays ??= 0) + value,
                    "2" => nightShiftDays = (nightShiftDays ?? 0) + value,
                    "3" => wholeNightShiftDays = (wholeNightShiftDays ?? 0) + value,
                    _ => throw new InvalidDataException()
                };
            }
            attendanceAndRestDayView.EveningShftDays = eveningShftDays;
            attendanceAndRestDayView.NightShiftDays = nightShiftDays;
            attendanceAndRestDayView.WholeNightShiftDays = wholeNightShiftDays;
            return attendanceAndRestDayView;
        }

        /// <summary>
        /// 获取考勤明细和考勤明细统计
        /// </summary>
        /// <param name="attendanceParams"></param>
        /// <returns></returns>
        private async Task CreateAttendanceDetailAndStatistics(AttendanceParams attendanceParams, List<PerpetualCalendarInfo> perpetualCalendarList)
        {
            var attendanceDetailList = new List<AttendanceDetailInfo>();
            var attendanceDetailStatisticsList = new List<AttendanceDetailStatisticsInfo>();
            var restPostDays = new Dictionary<string, decimal>();
            var mutiModifyInfo = new MutiModifyInfo()
            {
                AddDateTime = attendanceParams.NowTime,
                AddEmployeeID = attendanceParams.EmployeeID,
                ModifyDateTime = attendanceParams.NowTime,
                ModifyEmployeeID = attendanceParams.EmployeeID,
                DeleteFlag = ""
            };
            // 循环整月日期
            for (var date = attendanceParams.StartDate; date <= attendanceParams.EndDate; date = date.AddDays(1))
            {
                //获取当天是否是公休日期
                var perpetualCalendar = perpetualCalendarList.Find(x => x.Date == date);
                // 循环午别
                foreach (var noonType in attendanceParams.NoonTypeList)
                {
                    var noonTypeID = noonType.Value.ToString();
                    var attendanceDetail = attendanceDetailList.Find(m => m.AttendanceRecordID == attendanceParams.AttendanceRecordID && m.NoonTypeID == noonTypeID);
                    if (attendanceDetail == null)
                    {
                        attendanceDetail = CreateAttendanceDetail(attendanceParams.AttendanceRecordID, noonTypeID, mutiModifyInfo);
                        attendanceDetailList.Add(attendanceDetail);
                    }
                    var schedulingDetailList = attendanceParams.SchedulingDetailList.Where(m => m.NoonType == noonTypeID).ToList();
                    var schedulingDetail = schedulingDetailList.Find(m => m.SchedulingDate.Date == date);
                    // item1：当天考勤显示内容; item2:各休假岗天数统计; item3:多出的考勤天数
                    var tuple = await GetAttendanceDayValue(schedulingDetail, attendanceParams.DepartmentPostList, attendanceParams.AttendanceDifferenceDays, restPostDays, attendanceParams.DepartmentID, perpetualCalendar);
                    // 累加后的统计
                    restPostDays = tuple.Item2;
                    // 累减后的值
                    attendanceParams.AttendanceDifferenceDays = tuple.Item3;
                    // 通过日期获取字段
                    var field = $"Day{date.Day}";
                    // 通过反射赋值
                    ReflexUtil.SetProperty(attendanceDetail, field, tuple.Item1);
                }
            }
            if (attendanceDetailList.Count > 0)
            {
                await _unitOfWork.GetRepository<AttendanceDetailInfo>().InsertAsync(attendanceDetailList);
            }
            // 组装休假天数统计
            if (restPostDays.Count > 0)
            {
                foreach (var restPost in restPostDays)
                {
                    if (int.TryParse(restPost.Key, out int restPostID))
                    {
                        var attendanceDetailStatistics = CreateAttendanceDetailStatistics(attendanceParams.AttendanceRecordID, restPostID, restPost.Value, mutiModifyInfo);
                        attendanceDetailStatisticsList.Add(attendanceDetailStatistics);
                    }
                }
                if (attendanceDetailStatisticsList.Count > 0)
                {
                    await _unitOfWork.GetRepository<AttendanceDetailStatisticsInfo>().InsertAsync(attendanceDetailStatisticsList);
                }
            }
        }

        /// <summary>
        /// 创建AttendanceDetailInfo
        /// </summary>
        /// <param name="attendanceRecordID"></param>
        /// <param name="noonTypeID"></param>
        /// <param name="mutiModifyInfo"></param>
        /// <returns></returns>
        private AttendanceDetailInfo CreateAttendanceDetail(string attendanceRecordID, string noonTypeID, MutiModifyInfo mutiModifyInfo)
        {
            var attendanceDetail = new AttendanceDetailInfo()
            {
                AttendanceRecordID = attendanceRecordID,
                NoonTypeID = noonTypeID,
                AddDateTime = mutiModifyInfo.AddDateTime,
                AddEmployeeID = mutiModifyInfo.AddEmployeeID,
                ModifyDateTime = mutiModifyInfo.ModifyDateTime,
                ModifyEmployeeID = mutiModifyInfo.ModifyEmployeeID,
                DeleteFlag = mutiModifyInfo.DeleteFlag
            };
            attendanceDetail.AttendanceDetailID = attendanceDetail.GetId();
            return attendanceDetail;
        }

        /// <summary>
        /// AttendanceDetailStatisticsInfo
        /// </summary>
        /// <param name="attendanceRecordID"></param>
        /// <param name="restPostID"></param>
        /// <param name="mutiModifyInfo"></param>
        /// <returns></returns>
        private AttendanceDetailStatisticsInfo CreateAttendanceDetailStatistics(string attendanceRecordID, int restPostID, decimal restPostDays, MutiModifyInfo mutiModifyInfo)
        {
            var attendanceDetailStatistics = new AttendanceDetailStatisticsInfo()
            {
                AttendanceRecordID = attendanceRecordID,
                RestPostID = restPostID,
                AddDateTime = mutiModifyInfo.AddDateTime,
                StatisticsDays = restPostDays,
                AddEmployeeID = mutiModifyInfo.AddEmployeeID,
                ModifyDateTime = mutiModifyInfo.ModifyDateTime,
                ModifyEmployeeID = mutiModifyInfo.ModifyEmployeeID,
                DeleteFlag = mutiModifyInfo.DeleteFlag
            };
            attendanceDetailStatistics.AttendanceSheetDetailStatisticsID = attendanceDetailStatistics.GetId();
            return attendanceDetailStatistics;
        }

        /// <summary>
        /// 获取当天考勤数据
        /// </summary>
        /// <param name="schedulingDetail"></param>
        /// <param name="departmentPostList"></param>
        /// <param name="attendanceDifferenceDays"></param>
        /// <param name="restPostDays"></param>
        /// <returns>item1：当天考勤显示内容; item2:各休假岗天数统计; item3:多出的考勤天数</returns>
        private async Task<Tuple<string, Dictionary<string, decimal>, decimal>> GetAttendanceDayValue(ShiftSchedulingDetailInfo schedulingDetail, List<PostSelectOptionsView> departmentPostList, decimal attendanceDifferenceDays, Dictionary<string, decimal> restPostDays, int departmentID, PerpetualCalendarInfo perpetualCalendar)
        {
            // 半天考勤天数
            decimal noonAttendanceDays = 0.5m;
            var dayValue = "";
            // 如果排班为空或下夜班岗，并且考勤天数有多余，填充考勤
            if (schedulingDetail == null || schedulingDetail.DepartmentPostID == DEPARTMENTPOST_ID_999)
            {
                // 只有够半天才自动填充考勤
                if (attendanceDifferenceDays >= noonAttendanceDays)
                {
                    dayValue = "/";
                    attendanceDifferenceDays -= noonAttendanceDays;
                }
                return Tuple.Create(dayValue, restPostDays, attendanceDifferenceDays);
            }

            var departmentPost = departmentPostList.Find(m => m.Value.ToString() == schedulingDetail.DepartmentPostID.ToString());
            // 如果找不到岗位，可能该岗位状态关闭或删除了，根据部门岗位ID查找
            if (departmentPost == null)
            {
                departmentPost = await _dictionaryService.GetDepartmentPostDictByID(schedulingDetail.DepartmentPostID, departmentID);
            }
            // 非休假岗
            if (departmentPost != null && departmentPost.Type != POST_TYPE_ID_4)
            {
                dayValue = "/";
                return Tuple.Create(dayValue, restPostDays, attendanceDifferenceDays);
            }
            //处理公假逻辑
            if (perpetualCalendar != null && departmentPost.Type == POST_TYPE_ID_4 && departmentPost.Value.ToString() == REST_LEAVE.ToString())
            {
                departmentPost = departmentPostList.Find(m => m.Value.ToString() == PUBIC_LEAVE.ToString());
            }
            dayValue = departmentPost?.LocalLabel;
            // 休假岗，需要统计
            var departmentPostID = departmentPost?.Value.ToString();
            if (perpetualCalendar == null)
            {
                restPostDays = UpdateRestPostDays(restPostDays, departmentPostID, noonAttendanceDays);
            }
            else
            {
                if (departmentPostID == REST_LEAVE.ToString() || departmentPostID == PUBIC_LEAVE.ToString())
                {
                    restPostDays = UpdateRestPostDays(restPostDays, PUBIC_LEAVE.ToString(), noonAttendanceDays);
                }
                else
                {
                    restPostDays = UpdateRestPostDays(restPostDays, departmentPostID, noonAttendanceDays);
                }
            }
            return Tuple.Create(dayValue, restPostDays, attendanceDifferenceDays);
        }

        private Dictionary<string, decimal> UpdateRestPostDays(Dictionary<string, decimal> restPostDays, string departmentPostID, decimal noonAttendanceDays)
        {
            if (restPostDays.ContainsKey(departmentPostID))
            {
                restPostDays[departmentPostID] += noonAttendanceDays;
            }
            else
            {
                restPostDays.Add(departmentPostID, noonAttendanceDays);
            }
            return restPostDays;
        }

        /// <summary>
        /// 获取符合本部门考勤的人员编号和排班明细
        /// </summary>
        /// <param name="employeeList"></param>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        private async Task<Tuple<List<string>, List<ShiftSchedulingDetailInfo>, List<PostSelectOptionsView>>> GetAttendanceNeedData(List<EmployeeForSchedulingView> employeeList, List<ShiftSchedulingDetailInfo> shiftSchedulingDetailList, List<PostSelectOptionsView> departmentPostList, int departmentID, DateTime startDate, DateTime endDate, int monthDays)
        {
            var employeeIDs = employeeList.Select(m => m.EmployeeID).ToList();
            // 借调到本部门的借调记录
            var secondmentList = await _employeeService.GetSecondmentRecordByDepartmentIDAndDate(SECONDMENT_TYPE_1, departmentID, startDate, endDate);
            // 本部门借调出去人员的借调记录
            var secondedList = await _employeeService.GetSecondmentRecordByDepartmentIDAndDate(SECONDMENT_TYPE_2, departmentID, startDate, endDate);
            var checkSecondmentDays = 0;
            // 根据配置判断有借调记录的人员的考勤归属，借调天数>=这个阀值时，考勤归借调部门，否则归原部门
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "Common",
                SettingTypeCode = "SystemConfigs",
                SettingTypeValue = "CheckSecondmentDays"
            };
            var settingList = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var setting = settingList.FirstOrDefault();
            if (setting != null)
            {
                _ = int.TryParse(setting.SettingValue, out checkSecondmentDays);
            }
            // 0表示整月, 计算每月实际天数为阀值
            if (checkSecondmentDays == 0)
            {
                checkSecondmentDays = monthDays;
            }
            // 借调到本部门人员和排班处理
            var secondmentEmployeeIDs = GetAttendanceEmployeeIDs(SECONDMENT_TYPE_1, secondmentList, startDate, endDate, checkSecondmentDays);
            // 过滤掉不符合条件的借调记录
            if (secondmentEmployeeIDs.Count > 0)
            {
                employeeIDs.AddRange(secondmentEmployeeIDs);
                secondmentList = secondmentList.Where(m => secondmentEmployeeIDs.Contains(m.EmployeeID)).ToList();
                // 获取借调人员的原部门的考勤明细
                (var secondmentSchedulingDetailList, departmentPostList) = await GetSchedulingDetailListBySecondment(secondmentList, departmentPostList, startDate, endDate, "DepartmentID");
                if (secondmentSchedulingDetailList.Count > 0)
                {
                    shiftSchedulingDetailList.AddRange(secondmentSchedulingDetailList);
                }
            }
            // 本部门借调出去的人员和排班处理
            var secondedEmployeeIDs = GetAttendanceEmployeeIDs(SECONDMENT_TYPE_2, secondedList, startDate, endDate, checkSecondmentDays);
            // 过滤掉不符合条件的借调记录
            if (secondedEmployeeIDs.Count > 0)
            {
                // 把本部门不符合条件的人员过滤掉
                var mismatchEmployeeIDs = secondedList.Where(m => !secondedEmployeeIDs.Contains(m.EmployeeID)).Select(m => m.EmployeeID).ToList();
                employeeIDs = employeeIDs.Except(mismatchEmployeeIDs).ToList();
                secondedList = secondedList.Where(m => secondedEmployeeIDs.Contains(m.EmployeeID)).ToList();
                // 获取本部门人员在借调部门的考勤明细
                (var secondedSchedulingDetailList, departmentPostList) = await GetSchedulingDetailListBySecondment(secondedList, departmentPostList, startDate, endDate, "SecondmentDepartmentID");
                if (secondedSchedulingDetailList.Count > 0)
                {
                    shiftSchedulingDetailList.AddRange(secondedSchedulingDetailList);
                }
            }
            // employeeList中包含已离职人员需要过滤，排除没有排班信息的离职护士
            var excludeEmployeeIDs = employeeList.Where(m => m.StaffStatusCode == "0" && shiftSchedulingDetailList.Find(n => n.EmployeeID == m.EmployeeID) == null).Select(m => m.EmployeeID).ToList();
            employeeIDs = employeeIDs.Except(excludeEmployeeIDs).ToList();
            shiftSchedulingDetailList = shiftSchedulingDetailList.Distinct().ToList();
            return Tuple.Create(employeeIDs, shiftSchedulingDetailList, departmentPostList);
        }

        /// <summary>
        /// 获取符合考勤条件的人员编号集合
        /// </summary>
        /// <param name="type"></param>
        /// <param name="secondmentList"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="checkSecondmentDays"></param>
        /// <returns></returns>
        private List<string> GetAttendanceEmployeeIDs(int type, List<EmployeeSecondmentRecordInfo> secondmentList, DateTime startDate, DateTime endDate, int checkSecondmentDays)
        {
            var employeeIDs = new List<string>();

            if (secondmentList.Count <= 0)
            {
                return employeeIDs;
            }
            var secondmentEmployees = new Dictionary<string, decimal>();
            foreach (var secondment in secondmentList)
            {
                DateTime newStartDate = secondment.StartDate < startDate ? startDate : secondment.StartDate;
                var actualEndDate = secondment.ActualEndDate ?? secondment.EndDate;
                var actualEndNoon = secondment.ActualEndNoon ?? secondment.EndNoon;
                DateTime newEndDate = actualEndDate > endDate ? endDate : actualEndDate;
                decimal days = (newEndDate - newStartDate).Days + 1;
                // 如果借调开始时间属于本月且是从下午开始的，借调天数需要减去0.5
                if (secondment.StartDate > startDate && secondment.StartNoon == "2")
                {
                    days -= 0.5m;
                }
                // 如果借调结束时间属于本月且上午结束的，借调天数需要减去0.5
                if (actualEndDate < endDate && actualEndNoon == "1")
                {
                    days -= 0.5m;
                }
                if (secondmentEmployees.ContainsKey(secondment.EmployeeID))
                {
                    secondmentEmployees[secondment.EmployeeID] += days;
                }
                else
                {
                    secondmentEmployees.Add(secondment.EmployeeID, days);
                }
            }
            foreach (var employeeID in secondmentEmployees.Keys)
            {
                // 借调到本部门的 借调天数 >= 阀值 考勤归本部门
                if (type == SECONDMENT_TYPE_1 && secondmentEmployees[employeeID] >= checkSecondmentDays)
                {
                    employeeIDs.Add(employeeID);
                    continue;
                }
                // 本部门借调出去的 借调天数 < 阀值 考勤归本部门
                if (type == SECONDMENT_TYPE_2 && secondmentEmployees[employeeID] < checkSecondmentDays)
                {
                    employeeIDs.Add(employeeID);
                }
            }
            return employeeIDs;
        }

        /// <summary>
        /// 依据借调记录，获取人员已发布的排班明细集合
        /// </summary>
        /// <param name="employeeSecondmentList"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="departmentIDFields">SecondmentDepartmentID表示获取借调部门的排班信息，DepartmentID表示获取原部门排班信息</param>
        /// <returns></returns>
        private async Task<Tuple<List<ShiftSchedulingDetailInfo>, List<PostSelectOptionsView>>> GetSchedulingDetailListBySecondment(List<EmployeeSecondmentRecordInfo> employeeSecondmentList, List<PostSelectOptionsView> departmentPostList, DateTime startDate, DateTime endDate, string departmentIDFields)
        {
            var schedulingDetails = new List<ShiftSchedulingDetailInfo>();
            if (employeeSecondmentList == null || employeeSecondmentList.Count <= 0)
            {
                return Tuple.Create(schedulingDetails, departmentPostList);
            }
            foreach (var employeeSecondment in employeeSecondmentList)
            {
                // 通过反射取到部门ID
                var departmentID = (int)ReflexUtil.GetPropertyValue(employeeSecondment, departmentIDFields);
                // 只取已发布排班
                var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startDate, endDate);
                if (shiftSchedulingRecord == null)
                {
                    continue;
                }
                var shiftSchedulingDetails = await _shiftSchedulingDetailRepository.GetDetailByRecordID(shiftSchedulingRecord.ShiftSchedulingRecordID, startDate, endDate);
                shiftSchedulingDetails = shiftSchedulingDetails.Where(m => m.EmployeeID == employeeSecondment.EmployeeID).ToList();
                if (shiftSchedulingDetails.Count > 0)
                {
                    schedulingDetails.AddRange(shiftSchedulingDetails);
                }

                var departmentPostDict = await _dictionaryService.GetDepartmentPostDict(departmentID, false);
                departmentPostDict = departmentPostDict.Where(m => schedulingDetails.Any(n => n.DepartmentPostID.ToString() == m.Value.ToString())).ToList();
                if (departmentPostDict.Count > 0)
                {
                    departmentPostList.AddRange(departmentPostDict);
                }
            }
            return Tuple.Create(schedulingDetails, departmentPostList);
        }

        #endregion

        #region 考勤查询

        public async Task<AttendanceView> GetAttendanceDatas(int departmentID, int attendanceYear, int attendanceMonth, string employeeID, bool sortFlag)
        {
            var attendanceRecords = await _attendanceRecordRepository.GetRecordByDepartmentID(departmentID, attendanceYear, attendanceMonth);
            // 根据员工ID过滤记录
            if (!string.IsNullOrWhiteSpace(employeeID))
            {
                attendanceRecords = attendanceRecords.Where(m => m.EmployeeID == employeeID).ToList();
            }
            // 处理员工ID列表
            var employeeIDs = attendanceRecords.Select(m => m.EmployeeID).ToList();
            if (attendanceRecords.Count == 0)
            {
                if (string.IsNullOrWhiteSpace(employeeID))
                {
                    // 获取部门所有非医生人员
                    var employees = await _employeeRepository.GetEmployeeDataByDepartmentID(departmentID);
                    employeeIDs = employees.Select(m => m.EmployeeID).ToList();
                }
                else
                {
                    employeeIDs.Add(employeeID);
                }
            }
            var employeeList = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            // 根据排序标志进行员工排序
            if (sortFlag)
            {
                // 计算本月的开始和结束日期
                var startDate = new DateTime(attendanceYear, attendanceMonth, 1);
                var endDate = startDate.AddMonths(1).AddDays(-1);
                // 获取排班记录
                var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startDate, endDate);
                var shiftEmployeeIDs = await SortEmployeeList(employeeIDs, shiftSchedulingRecord?.ShiftSchedulingRecordID, startDate, departmentID);
                if (shiftEmployeeIDs?.Count > 0)
                {
                    employeeList = employeeList
                        .OrderBy(employee => shiftEmployeeIDs.IndexOf(employee.Key))
                        .ToDictionary(employee => employee.Key, employee => employee.Value);
                }
            }
            // 午别字典
            var param = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var noonTypeList = await _settingDictionaryService.GetSettingDictionaryDict(param);
            var restPostList = await _dictionaryService.GetDepartmentPostDict(departmentID, false, POST_TYPE_ID_4);
            var columns = await CreateTableColumns(attendanceYear, attendanceMonth, restPostList);
            var rows = await GetRowsByEmployeeList(attendanceRecords, employeeList, columns, noonTypeList, restPostList);
            bool approveFlag = false;
            var attendanceApprove = await _attendanceApproveRecordRepository.GetRecord(departmentID, attendanceYear, attendanceMonth);
            if (attendanceApprove != null)
            {
                var approveRecordInfo = await _approveRecordRepository.GetApproveRecordBySourceIDAsync(attendanceApprove.AttendanceApproveRecordID);
                if (approveRecordInfo == null)
                {
                    approveFlag = true;
                }
            }
            return new AttendanceView()
            {
                AttendanceTable = new TableView() { Columns = columns, Rows = rows },
                ApproveFlag = approveFlag,
                AttendanceApproveRecordID = attendanceApprove?.AttendanceApproveRecordID
            };
        }

        private async Task<List<string>> SortEmployeeList(List<string> employeeList, string shiftSchedulingRecordID, DateTime startDate, int departmentID)
        {
            var sortEmployeeList = new List<ShiftSchedulingEmployeeSortInfo>();
            // 尝试获取当前排班的人员顺序
            if (!string.IsNullOrWhiteSpace(shiftSchedulingRecordID))
            {
                sortEmployeeList = await _shiftSchedulingEmployeeSortRepository.GetEmployeeListByScheduling(shiftSchedulingRecordID);
            }
            // 如果当前排班没有人员顺序，尝试获取上月排班
            if (sortEmployeeList.Count == 0)
            {
                var shiftSchedulingRecord = await _shiftSchedulingRecordRepository.GetRecordByDepartmentID(departmentID, startDate.AddMonths(-1), startDate.AddDays(-1));
                if (shiftSchedulingRecord != null)
                {
                    sortEmployeeList = await _shiftSchedulingEmployeeSortRepository.GetEmployeeListByScheduling(shiftSchedulingRecord.ShiftSchedulingRecordID);
                }
            }
            // 如果获取不到人员顺序，返回原始列表
            if (sortEmployeeList.Count == 0)
            {
                return employeeList;
            }
            // 根据排序规则生成新的员工列表
            var sortedEmployeeList = sortEmployeeList
                .Where(se => employeeList.Contains(se.EmployeeID))
                .Select(se => se.EmployeeID)
                .ToList();
            // 添加未排序的员工到最后
            var remainingEmployees = employeeList.Except(sortedEmployeeList).ToList();
            // 合并排序后的员工和剩余员工
            sortedEmployeeList.AddRange(remainingEmployees);
            return sortedEmployeeList;
        }

        /// <summary>
        /// 组装考勤表的列集合
        /// </summary>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <param name="restPostList"></param>
        /// <returns></returns>
        private async Task<List<TableColumn>> CreateTableColumns(int attendanceYear, int attendanceMonth, List<PostSelectOptionsView> restPostList)
        {
            // 获取岗位班别配置
            var postShiftSettingParasms = new SettingDictionaryParams
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "PostShift"
            };
            var postShiftSettings = await _settingDictionaryRepository.GetSettingDictionary(postShiftSettingParasms);
            var columns = new List<TableColumn>();
            // 获取所在月的开始时间和结束时间
            var startDate = new DateTime(attendanceYear, attendanceMonth, 1);
            var endDate = DateHelper.GetLastDayOfMonth(startDate);
            // 人员列 int index, string name, object value, string key, int? width, int? sort, bool
            // specialMark, List<TableColumn> childColumns
            var employeeColumn = new TableColumn(0, "姓名", "employee", 90, 0, "left");
            employeeColumn.SetMerge();
            employeeColumn.SetFixed();
            columns.Add(employeeColumn);
            // 午别列
            var noonColumn = new TableColumn(1, "午别", "noonType", 60, 1, "center");
            noonColumn.SetFixed();
            columns.Add(noonColumn);
            var dateColumns = new List<TableColumn>();
            // 循环日期
            int index = 0;
            for (var date = startDate; date <= endDate; date = date.AddDays(1))
            {
                var dateKey = date.ToString("yyyyMMdd");
                var childColumn = new TableColumn(index, DateHelper.GetWeekByDate(date), dateKey, 50, index, "center")
                {
                    Value = date.Date
                };
                var column = new TableColumn(index, date.Day.ToString(), dateKey, 50, index, "center", new List<TableColumn>() { childColumn })
                {
                    Value = date.Date
                };
                index++;
                dateColumns.Add(column);
            }
            var dateColumn = new TableColumn(2, "出缺勤情况统计", "dates", 500, 2, "center", dateColumns);
            columns.Add(dateColumn);
            // 子列 大夜、小夜、备注
            var shiftColumns = new List<TableColumn>();
            for (var i = 0; i < postShiftSettings.Count; i++)
            {
                // 首字母转小写
                var key = $"{postShiftSettings[i].SettingTypeValue.FirstLetterToLowerCase()}Days";
                var column = new TableColumn(index, postShiftSettings[i].Description, key, 50, i + 1, "center")
                {
                    MergeFlag = true,
                    ComponentType = "TN"
                };
                shiftColumns.Add(column);
            }
            // 夜班列
            var nightShiftColumn = new TableColumn(3, "夜班", "nightShift", 150, 3, "center", shiftColumns);
            columns.Add(nightShiftColumn);

            // 实际出勤天数列
            var actualAttendanceDaysColumn = new TableColumn(3, "实际出勤天数", "actualAttendanceDays", 65, 3, "right");
            actualAttendanceDaysColumn.SetMerge();
            columns.Add(actualAttendanceDaysColumn);

            // 假别统计列
            var resetStatisticsColumns = new List<TableColumn>();
            var statisticsIndex = 0;
            foreach (var restPost in restPostList)
            {
                var resetColumn = new TableColumn(statisticsIndex, restPost.Label, restPost.Value.ToString(), 65, statisticsIndex, "right");
                resetColumn.SetMerge();
                resetStatisticsColumns.Add(resetColumn);
                statisticsIndex++;
            }
            var resetCountColumn = new TableColumn(statisticsIndex, "合计", "restDays", 65, statisticsIndex, "right");
            resetCountColumn.SetMerge();
            resetStatisticsColumns.Add(resetCountColumn);
            var resetStatisticsColumn = new TableColumn(4, "缺勤情况统计", "resetStatistics", 300, 4, "center", resetStatisticsColumns);
            columns.Add(resetStatisticsColumn);
            // 备注列
            var remarkColumn = new TableColumn(5, "备注", "remark", 100, 5, "left")
            {
                MergeFlag = true,
                ComponentType = "T"
            };
            columns.Add(remarkColumn);
            return columns;
        }

        /// <summary>
        /// 组装考勤表的行数据
        /// </summary>
        /// <param name="attendanceRecords"></param>
        /// <param name="employeeList"></param>
        /// <param name="columns"></param>
        /// <param name="noonTypeList"></param>
        /// <param name="restPostList"></param>
        /// <returns></returns>
        private async Task<List<Dictionary<string, object>>> GetRowsByEmployeeList(List<AttendanceRecordInfo> attendanceRecords, Dictionary<string, string> employeeList, List<TableColumn> columns, List<SelectOptionsView> noonTypeList, List<PostSelectOptionsView> restPostList)
        {
            var tableRows = new List<Dictionary<string, object>>();

            // 循环人员
            foreach (var employee in employeeList)
            {
                var hasAttendance = true;
                var employeeAttendanceRecord = attendanceRecords.OrderByDescending(m => m.ModifyDateTime).FirstOrDefault(m => m.EmployeeID == employee.Key);
                var employeeAttendanceDetails = new List<AttendanceDetailInfo>();
                var employeeAttendanceDetailStatisticsList = new List<AttendanceDetailStatisticsInfo>();
                // 不管有没有考勤，前端都要呈现人员
                if (employeeAttendanceRecord == null)
                {
                    hasAttendance = false;
                }
                else
                {
                    employeeAttendanceDetails = await _attendanceDetailRepository.GetDetailByRecordID(employeeAttendanceRecord.AttendanceRecordID);
                    employeeAttendanceDetailStatisticsList = await _attendanceDetailStatisticsRepository.GetDetailStatisticsByRecordID(employeeAttendanceRecord.AttendanceRecordID);
                }
                // 循环午别
                foreach (var noonType in noonTypeList)
                {
                    // 初始化行数据
                    var row = initRow(employee, noonType, hasAttendance, employeeAttendanceRecord);
                    var noonAttendanceDetail = employeeAttendanceDetails.Find(m => m.NoonTypeID == noonType.Value.ToString());
                    // 填充日期列的考勤信息
                    row = FillDateColumnsAttendanceData(row, noonAttendanceDetail, noonType, columns, restPostList);
                    // 假别统计列赋值
                    var resetStatisticsColumns = columns.Find(m => m.Key == "resetStatistics")?.ChildColumns;
                    foreach (var resetStatisticsColumn in resetStatisticsColumns)
                    {
                        var employeeAttendanceDetailStatistics = employeeAttendanceDetailStatisticsList.Find(m => m.RestPostID.ToString() == resetStatisticsColumn.Key);
                        object dayValue = "";
                        if (resetStatisticsColumn.Key == "restDays")
                        {
                            dayValue = hasAttendance ? employeeAttendanceRecord.RestDays : 0;
                        }
                        if (employeeAttendanceDetailStatistics != null)
                        {
                            dayValue = employeeAttendanceDetailStatistics.StatisticsDays;
                        }
                        row.Add(resetStatisticsColumn.Key, dayValue);
                    }
                    tableRows.Add(row);
                }
            }
            return tableRows;
        }

        /// <summary>
        /// 初始化行数据
        /// </summary>
        /// <param name="employee">人员信息</param>
        /// <param name="noonType">午别</param>
        /// <param name="hasAttendance">是否有考勤记录</param>
        /// <param name="employeeAttendanceRecord">考勤记录</param>
        /// <returns></returns>
        private Dictionary<string, object> initRow(KeyValuePair<string, string> employee, SelectOptionsView noonType, bool hasAttendance, AttendanceRecordInfo employeeAttendanceRecord)
        {
            var employeeInfo = new Dictionary<string, string>() {
                { "employeeID", employee.Key },
                { "employeeName", employee.Value }
            };
            var row = new Dictionary<string, object>() {
                // 人员列
                { "employee",employeeInfo },
                // 午别列
                { "noonType",noonType },
                // 备注列
                { "remark",hasAttendance ? employeeAttendanceRecord.Remark : "" },
            };
            if (hasAttendance)
            {
                // 考勤记录ID
                row.Add("attendanceRecordID", employeeAttendanceRecord.AttendanceRecordID);
                // 实际出勤天数列
                row.Add("actualAttendanceDays", employeeAttendanceRecord.ActualAttendanceDays);
                // 小夜天数列
                row.Add("eveningShftDays", employeeAttendanceRecord.EveningShftDays);
                // 大夜天数列
                row.Add("nightShiftDays", employeeAttendanceRecord.NightShiftDays);
                // 通夜天数列
                row.Add("wholeNightShiftDays", employeeAttendanceRecord.WholeNightShiftDays);
            }
            return row;
        }

        /// <summary>
        /// 填充日期列的考勤信息
        /// </summary>
        /// <param name="row">行</param>
        /// <param name="noonAttendanceDetail">考勤明细</param>
        /// <param name="noonType">午别</param>
        /// <param name="columns">表格列</param>
        /// <param name="restPostList">休假岗位字典表</param>
        /// <returns></returns>
        private Dictionary<string, object> FillDateColumnsAttendanceData(Dictionary<string, object> row, AttendanceDetailInfo noonAttendanceDetail, SelectOptionsView noonType, List<TableColumn> columns, List<PostSelectOptionsView> restPostList)
        {
            // 时间列赋值
            var dateColumns = columns.Find(m => m.Key == "dates")?.ChildColumns;
            foreach (var dateColumn in dateColumns)
            {
                var dayValue = "";
                if (noonAttendanceDetail != null)
                {
                    var field = $"Day{dateColumn.Index + 1}";
                    dayValue = ReflexUtil.GetPropertyValue(noonAttendanceDetail, field).ToString();
                }
                var dayData = new Dictionary<string, object>()
                        {
                            { "attendanceDate", dateColumn.Value},
                            { "noonType", noonType.Value.ToString()},
                            { "value", dayValue},
                        };
                // 将休假岗位的ID返回前端，方便前端统计
                if (!string.IsNullOrWhiteSpace(dayValue) && dayValue != "/")
                {
                    var restPost = restPostList.Find(m => m.LocalLabel == dayValue);
                    if (restPost != null)
                    {
                        dayData.Add("restPostID", restPost.Value);
                    }
                }
                row.Add(dateColumn.Key, dayData);
            }
            return row;
        }

        #endregion

        #region 考勤修改

        public async Task<bool> SaveAttendance(AttendanceView attendanceView)
        {
            if (attendanceView.EmployeeAttendanceList?.Count <= 0)
            {
                return false;
            }
            // 获取所在月的开始时间和结束时间
            var startDate = new DateTime(attendanceView.AttendanceYear, attendanceView.AttendanceMonth, 1);
            var endDate = DateHelper.GetLastDayOfMonth(startDate);
            var nowTime = DateTime.Now;
            // 午别字典
            var param = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var noonTypeList = await _settingDictionaryService.GetSettingDictionaryDict(param);
            // 如果SourceID不为空，说明是第一次手动保存考勤，走新增逻辑
            var isAdd = !string.IsNullOrWhiteSpace(attendanceView.SourceID);
            var attendanceRecords = new List<AttendanceRecordInfo>();
            // 如果是修改，获取历史数据
            if (!isAdd)
            {
                // 获取历史数据
                attendanceRecords = await _attendanceRecordRepository.GetRecordByDepartmentID(attendanceView.DepartmentID, attendanceView.AttendanceYear, attendanceView.AttendanceMonth);
                // 手动编辑考勤 只处理无SourceID的考勤数据
                attendanceRecords = attendanceRecords.Where(m => string.IsNullOrWhiteSpace(m.SourceID)).ToList();
                // 如果没有查到手动调整数据，走新增逻辑
                if (attendanceRecords.Count <= 0)
                {
                    isAdd = true;
                }
            }
            var mutiModifyInfo = new MutiModifyInfo()
            {
                AddDateTime = nowTime,
                AddEmployeeID = attendanceView.EmployeeID,
                ModifyDateTime = nowTime,
                ModifyEmployeeID = attendanceView.EmployeeID,
                DeleteFlag = ""
            };
            foreach (var attendance in attendanceView.EmployeeAttendanceList)
            {
                var attendanceRecord = await SaveAttendanceRecord(isAdd, attendanceView, attendance, attendanceRecords, nowTime, startDate, endDate);
                if (attendanceRecord == null)
                {
                    continue;
                }
                await SaveAttendanceDetail(attendance.AttendanceDetails, attendanceRecord.AttendanceRecordID, noonTypeList, startDate, endDate, mutiModifyInfo);
                await SaveAttendanceStatistics(attendance.RestStatistics, attendanceRecord.AttendanceRecordID, mutiModifyInfo);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 组装并保存AttendanceRecordInfo对象
        /// </summary>
        /// <param name="isAdd"></param>
        /// <param name="attendanceView"></param>
        /// <param name="attendance"></param>
        /// <param name="attendanceRecords"></param>
        /// <param name="nowTime"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        private async Task<AttendanceRecordInfo> SaveAttendanceRecord(bool isAdd, AttendanceView attendanceView, EmployeeAttendanceView attendance, List<AttendanceRecordInfo> attendanceRecords, DateTime nowTime, DateTime startDate, DateTime endDate)
        {
            AttendanceRecordInfo attendanceRecord = null;
            if (isAdd)
            {
                attendanceRecord = new AttendanceRecordInfo()
                {
                    HospitalID = attendanceView.HospitalID,
                    EmployeeID = attendance.EmployeeID,
                    DepartmentID = attendanceView.DepartmentID,
                    AttendanceYear = attendanceView.AttendanceYear,
                    AttendanceMonth = attendanceView.AttendanceMonth,
                    // 从万年历获取当月应出勤天数
                    RequiredAttendanceDays = await _dictionaryService.GetRequiredAttendanceDays(startDate, endDate),
                    SourceID = null,
                    AddDateTime = nowTime,
                    AddEmployeeID = attendanceView.EmployeeID,
                    DeleteFlag = ""
                };
                attendanceRecord.AttendanceRecordID = attendanceRecord.GetId();
                await _unitOfWork.GetRepository<AttendanceRecordInfo>().InsertAsync(attendanceRecord);
            }
            else
            {
                attendanceRecord = attendanceRecords.Find(m => m.EmployeeID == attendance.EmployeeID);
            }
            attendanceRecord.RestDays = attendance.RestDays;
            attendanceRecord.EveningShftDays = attendance.EveningShiftDays;
            attendanceRecord.NightShiftDays = attendance.NightShiftDays;
            attendanceRecord.WholeNightShiftDays = attendance.WholeNightShiftDays;
            attendanceRecord.ActualAttendanceDays = attendance.ActualAttendanceDays;
            attendanceRecord.Remark = attendance.Remark;
            attendanceRecord.DataPumpDateTime = null;
            attendanceRecord.DataPumpFlag = null;
            attendanceRecord.ModifyDateTime = nowTime;
            attendanceRecord.ModifyEmployeeID = attendanceView.EmployeeID;
            return attendanceRecord;
        }

        /// <summary>
        /// 保存考勤明细
        /// </summary>
        /// <param name="attendanceDetails"></param>
        /// <param name="attendanceRecordID"></param>
        /// <param name="noonTypeList"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="mutiModifyInfo"></param>
        /// <returns></returns>
        private async Task SaveAttendanceDetail(List<AttendanceDetailView> attendanceDetails, string attendanceRecordID, List<SelectOptionsView> noonTypeList, DateTime startDate, DateTime endDate, MutiModifyInfo mutiModifyInfo)
        {
            var attendanceDetailList = new List<AttendanceDetailInfo>();
            var oldAttendanceDetailList = await _attendanceDetailRepository.GetDetailByRecordID(attendanceRecordID);

            // 循环午别
            foreach (var noonType in noonTypeList)
            {
                var noonTypeID = noonType.Value.ToString();
                var noonAttendanceDetails = attendanceDetails.Where(m => m.NoonTypeID == noonTypeID).ToList();
                var attendanceDetail = oldAttendanceDetailList.Find(m => m.NoonTypeID == noonType.Value.ToString());
                if (attendanceDetail == null)
                {
                    attendanceDetail = CreateAttendanceDetail(attendanceRecordID, noonTypeID, mutiModifyInfo);
                    attendanceDetailList.Add(attendanceDetail);
                }
                // 循环整月日期
                for (var date = startDate; date <= endDate; date = date.AddDays(1))
                {
                    var detail = noonAttendanceDetails.Find(m => m.AttendanceDate.Date == date);
                    // 通过日期获取字段
                    var field = $"Day{date.Day}";
                    // 通过反射赋值
                    ReflexUtil.SetProperty(attendanceDetail, field, detail?.AttendanceContent ?? "");
                }
            }
            if (attendanceDetailList.Count > 0)
            {
                await _unitOfWork.GetRepository<AttendanceDetailInfo>().InsertAsync(attendanceDetailList);
            }
        }

        /// <summary>
        /// 保存考勤休假统计
        /// </summary>
        /// <param name="restStatistics"></param>
        /// <param name="attendanceRecordID"></param>
        /// <param name="mutiModifyInfo"></param>
        /// <returns></returns>
        private async Task SaveAttendanceStatistics(List<RestStatisticsView> restStatistics, string attendanceRecordID, MutiModifyInfo mutiModifyInfo)
        {
            var attendanceDetailStatisticsList = new List<AttendanceDetailStatisticsInfo>();
            // 先删除历史统计
            var detailStatistics = await _attendanceDetailStatisticsRepository.GetDetailStatisticsByRecordID(attendanceRecordID);
            if (detailStatistics.Count > 0)
            {
                detailStatistics.ForEach(attendanceDetailStatistic =>
                {
                    attendanceDetailStatistic.ModifyDateTime = mutiModifyInfo.ModifyDateTime;
                    attendanceDetailStatistic.ModifyEmployeeID = mutiModifyInfo.ModifyEmployeeID;
                    attendanceDetailStatistic.DeleteFlag = "*";
                });
            }
            // 组装休假天数统计
            if (restStatistics.Count > 0)
            {
                foreach (var restPost in restStatistics)
                {
                    var attendanceDetailStatistics = CreateAttendanceDetailStatistics(attendanceRecordID, restPost.RestPostID, restPost.StatisticsDays, mutiModifyInfo);
                    attendanceDetailStatisticsList.Add(attendanceDetailStatistics);
                }
                if (attendanceDetailStatisticsList.Count > 0)
                {
                    await _unitOfWork.GetRepository<AttendanceDetailStatisticsInfo>().InsertAsync(attendanceDetailStatisticsList);
                }
            }
        }

        /// <summary>
        /// 同步考勤信息到OA
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        public async Task<bool> SyncAttendanceData(int departmentID, int attendanceYear, int attendanceMonth, string employeeID)
        {
            var attendanceApproveRecord = await _attendanceApproveRecordRepository.GetRecord(departmentID, attendanceYear, attendanceMonth);
            if (attendanceApproveRecord == null)
            {
                _logger.Info("考勤未提交审核,不允许确认考勤");
                return false;
            }
            //多条数据，取最新添加的一条，因存在提交审核被驳回或者取消的情况,回传1，默认该科室考勤仍数据未审核状态
            if (attendanceApproveRecord.StatusCode == "2")
            {
                //调用同步程序接口，向中间库表存放消息
                try
                {
                    await SyncAttendanceDataToOAAsync(attendanceApproveRecord);
                }
                catch (Exception ex)
                {
                }
                attendanceApproveRecord.StatusCode = "5";
                attendanceApproveRecord.Modify(employeeID);
                return await _unitOfWork.SaveChangesAsync() > 0;
            }
            _logger.Info("考勤审核未通过");
            return false;
        }

        /// <summary>
        /// 调用中间库接口回传考勤数据
        /// </summary>
        /// <param name="item"></param>
        /// <returns></returns>
        public async Task SyncAttendanceDataToOAAsync(AttendanceApproveRecordInfo item)
        {
            string param = $"?departmentID={item.DepartmentID}&attendanceYear={item.AttendanceYear}&attendanceMonth={item.AttendanceMonth}";
            await _requestApiService.RequestAPI(SYNC_ATTENDANCE_TO_OA_CODE, param, null);
        }

        /// <summary>
        /// 获取考勤状态
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns>1,未审核,2审核通过,3审核驳回，4审核取消(如果没有数据，回传状态1)</returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<string> GetAttendanceStatus(int departmentID, int attendanceYear, int attendanceMonth)
        {
            var attendanceApproveRecord = await _attendanceApproveRecordRepository.GetRecord(departmentID, attendanceYear, attendanceMonth);
            if (attendanceApproveRecord == null)
            {
                return "0";
            }
            //多条数据，取最新添加的一条，因存在提交审核被驳回或者取消的情况,回传1，默认该科室考勤仍数据未审核状态
            if (attendanceApproveRecord.StatusCode == "3" || attendanceApproveRecord.StatusCode == "4")
            {
                return "0";
            }
            return attendanceApproveRecord.StatusCode;
        }

        /// <summary>
        /// 提交审核
        /// </summary>
        /// <param name="departmentID">病区主键</param>
        /// <param name="attendanceYear">排班年份</param>
        /// <param name="attendanceMonth">排班月份</param>
        /// <param name="proveCategory">审批类型</param>
        /// <param name="employeeID">人员ID</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        public async Task<SaveReponseView> AttendanceApproval(int departmentID, int attendanceYear, int attendanceMonth, string proveCategory, string employeeID, string hospitalID)
        {
            var result = new SaveReponseView();
            var attendanceApproveRecord = await _attendanceApproveRecordRepository.GetRecord(departmentID, attendanceYear, attendanceMonth);
            if (attendanceApproveRecord != null && (attendanceApproveRecord.StatusCode == "1" || attendanceApproveRecord.StatusCode == "2"))
            {
                _logger.Info("数据已提交过审批");
                return result;
            }
            var attendanceApprove = new AttendanceApproveRecordInfo
            {
                HospitalID = hospitalID,
                AttendanceApproveRecordID = Guid.NewGuid().ToString("N"),
                AttendanceYear = attendanceYear,
                AttendanceMonth = attendanceMonth,
                DepartmentID = departmentID,
                AddEmployeeID = employeeID,
                AddDateTime = DateTime.Now,
                StatusCode = "0"
            };
            attendanceApprove.Add(employeeID);
            attendanceApprove.Modify(employeeID);
            await _unitOfWork.GetRepository<AttendanceApproveRecordInfo>().InsertAsync(attendanceApprove);
            result.RecordSaveFlag = await _unitOfWork.SaveChangesAsync() >= 0;
            if (!result.RecordSaveFlag)
            {
                return result;
            }
            //提交到审批流程
            result.ApproveSaveFlag = await CreateAttendanceApproval(attendanceApprove, proveCategory);
            return result;
        }

        /// <summary>
        /// 创建审批流程实体参数
        /// </summary>
        /// <param name="data">排班审批主记录数据</param>
        /// <param name="proveCategory">审批类型</param>
        /// <returns></returns>
        public async Task<bool> CreateAttendanceApproval(AttendanceApproveRecordInfo data, string proveCategory)
        {
            var department = await _departmentListRepository.GetByIDAsync(data.DepartmentID);
            var emp = await _employeePersonalDataRepository.GetEmployeeNameByID(data.AddEmployeeID);
            var view = new ApproveMainAndDetailParamView
            {
                SourceID = data.AttendanceApproveRecordID,
                DepartmentID = data.DepartmentID,
                ProveCategory = proveCategory,
                AddEmployeeID = data.AddEmployeeID,
                Content = department.LocalShowName + "提交" + data.AttendanceYear + "年" + data.AttendanceMonth + "月考勤,提交人:" + emp + "."
            };
            return await SubmitForApprovalAsync(view, data);
        }

        /// <summary>
        /// 保存审核信息
        /// </summary>
        /// <param name="view">审批主表与明细参数视图</param>
        /// <param name="attendanceApprove">排班审批主记录表</param>
        /// <returns></returns>
        private async Task<bool> SubmitForApprovalAsync(ApproveMainAndDetailParamView view, AttendanceApproveRecordInfo attendanceApprove)
        {
            var apparoveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            var approveProcessService = _serviceProvider.GetService<IApproveProcessService>();
            if (view == null || string.IsNullOrEmpty(view.SourceID))
            {
                _logger.Error("质控考核维护记录ID为空");
                return false;
            }
            var (approveProcessID, _) = await approveProcessService.GetProcessIDAndContentTemplateByTypeAndDepartmentID(view.ProveCategory, view.DepartmentID);
            if (string.IsNullOrEmpty(approveProcessID))
            {
                _logger.Error("未查到审批流程为空");
                return false;
            }
            if (!await apparoveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID))
            {
                return false;
            }
            attendanceApprove.StatusCode = "1";
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 取消考勤审核
        /// </summary>
        /// <param name="departmentID"></param>
        /// <param name="attendanceYear"></param>
        /// <param name="attendanceMonth"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public async Task<bool> CancelAttendanceApproval(int departmentID, int attendanceYear, int attendanceMonth, string employeeID)
        {
            var apparoveRecordService = _serviceProvider.GetService<IApproveRecordService>();

            var list = await _attendanceApproveRecordRepository.GetRecordByDepartmentID(departmentID, attendanceYear, attendanceMonth);
            if (list == null)
            {
                _logger.Error("未发现考勤审核维护记录");
                return false;
            }
            var attendanceApproveRecord = list.OrderByDescending(m => m.AddDateTime).FirstOrDefault();
            if (attendanceApproveRecord.StatusCode != "1")
            {
                _logger.Error("未找到最新记录状态等于1的考勤记录数据");
                return false;
            }
            await apparoveRecordService.StopApprovalAsync(attendanceApproveRecord.AttendanceApproveRecordID, employeeID);
            attendanceApproveRecord.StatusCode = "4";
            attendanceApproveRecord.Delete(employeeID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        #endregion
    }
}
