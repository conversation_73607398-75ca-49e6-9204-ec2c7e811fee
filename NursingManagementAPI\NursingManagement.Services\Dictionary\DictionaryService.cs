using Arch.EntityFrameworkCore.UnitOfWork;
using DocumentFormat.OpenXml;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;

namespace NursingManagement.Services
{
    public class DictionaryService : IDictionaryService
    {
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IAdministrationDictionaryRepository _administrationDictionaryRepository;
        private readonly IDepartmentPostRepository _departmentPostRepository;
        private readonly IPostRepository _postRepository;
        private readonly ICapabilityLevelRepository _capabilityLevelRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IDepartmentPostWorkingTimeRepository _departmentPostWorkingTimeRepository;
        private readonly IAdministrationIconRepository _administrationIconRepository;
        private readonly IHospitalListRepository _hospitalListRepository;
        private readonly IDepartmentToJobRepository _departmentToJobRepository;
        private readonly IEmployeeToJobRepository _employeeToJobRepository;
        private readonly IPerpetualCalendarRepository _perpetualCalendarRepository;
        private readonly IAuthorityRoleRepository _authorityRoleRepository;
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IAppConfigSettingRepository _appConfigSettingRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly IComponentListRepository _componentListRepository;
        private readonly IEmployeeToDepartmentRepository _employeeToDepartmentRepository;

        public DictionaryService(
            IDepartmentListRepository departmentListRepository
            , IAdministrationDictionaryRepository administrationDictionaryRepository
            , IDepartmentPostRepository departmentPostRepository
            , IPostRepository postRepository
            , ICapabilityLevelRepository capabilityLevelRepository
            , IEmployeeStaffDataRepository employeeStaffDataRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IDepartmentPostWorkingTimeRepository departmentPostWorkingTimeRepository
            , IAdministrationIconRepository administrationIconRepository
            , IHospitalListRepository hospitalListRepository
            , IDepartmentToJobRepository departmentToJobRepository
            , IEmployeeToJobRepository employeeToJobRepository
            , IPerpetualCalendarRepository perpetualCalendarRepository
            , IAuthorityRoleRepository authorityRoleRepository
            , IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
            , IUnitOfWork unitOfWork
            , IAppConfigSettingRepository appConfigSettingRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            , ISettingDictionaryService settingDictionaryService
            , IComponentListRepository componentListRepository
            , IEmployeeToDepartmentRepository employeeToDepartmentRepository
        )
        {
            _departmentListRepository = departmentListRepository;
            _administrationDictionaryRepository = administrationDictionaryRepository;
            _departmentPostRepository = departmentPostRepository;
            _postRepository = postRepository;
            _capabilityLevelRepository = capabilityLevelRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _departmentPostWorkingTimeRepository = departmentPostWorkingTimeRepository;
            _administrationIconRepository = administrationIconRepository;
            _hospitalListRepository = hospitalListRepository;
            _departmentToJobRepository = departmentToJobRepository;
            _employeeToJobRepository = employeeToJobRepository;
            _perpetualCalendarRepository = perpetualCalendarRepository;
            _authorityRoleRepository = authorityRoleRepository;
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
            _unitOfWork = unitOfWork;
            _appConfigSettingRepository = appConfigSettingRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _settingDictionaryService = settingDictionaryService;
            _componentListRepository = componentListRepository;
            _employeeToDepartmentRepository = employeeToDepartmentRepository;
        }

        /// <summary>
        /// 休假岗类型
        /// </summary>
        private static readonly string POST_TYPE_4 = "4";

        /// <summary>
        /// 公共部门
        /// </summary>
        private static readonly int DEPARTMENT_ID_999999 = 999999;

        /// <summary>
        /// 公共部门
        /// </summary>
        private static readonly List<int> PUBLIC_POST_ID_LIST = new List<int> { 998, 999 };

        /// <summary>
        /// 护理部部门ID
        /// </summary>
        private const int DEPARTMENT_ID_405 = 405;

        public async Task<List<DictItem>> GetHrmDepartmentDict()
        {
            var list = await _departmentListRepository.GetByOrganizationType("6");
            var oneLevelList = list.Where(m => m.Level == 1).OrderBy(m => m.Sort).ToList();
            var deptDict = new List<DictItem>();
            deptDict = AssembleHrmDepartmentContent(deptDict, list, oneLevelList, "");
            return deptDict;
        }

        /// <summary>
        /// 组装人事部门名称
        /// </summary>
        /// <param name="deptDict"></param>
        /// <param name="allDepartmentList"></param>
        /// <param name="departmentList"></param>
        /// <param name="parentDepartmentContent"></param>
        /// <returns></returns>
        private List<DictItem> AssembleHrmDepartmentContent(List<DictItem> deptDict, List<DepartmentListInfo> allDepartmentList, List<DepartmentListInfo> departmentList, string parentDepartmentContent)
        {
            foreach (var department in departmentList)
            {
                var departmentContent = department.DepartmentContent;
                if (!string.IsNullOrWhiteSpace(parentDepartmentContent))
                {
                    departmentContent = $"{parentDepartmentContent}->{department.DepartmentContent}";
                }
                deptDict.Add(new DictItem { Key = department.DepartmentID, Value = departmentContent });
                var childen = allDepartmentList.Where(m => m.UpperLevelDepartmentID == department.DepartmentID).OrderBy(m => m.Sort).ToList();
                if (childen.Count > 0)
                {
                    deptDict = AssembleHrmDepartmentContent(deptDict, allDepartmentList, childen, departmentContent);
                }
            }
            return deptDict;
        }

        /// <summary>
        /// 获取当前部门、上级部门及从属部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="includeNursingDept">是否包括护理部</param>
        /// <returns></returns>
        public async Task<List<DepartmentListInfo>> GetSuperAndSubDepartmentsByID(int departmentID, bool includeNursingDept = true)
        {
            var departments = await _departmentListRepository.GetByCacheAsync();
            var currentDepartment = departments.FirstOrDefault(m => m.DepartmentID == departmentID);
            var departmentList = new List<DepartmentListInfo>
            {
                currentDepartment
            };
            // 连续上级部门
            departmentList.AddRange(await GetUpperDepts<DepartmentListInfo, List<DepartmentListInfo>>(departmentID, m => m));
            // 连续下级部门
            departmentList.AddRange(await GetLowerDepts(departmentID, m => m));
            // 添加护理部
            if (includeNursingDept)
            {
                var nursingDept = departments.FirstOrDefault(m => m.DepartmentID == DEPARTMENT_ID_405);
                if (nursingDept != null)
                {
                    departmentList.Add(nursingDept);
                }
            }
            return departmentList.Where(department => department != null).ToList();
        }

        /// <summary>
        /// 获取直接与间接上级部门信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="func">要返回的数据格式</param>
        /// <returns></returns>
        public async Task<V> GetUpperDepts<T, V>(int departmentID, Func<DepartmentListInfo, T> func) where V : ICollection<T>, new()
        {
            return await _departmentListRepository.GetUpperDepartmentRecursion<T, V>(departmentID, func);
        }

        /// <summary>
        /// 获取直接与间接下级部门信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="func">要返回的数据格式</param>
        /// <returns></returns>
        private async Task<List<T>> GetLowerDepts<T>(int departmentID, Func<DepartmentListInfo, T> func)
        {
            var lowerDepartments = await _departmentListRepository.GetLowerDepartments(departmentID);
            if (!lowerDepartments.Any())
            {
                return new List<T>();
            }
            var lowerDepts = new List<T>();
            lowerDepts.AddRange(lowerDepartments.Select(func));
            foreach (var lowerDepartment in lowerDepartments)
            {
                var subLowerDepts = await GetLowerDepts(lowerDepartment.DepartmentID, func);
                lowerDepts.AddRange(subLowerDepts);
            }
            return lowerDepts;
        }

        public async Task<List<SelectOptionsView>> GetAdministrationDict(AdministrationParams administrationParams)
        {
            if (administrationParams == null)
            {
                return new List<SelectOptionsView>();
            }
            var datas = await _administrationDictionaryRepository.GetDictionary(administrationParams);
            return datas.OrderBy(m => m.Sort)
                        .Select(m => new SelectOptionsView() { Label = m.LocalShowName, Value = m.TypeValue })
                        .ToList();
        }

        public async Task<List<SelectOptionsView>> GetPostDict(string postTypeID = null)
        {
            var list = await _postRepository.GetPostList(postTypeID);
            //去掉出差和下夜班岗位
            list = list.Where(m => !PUBLIC_POST_ID_LIST.Contains(m.PostID)).ToList();
            return list.Select(m => new SelectOptionsView { Label = m.PostName, Value = m.PostID }).ToList();
        }

        public async Task<List<PostSelectOptionsView>> GetDepartmentPostDict(int departmentID, bool showAll, string postTypeID = null, DateTime? currentDate = null)
        {
            var postList = await _postRepository.GetByCacheAsync();
            if (!string.IsNullOrWhiteSpace(postTypeID))
            {
                postList = postList.Where(m => m.PostTypeID == postTypeID).ToList();
            }
            var departmentPosts = await _departmentPostRepository.GetAsync(departmentID);
            // 如果是休假岗或显示所有，取出公共数据
            if (postTypeID == POST_TYPE_4 || showAll)
            {
                departmentPosts.AddRange(await _departmentPostRepository.GetAsync(DEPARTMENT_ID_999999));
            }
            departmentPosts = departmentPosts.Where(m => m.StatusCode == "1").OrderBy(m => m.Sort).ToList();
            // 获取时间
            var season = await _settingDictionaryService.GetCurrentPostSeason(departmentID, currentDate == null ? DateTime.Now : currentDate.Value);
            var postWorkingTimes = await _departmentPostWorkingTimeRepository.GetByDepartmenPostIDs(departmentPosts.Select(m => m.DepartmentPostID).ToList(), season);
            var list = new List<PostSelectOptionsView>();
            foreach (var departmentPost in departmentPosts)
            {
                var post = postList.Find(m => m.PostID == departmentPost.PostID);
                if (!string.IsNullOrWhiteSpace(postTypeID) && post == null)
                {
                    continue;
                }
                var selectOptionsView = GetPostSelectOptionsView(post, postWorkingTimes, departmentPost);
                list.Add(selectOptionsView);
            }
            return list;
        }

        public async Task<PostSelectOptionsView> GetDepartmentPostDictByID(int departmentPostID, int departmentID, DateTime? currentDate = null)
        {
            var postList = await _postRepository.GetByCacheAsync();
            var departmentPost = await _departmentPostRepository.GetDepartmentPostByID(departmentPostID);
            // 获取时间
            var season = await _settingDictionaryService.GetCurrentPostSeason(departmentID, currentDate == null ? DateTime.Now : currentDate.Value);
            var postWorkingTimes = await _departmentPostWorkingTimeRepository.GetByDepartmenPostIDs(new List<int>() { departmentPostID }, season);
            var post = postList.Find(m => m.PostID == departmentPost?.PostID);
            if (post == null)
            {
                return null;
            }
            return GetPostSelectOptionsView(post, postWorkingTimes, departmentPost);
        }

        /// <summary>
        /// 组装岗位对象
        /// </summary>
        /// <param name="post"></param>
        /// <param name="postWorkingTimes"></param>
        /// <param name="departmentPost"></param>
        /// <returns></returns>
        private PostSelectOptionsView GetPostSelectOptionsView(PostInfo post, List<DepartmentPostWorkingTimeInfo> postWorkingTimes, DepartmentPostInfo departmentPost)
        {
            var postTimes = postWorkingTimes.Where(m => m.DepartmentPostID == departmentPost.DepartmentPostID).OrderBy(m => m.Sort).ToList();
            var postTime = "";
            if (postTimes.Count > 0)
            {
                postTime = string.Join(" ", postTimes.Select(m => m.StartTime.ToString(@"hh\:mm") + "-" + m.EndTime.ToString(@"hh\:mm")));
            }
            var selectOptionsView = new PostSelectOptionsView()
            {
                Label = departmentPost.DepartmentPostName,
                Value = departmentPost.DepartmentPostID,
                LocalLabel = departmentPost.ShortName,
                Type = post?.PostTypeID,
                PostName = post.PostName,
                WorkingTimeRange = postTime,
                AttendanceDays = departmentPost.AttendanceDays,
                PostShiftID = departmentPost.PostShiftID,
                Color = departmentPost.Color,
                BackGroundColor = departmentPost.BackGroundColor,
                HalfDayAttendanceCalc = departmentPost.HalfDayAttendanceCalc
            };
            return selectOptionsView;
        }

        public async Task<List<SelectOptionsView>> GetCapabilityLevelDict(string type = "Nursing")
        {
            var list = await _capabilityLevelRepository.GetByCacheAsync();
            if (!string.IsNullOrWhiteSpace(type))
            {
                list = list.Where(m => m.Type == type).OrderBy(m => m.Sort).ToList();
            }
            return list.Select(m => new SelectOptionsView { Label = m.CapabilityLevelName, Value = m.CapabilityLevelID }).ToList();
        }

        /// <summary>
        /// 获取人员数据
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="showAll">是否获取全部人员</param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetEmployeeDict(int? departmentID, bool showAll)
        {
            var employeeIDs = await _employeeStaffDataRepository.GetEmployeeIDsByDepartmentIDAsync(departmentID, !showAll);
            var staffData = await _employeeStaffDataRepository.GetAll<EmployeeStaffDataInfo>();
            var personalDatas = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            return personalDatas.Select(m =>
            new SelectOptionsView
            {
                Label = m.Value,
                Value = m.Key,
                Suffix = departmentList.Find(department => department.DepartmentID == staffData.Find(staff => staff.EmployeeID == m.Key)?.DepartmentID)?.LocalShowName
            }).ToList();
        }

        /// <summary>
        /// 获取部门级联选择器数据
        /// </summary>
        /// <param name="organizationType">组织架构</param>
        /// <param name="disableDepartmentIDs">禁用科室</param>
        /// <returns></returns>
        public async Task<List<CascaderView<int>>> GetDepartmentCascaderList(string organizationType, int[] disableDepartmentIDs)
        {
            var returnView = new List<CascaderView<int>>();
            var departmenList = await _departmentListRepository.GetByOrganizationType(organizationType);
            var oneLevelList = departmenList.Where(m => m.Level == 1).ToList();
            disableDepartmentIDs = disableDepartmentIDs.Where(m => m != 0).ToArray();
            foreach (var oneLevel in oneLevelList)
            {
                var cascaderView = new CascaderView<int>
                {
                    Label = oneLevel.LocalShowName,
                    Value = oneLevel.DepartmentID,
                    Children = GenerateCascaderView(departmenList, 2, oneLevel.DepartmentID, disableDepartmentIDs),
                };
                cascaderView.Disabled = disableDepartmentIDs.Contains(cascaderView.Value);
                returnView.Add(cascaderView);
            }
            return returnView;
        }

        /// <summary>
        /// 递归获取级联选择器数据
        /// </summary>
        /// <param name="departmenList">部门集合</param>
        /// <param name="level">层级</param>
        /// <param name="upperLevelDepartmentID">上一级部门ID</param>
        /// <param name="disableDepartmentIDs"></param>
        /// <returns></returns>
        private List<CascaderView<int>> GenerateCascaderView(List<DepartmentListInfo> departmenList, int level, int upperLevelDepartmentID, int[] disableDepartmentIDs)
        {
            var cascaderViews = new List<CascaderView<int>>();
            var currentLevelList = departmenList.Where(m => m.Level == level && m.UpperLevelDepartmentID == upperLevelDepartmentID).ToList();

            foreach (var department in currentLevelList)
            {
                var cascaderView = new CascaderView<int>
                {
                    Label = department.LocalShowName,
                    Value = department.DepartmentID,
                    // 递归获取子级部门
                    Children = GenerateCascaderView(departmenList, level + 1, department.DepartmentID, disableDepartmentIDs)
                };
                cascaderView.Disabled = disableDepartmentIDs.Contains(cascaderView.Value);
                cascaderViews.Add(cascaderView);
            }
            return cascaderViews;
        }

        /// <summary>
        /// 获取注记图示
        /// </summary>
        /// <param name="moduleType">标识类型</param>
        /// <returns></returns>
        public async Task<List<AdministrationIconInfo>> GetIconsByModuleType(string moduleType, string[] groupIDs = null)
        {
            var query = await _administrationIconRepository.GetIconData(moduleType, groupIDs);
            if (groupIDs == null || groupIDs.Length <= 0)
            {
                return query;
            }
            var filteredIcons = new List<AdministrationIconInfo>();
            var defaultGroupIcons = query.Where(icon => icon.GroupID == "999999").ToList();
            var otherGroupIcons = query.Where(icon => icon.GroupID != "999999").ToList();
            filteredIcons.AddRange(otherGroupIcons);
            foreach (var defaultIcon in defaultGroupIcons)
            {
                if (!otherGroupIcons.Any(icon => icon.Remark.Trim() == defaultIcon.Remark.Trim()))
                {
                    filteredIcons.Add(defaultIcon);
                }
            }
            return filteredIcons;
        }

        public async Task<List<Dictionary<string, string>>> GetHospitalList()
        {
            var hospitalList = await _hospitalListRepository.GetHospitalList();
            return hospitalList.Select(m => new Dictionary<string, string>
            {
                { "hospitalID",m.HospitalID },
                { "hospitalName",m.HospitalName },
                { "systemVersion",m.SystemVersion },
                { "themeColor", m.ThemeColor }
            }).ToList();
        }

        /// <summary>
        /// 根据医院ID获取医院信息
        /// </summary>
        /// <param name="hospitalID"></param>
        /// <returns></returns>
        public async Task<Dictionary<string, string>> GetHospitalByHospitalIDAsync(string hospitalID)
        {
            var hospitalList = await _hospitalListRepository.GetHospitalList();
            return hospitalList.Where(m => m.HospitalID == hospitalID).Select(m => new Dictionary<string, string>
            {
                { "hospitalID",m.HospitalID },
                { "hospitalName",m.HospitalName },
                { "systemVersion",m.SystemVersion },
                { "themeColor", m.ThemeColor }
            }).FirstOrDefault();
        }

        /// <summary>
        /// 获取职务级联选择器数据
        /// </summary>
        /// <param name="departmentID">科室ID，非空时依据过滤后的EmployeeToJobs.JobCode对职务字典过滤</param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetDepartmentToJobs(int? departmentID)
        {
            var departmentToJobSettings = await _departmentToJobRepository.GetAll<DepartmentToJobInfo>();
            // 若存在科室ID，需要对列表进行过滤
            if (departmentID.HasValue)
            {
                var jobCodes = await _employeeToJobRepository.GetJobCodesByDepartmentID(departmentID.Value);
                departmentToJobSettings = departmentToJobSettings.Where(m => jobCodes.Contains(m.JobCode)).ToList();
            }
            var returnView = departmentToJobSettings.Select(m => new SelectOptionsView
            {
                Label = $"{m.ShowJobName}（{m.HRDepartmentName}）",
                Value = m.JobCode
            }).ToList();
            return returnView;
        }

        /// <summary>
        /// 模糊查询人员信息
        /// </summary>
        /// <param name="name">姓名</param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetEmployeeDataByName(string name)
        {
            var employeeDict = await _employeePersonalDataRepository.GetEmployeeIDByName(name);
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var staffData = await _employeeStaffDataRepository.GetAll<EmployeeStaffDataInfo>();
            // 使用部门对人员进行区分
            var selectOptions = employeeDict.Select(m => new SelectOptionsView
            {
                Label = m.Value,
                Value = m.Key,
                Suffix = departmentList.Find(department => department.DepartmentID == staffData.Find(staff => staff.EmployeeID == m.Key)?.DepartmentID)?.LocalShowName
            }).ToList();
            return selectOptions;
        }

        /// <summary>
        /// 获取员工的基本信息
        /// </summary>
        /// <param name="employeeIDs">工号</param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetEmployeeDataByIDs(string[] employeeIDs)
        {
            var employeeNameAndID = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);
            if (employeeNameAndID.Count == 0)
            {
                return [];
            }
            return employeeNameAndID.Select(m => new SelectOptionsView
            {
                Label = m.Value,
                Value = m.Key
            }).ToList();
        }

        /// <summary>
        /// 获取部门名称
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns></returns>
        public async Task<string> GetDepartmentName(int departmentID)
        {
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            return departmentList.Find(m => m.DepartmentID == departmentID)?.LocalShowName;
        }

        /// <summary>
        /// 获取时间段内应出勤天数
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<decimal> GetRequiredAttendanceDays(DateTime startDate, DateTime endDate)
        {
            var calendarList = await _perpetualCalendarRepository.GetCalendarByDate(startDate, endDate);
            var restDays = calendarList.Sum(m => m.Holiday);
            return (endDate - startDate).Days + 1 - (restDays ?? 0);
        }

        /// <summary>
        /// 获取部门字典
        /// </summary>
        /// <param name="organizationType">组织架构类型</param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentView>> GetDepartmentViewsByOrganizationType(string organizationType)
        {
            return await _departmentListRepository.GetViewsByOrganizationType(organizationType);
        }

        /// <summary>
        /// 获取角色权限清单
        /// </summary>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetAuthorityRoles()
        {
            var dicts = await _authorityRoleRepository.GetRoles();

            return dicts.Select(n => new SelectOptionsView { Label = n.Value, Value = n.Key }).OrderBy(m => m.Value).ToList();
        }

        /// <summary>
        /// 查看员工的拥有那几个部门的权限
        /// </summary>
        /// <param name="employeeID">人员ID</param>
        /// <param name="organizationType">组织类型</param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetEmployeeDepartmentAsync(string employeeID, string organizationType = "1")
        {
            var returnView = new List<SelectOptionsView>();
            var list = await _employeeDepartmentSwitchRepository.GetDepartmentListByEmployeeID(employeeID, organizationType);
            var departmentIDs = list.Select(m => m.DepartmentID).ToList();
            var departments = await _departmentListRepository.GetByOrganizationType(organizationType);

            return list.Select(m => new SelectOptionsView
            {
                Label = departments.Find(n => n.DepartmentID == m.DepartmentID)?.LocalShowName,
                Value = m.DepartmentID
            }).ToList();
        }

        /// <summary>
        /// 根据部门ID 获取部门岗位
        /// </summary>
        /// <param name="departmentID"></param>
        /// <returns></returns>
        public async Task<List<SelectOptionsView>> GetPostDictByDepartmentID(int departmentID)
        {
            var departmentPost = await _departmentPostRepository.GetAsync(departmentID);
            if (departmentPost.Count == 0)
            {
                return new List<SelectOptionsView>();
            }
            var postIDList = departmentPost.Select(m => m.PostID).Distinct().ToList();
            var postInfoList = await _postRepository.GetByPostIDs(postIDList);
            if (postInfoList.Count == 0)
            {
                return new List<SelectOptionsView>();
            }
            return postInfoList.Select(m => new SelectOptionsView { Label = m.PostName, Value = m.PostID.ToString() }).ToList();
        }

        /// <summary>
        /// 获取AppConfigSetting配置
        /// </summary>
        /// <param name="settingType"></param>
        /// <returns></returns>
        public async Task<List<AppConfigSettingInfo>> GetAppConfigsAsync(string settingType)
        {
            return await _appConfigSettingRepository.GetBySettingType(settingType);
        }

        /// <summary>
        /// 根据组件类型获取组件数据
        /// </summary>
        /// <param name="componentType">组件类型</param>
        /// <returns></returns>
        public async Task<List<KeyValuePair<int, string>>> GetComponentListByType(string componentType)
        {
            var components = await _componentListRepository.GetComponentListByType(componentType);
            var returnView = components.ToDictionary(m => m.ComponentListID, n => n.Description).ToList();
            return returnView;
        }

        /// <summary>
        /// 获取上级部门选项 | 一般为片区
        /// </summary>
        /// <returns>返回空数组或者选项</returns>
        public async Task<List<SelectOptionsView>> GetUpperDeptOptions()
        {
            var cacheDepartments = await _departmentListRepository.GetByOrganizationType("1");
            // 没有组织结构的医院不显示
            if (cacheDepartments.DistinctBy(m => m.Level).Count() <= 1)
            {
                return [];
            }
            return cacheDepartments.Where(m => m.UpperLevelDepartmentID == 0).Select(m => new SelectOptionsView
            {
                Label = m.LocalShowName,
                Value = m.DepartmentID
            }).ToList();
        }

        /// <summary>
        /// 获取访视权限
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<CascaderView<int>>> GetVisitsDeptOptionsAsync(string employeeID)
        {
            var result = new List<CascaderView<int>>();
            var employeeToDepartment = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var departmentIDs = employeeToDepartment.Select(m => m.DepartmentID).ToList();
            var cascaderList = await GetDepartmentCascaderList("1", []);
            cascaderList = cascaderList.Where(m => m.Children.Count > 0).ToList();
            foreach (var cascader in cascaderList)
            {
                if (departmentIDs.Contains(cascader.Value))
                {
                    result.Add(cascader);
                    continue;
                }
                var matchingChildren = cascader.Children.Where(m => departmentIDs.Contains(m.Value)).ToList();
                if (matchingChildren.Count == 0)
                {
                    continue;
                }
                cascader.Children = matchingChildren;
                result.Add(cascader);
            }
            return result;
        }
    }
}
