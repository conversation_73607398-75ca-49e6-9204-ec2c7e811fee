using Arch.EntityFrameworkCore.UnitOfWork;
using DocumentFormat.OpenXml;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data;
using NursingManagement.Data.Interface;
using NursingManagement.Data.Interface.Employee;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.Employee;

namespace NursingManagement.Services
{
    public class EmployeeService : IEmployeeService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IEmployeeClothingSizesRepository _employeeClothingSizesRepository;
        private readonly IEmployeeContactRepository _employeeContactRepository;
        private readonly IEmployeeEducationalExperienceRepository _employeeEducationalExperienceRepository;
        private readonly IEmployeeEmploymentRecordRepository _employeeEmploymentRecordRepository;
        private readonly IEmployeeCapabilityLevelRepository _employeeCapabilityLevelRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IEmployeeProfessionalPositionRepository _employeeProfessionalPositionRepository;
        private readonly IEmployeeRelativesRepository _employeeRelativesRepository;
        private readonly IEmployeeRewardRepository _employeeRewardRepository;
        private readonly IEmployeeStaffDataRepository _employeeStaffDataRepository;
        private readonly IEmployeeWorkExperienceRepository _employeeWorkExperienceRepository;
        private readonly IEmployeeTeachintRelationRepository _employeeTeachintRelationRepository;
        private readonly IAdministrationDictionaryRepository _administrationDictionaryRepository;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IPostRepository _postRepository;
        private readonly ICapabilityLevelRepository _capabilityLevelRepository;
        private readonly IDictionaryService _dictionaryService;
        private readonly IEmployeeSecondmentRecordRepository _employeeSecondmentRecordRepository;
        private readonly IShiftSchedulingDetailRepository _shiftSchedulingDetailRepository;
        private readonly IShiftSchedulingDetailMarkRepository _shiftSchedulingDetailMarkRepository;
        private readonly IRequestApiService _requestApiService;
        private readonly IEmployeeDepartmentChangeRequestRepository _employeeDepartmentChangeRequestRepository;
        private readonly IDepartmentVSDepartmentRepository _departmentVSDepartmentRepository;
        private readonly IApproveProcessService _approveProcessService;
        private readonly IServiceProvider _serviceProvider;
        private readonly IOptions<SystemConfig> _options;
        private readonly IEmployeeToDepartmentRepository _employeeToDepartmentRepository;
        private readonly IApproveRecordRepository _approveRecordRepository;
        private readonly IApproveDetailRepository _approveDetailRepository;
        private readonly IApproveMainRepository _approveMainRepository;
        private readonly ISettingDictionaryRepository _settingDictionaryRepository;
        private readonly IEmployeeStrengthRepository _employeeStrengthRepository;
        private readonly ISettingDictionaryService _settingDictionaryService;
        private readonly SessionCommonServer _sessionCommonServer;
        private readonly IEmployeeDepartmentSwitchRepository _employeeDepartmentSwitchRepository;

        #region 常量

        /// <summary>
        /// 护理部部门ID
        /// </summary>
        private const int DEPARTMENT_ID_405 = 405;

        /// <summary>
        /// 正式任职ID
        /// </summary>
        private const int EMPLOYEE_POSTTYPE_OFFICIAL_1 = 1;

        /// <summary>
        /// 兼职任职ID
        /// </summary>
        private const int EMPLOYEE_POSTTYPE_PARTTIMEJOB_2 = 2;

        /// <summary>
        /// 社会任职ID
        /// </summary>
        private const int EMPLOYEE_POSTTYPE_SOCIETY_3 = 3;

        /// <summary>
        /// 档案ID中标识住院病区人员的字符
        /// </summary>
        private const string IN_HOSPITAL_EMPLOYEE_FILEID_IDENTIFIER = "-BQ-";

        /// <summary>
        /// 创建一个int集合，用于存储员工病区权限
        /// </summary>
        private readonly List<int> DEPARTMENT_AUTHORITY = [354, 355, 356, 357];

        #endregion

        public EmployeeService(
            IUnitOfWork unitOfWork
            , IEmployeeClothingSizesRepository employeeClothingSizesRepository
            , IEmployeeContactRepository employeeContactRepository
            , IEmployeeEducationalExperienceRepository employeeEducationalExperienceRepository
            , IEmployeeEmploymentRecordRepository employeeEmploymentRecordRepository
            , IEmployeeCapabilityLevelRepository employeeCapabilityLevelRepository
            , IEmployeePersonalDataRepository employeePersonalDataRepository
            , IEmployeeProfessionalPositionRepository employeeProfessionalPositionRepository
            , IEmployeeRelativesRepository employeeRelativesRepository
            , IEmployeeRewardRepository employeeRewardRepository
            , IEmployeeStaffDataRepository employeeStaffDataRepository
            , IEmployeeWorkExperienceRepository employeeWorkExperienceRepository
            , IAdministrationDictionaryRepository administrationDictionaryRepository
            , IDepartmentListRepository departmentListRepository
            , IPostRepository postRepository
            , ICapabilityLevelRepository capabilityLevelRepository
            , IDictionaryService dictionaryService
            , IEmployeeSecondmentRecordRepository employeeSecondmentRecordRepository
            , IEmployeeTeachintRelationRepository employeeTeachintRelationRepository
            , IShiftSchedulingDetailRepository shiftSchedulingDetailRepository
            , IShiftSchedulingDetailMarkRepository shiftSchedulingDetailMarkRepository
            , IRequestApiService requestApiService
            , IEmployeeDepartmentChangeRequestRepository employeeDepartmentChangeRequestRepository
            , IDepartmentVSDepartmentRepository departmentVSDepartmentRepository
            , IApproveProcessService approveProcessService
            , IServiceProvider serviceProvider
            , IOptions<SystemConfig> options
            , IEmployeeToDepartmentRepository employeeToDepartmentRepository
            , IApproveRecordRepository approveRecordRepository
            , IApproveDetailRepository approveDetailRepository
            , IApproveMainRepository approveMainRepository
            , ISettingDictionaryRepository settingDictionaryRepository
            , IEmployeeStrengthRepository employeeStrengthRepository
            , ISettingDictionaryService settingDictionaryService
            , SessionCommonServer sessionCommonServer
            , IEmployeeDepartmentSwitchRepository employeeDepartmentSwitchRepository
        )
        {
            _unitOfWork = unitOfWork;
            _employeeClothingSizesRepository = employeeClothingSizesRepository;
            _employeeContactRepository = employeeContactRepository;
            _employeeEducationalExperienceRepository = employeeEducationalExperienceRepository;
            _employeeEmploymentRecordRepository = employeeEmploymentRecordRepository;
            _employeeCapabilityLevelRepository = employeeCapabilityLevelRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _employeeProfessionalPositionRepository = employeeProfessionalPositionRepository;
            _employeeRelativesRepository = employeeRelativesRepository;
            _employeeRewardRepository = employeeRewardRepository;
            _employeeStaffDataRepository = employeeStaffDataRepository;
            _employeeWorkExperienceRepository = employeeWorkExperienceRepository;
            _administrationDictionaryRepository = administrationDictionaryRepository;
            _departmentListRepository = departmentListRepository;
            _postRepository = postRepository;
            _capabilityLevelRepository = capabilityLevelRepository;
            _dictionaryService = dictionaryService;
            _employeeSecondmentRecordRepository = employeeSecondmentRecordRepository;
            _employeeTeachintRelationRepository = employeeTeachintRelationRepository;
            _shiftSchedulingDetailRepository = shiftSchedulingDetailRepository;
            _shiftSchedulingDetailMarkRepository = shiftSchedulingDetailMarkRepository;
            _requestApiService = requestApiService;
            _employeeDepartmentChangeRequestRepository = employeeDepartmentChangeRequestRepository;
            _departmentVSDepartmentRepository = departmentVSDepartmentRepository;
            _approveProcessService = approveProcessService;
            _serviceProvider = serviceProvider;
            _options = options;
            _employeeToDepartmentRepository = employeeToDepartmentRepository;
            _approveRecordRepository = approveRecordRepository;
            _approveDetailRepository = approveDetailRepository;
            _approveMainRepository = approveMainRepository;
            _settingDictionaryRepository = settingDictionaryRepository;
            _employeeStrengthRepository = employeeStrengthRepository;
            _settingDictionaryService = settingDictionaryService;
            _sessionCommonServer = sessionCommonServer;
            _employeeDepartmentSwitchRepository = employeeDepartmentSwitchRepository;
        }

        public async Task<EmployeeStatisticListView> GetEmployeeList(EmployeeQueryView employeeQueryView)
        {
            EmployeeStatisticListView employeeStatisticListView = new EmployeeStatisticListView();
            if (employeeQueryView == null)
            {
                return employeeStatisticListView;
            }
            var administrationDictionary = await _administrationDictionaryRepository.GetAll();
            var employeeListViews = await GetEmployeeListViews(employeeQueryView, administrationDictionary);
            employeeStatisticListView.EmployeeListViews = employeeListViews;
            employeeStatisticListView.EmployeeStatisticViews = await GetEmployeeStatisticViews(employeeListViews);
            return employeeStatisticListView;
        }

        /// <summary>
        /// 获取人员清单统计
        /// </summary>
        /// <param name="employeeListViews"></param>
        /// <returns></returns>
        private async Task<List<EmployeeStatisticView>> GetEmployeeStatisticViews(List<EmployeeListView> employeeListViews)
        {
            var inHospitalEmployeeCount = employeeListViews.Where(m => m.IsInHospitalStationEmployee).Count();
            List<EmployeeStatisticView> employeeStatisticViews =
            [
                new(){ StatisticTitle="在职人数",StatisticCode="GetStaffEmployee",StatisticFunction = "GetStaffEmployee",StatisticValue = employeeListViews.Count },
                new(){ StatisticTitle="待离职",StatisticCode="GetPreresignationEmployee",StatisticFunction = "GetPreresignationEmployee",StatisticValue =employeeListViews.Where(m=>m.StaffStatusCode== "2").Count()},
                new(){ StatisticTitle="住院病区",StatisticCode="GetInHospitalStationEmployee",StatisticFunction = "GetInHospitalStationEmployee",StatisticValue = inHospitalEmployeeCount },
                new(){ StatisticTitle="非住院病区",StatisticCode="GetUnHospitalStationEmployee",StatisticFunction = "GetUnHospitalStationEmployee",StatisticValue = employeeListViews.Count - inHospitalEmployeeCount }
            ];
            var capabilityLevels = await _dictionaryService.GetCapabilityLevelDict();
            foreach (var capabilityLevel in capabilityLevels)
            {
                var employeeStatistic = new EmployeeStatisticView()
                {
                    StatisticTitle = capabilityLevel.Label.ToString(),
                    StatisticCode = capabilityLevel.Value.ToString(),
                    StatisticFunction = "GetCapabilityLevel"
                };
                employeeStatisticViews.Add(employeeStatistic);
            }
            return employeeStatisticViews;
        }

        /// <summary>
        /// 获取人员清单明细
        /// </summary>
        /// <param name="employeeQueryView"></param>
        /// <param name="settingList"></param>
        /// <returns></returns>
        private async Task<List<EmployeeListView>> GetEmployeeListViews(EmployeeQueryView employeeQueryView, List<AdministrationDictionaryInfo> settingList)
        {
            //人员在职信息
            var employeeListViewList = await _employeeStaffDataRepository.GetStaffByEmployeeQueryView(employeeQueryView);
            var employeeIDs = employeeListViewList.Select(m => m.EmployeeID).ToArray();
            //能力层级字典
            var capabilityLevelList = await _capabilityLevelRepository.GetByCacheAsync();
            //人员护士层级
            var employeeCapabilityLevels = await _employeeCapabilityLevelRepository.GetCurrentCapabilityLevelView(employeeQueryView.HospitalID, employeeIDs, employeeQueryView.CapabilityIDs);
            //人员职称
            var employeeProfessionalPositions = await _employeeProfessionalPositionRepository.GetHighestCurrentProfessionalPositionView(employeeQueryView.HospitalID, employeeIDs, employeeQueryView.PositionIDs);
            //人员教育经历表
            var employeeEducationalExperiences = await _employeeEducationalExperienceRepository.GetFirstAndHighestEducationalView(employeeQueryView.HospitalID, employeeIDs, employeeQueryView.EducationalIDs);
            var settingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeEducationalExperience",
                SettingTypeValue = "EducationCode"
            };
            var educationCodeSetting = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            var titleSettingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeProfessionalPosition",
                SettingTypeValue = "ProfessionalTypeCode",
            };
            var titleSetting = await _settingDictionaryRepository.GetSettingDictionary(titleSettingDictionaryParams);
            employeeListViewList = employeeListViewList.Where(m => employeeIDs.Contains(m.EmployeeID)).ToList();
            foreach (var employeeListView in employeeListViewList)
            {
                employeeListView.Gender = GetLocalShowName("GenderCode", employeeListView.GenderCode, settingList);
                employeeListView.Nation = GetLocalShowName("NationCode", employeeListView.NationCode, settingList);
                employeeListView.Birthdate = employeeListView.Birthdate;
                employeeListView.Age = (int?)GetAnniversary(employeeListView.Birthdate);
                employeeListView.EntryDate = employeeListView.EntryDate;
                employeeListView.EntryAge = GetAnniversary(employeeListView.EntryDate);
                employeeListView.JobCategory = GetLocalShowName("JobCategoryCode", employeeListView.JobCategoryCode, settingList);
                employeeListView.CapabilityLevel = capabilityLevelList.Find(m => m.CapabilityLevelID == employeeListView.CapabilityLevelID)?.CapabilityLevelName;
                var capabilityLevelInfo = employeeCapabilityLevels.Find(m => m.EmployeeID == employeeListView.EmployeeID && m.CapabilityLevelID == employeeListView.CapabilityLevelID);
                if (capabilityLevelInfo != null)
                {
                    employeeListView.PromotionDate = capabilityLevelInfo.PromotionDate;
                }
                var professionalPositionInfo = employeeProfessionalPositions.FirstOrDefault(m => m.EmployeeID == employeeListView.EmployeeID);
                if (professionalPositionInfo != null)
                {
                    employeeListView.ProfessionalLevel = titleSetting.FirstOrDefault(m => m.SettingValue == professionalPositionInfo.ProfessionalLevelCode)?.Description;
                    employeeListView.ProfessionalCode = professionalPositionInfo.ProfessionalLevelCode;
                    employeeListView.ObtainingDate = professionalPositionInfo.ObtainingDate;
                }
                var highestEducationalExperienceInfo = employeeEducationalExperiences.Find(m => m.EmployeeID == employeeListView.EmployeeID && m.SpecialFlag == "H");
                if (highestEducationalExperienceInfo != null)
                {
                    employeeListView.HighestDegree = educationCodeSetting.FirstOrDefault(m => m.SettingValue == highestEducationalExperienceInfo.EducationCode)?.Description;
                    employeeListView.HighestDegreeCode = highestEducationalExperienceInfo.EducationCode;
                }

                var FirstEducationalExperienceInfo = employeeEducationalExperiences.Find(m => m.EmployeeID == employeeListView.EmployeeID && m.SpecialFlag == "F");
                if (FirstEducationalExperienceInfo != null)
                {
                    employeeListView.FirstDegree = educationCodeSetting.FirstOrDefault(m => m.SettingValue == FirstEducationalExperienceInfo.EducationCode)?.Description;
                    employeeListView.FirstDegreeCode = FirstEducationalExperienceInfo.EducationCode;
                }
                // 处理判断护士是否为住院病区员工
                employeeListView.IsInHospitalStationEmployee = employeeListView.FileID.Contains(IN_HOSPITAL_EMPLOYEE_FILEID_IDENTIFIER);
            }
            return GetEmployeeListViewSort(employeeListViewList);
        }

        /// <summary>
        /// 获取用户列表排序
        /// </summary>
        /// <param name="employeeListViewList"></param>
        /// <returns></returns>
        private static List<EmployeeListView> GetEmployeeListViewSort(List<EmployeeListView> employeeListViewList)
        {
            var listViews = new List<EmployeeListView>();
            var employeeListViewGroupList = employeeListViewList.OrderBy(m => m.DepartmentID).GroupBy(m => m.DepartmentID).ToList();
            foreach (var employeeListViewGroup in employeeListViewGroupList)
            {
                var employeeListViews = employeeListViewGroup.OrderBy(m => m.EntryDate).ToList();
                var tempEmployeeManagement = new List<EmployeeListView>();
                for (int i = employeeListViews.Count - 1; i >= 0; i--)
                {
                    var employeeManagement = employeeListViews.Find(m => m.CapabilityLevel == "管理层");
                    if (employeeManagement != null)
                    {
                        employeeListViews.Remove(employeeManagement);
                        tempEmployeeManagement.Add(employeeManagement);
                    }
                }
                tempEmployeeManagement = tempEmployeeManagement.OrderBy(m => m.EntryDate).ToList();
                employeeListViews.InsertRange(0, tempEmployeeManagement);
                listViews.AddRange(employeeListViews);
            }
            return listViews;
        }

        /// <summary>
        /// 获取周年
        /// </summary>
        /// <param name="date"></param>
        /// <returns></returns>
        private double? GetAnniversary(DateTime? date)
        {
            if (!date.HasValue) { return null; }
            return Math.Round((double)(DateTime.Now.Date - date.Value.Date).Days / 365, 1);
        }

        /// <summary>
        /// 获取本地配置名称
        /// </summary>
        /// <param name="referenceValue"></param>
        /// <param name="typeValue"></param>
        /// <param name="settingList"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        private string GetLocalShowName(string referenceValue, string typeValue, List<AdministrationDictionaryInfo> settingList)
        {
            return settingList.Find(m => m.ReferenceValue == referenceValue && m.TypeValue == typeValue)?.LocalShowName ?? string.Empty;
        }

        /// <summary>
        /// 获取在职人员的信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<object> GetOnJobInfoAsync(string employeeID)
        {
            if (employeeID == null)
            {
                _logger.Warn("请求参数为空");
                throw new ArgumentNullException(nameof(employeeID));
            }
            //配置| 字典
            var settingList = await _administrationDictionaryRepository.GetAll();
            var departmentListInfos = await _departmentListRepository.GetByOrganizationType("1");
            var postList = await _postRepository.GetByCacheAsync();
            var capabilityLevels = await _capabilityLevelRepository.GetAll<CapabilityLevelInfo>();
            var staffDataViews = await GetEmployeeStaffDataViewsAsync(employeeID, settingList, departmentListInfos, postList, capabilityLevels);
            var employeeEmploymentRecordViews = await GetEmployeeEmploymentViewsAsync(employeeID, settingList, departmentListInfos, postList);
            var rewardRecordViews = await GetEmployeeRewardViewsAsync(employeeID);
            var capabilityLevelViews = await GetEmployeeCapabilityLevelViewAsync(employeeID, capabilityLevels);

            return new EmployeeOnJobView
            {
                EmployeeReward = rewardRecordViews,
                EmployeeEmploymentRecord = employeeEmploymentRecordViews,
                EmployeeCapabilityLevel = capabilityLevelViews,
                EmployeeStaffData = staffDataViews
            };
        }

        /// <summary>
        /// 获取员工能级
        /// </summary>
        /// <param name="employeeID">员工</param>
        /// <param name="capabilityLevels">岗位层级</param>
        /// <returns></returns>
        private async Task<List<EmployeeCapabilityLevelView>> GetEmployeeCapabilityLevelViewAsync(string employeeID, List<CapabilityLevelInfo> capabilityLevels)
        {
            var capabilityLevelViews = new List<EmployeeCapabilityLevelView>();
            var employeeCapabilityLevels = await _employeeCapabilityLevelRepository.GetRecordListAsync(employeeID);
            foreach (var item in employeeCapabilityLevels)
            {
                if (item.CapabilityLevelID == null)
                {
                    continue;
                }
                var capabilityLevelView = new EmployeeCapabilityLevelView
                {
                    ApplicationNo = item.ApplicationNo,
                    EmployeeCapabilityLevelID = item.EmployeeCapabilityLevelID,
                    CapabilityLevelID = item.CapabilityLevelID.Value,
                    EmployeeID = item.EmployeeID,
                    PromotionDate = item.PromotionDate,
                };
                capabilityLevelView.CapabilityLevel = capabilityLevels.Find(m => m.CapabilityLevelID == capabilityLevelView.CapabilityLevelID)?.CapabilityLevelName;
                capabilityLevelViews.Add(capabilityLevelView);
            }
            return capabilityLevelViews;
        }

        /// <summary>
        /// 获取个人奖惩记录|前端呈现
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task<List<EmployeeRewardView>> GetEmployeeRewardViewsAsync(string employeeID)
        {
            var rewardRecordsViews = new List<EmployeeRewardView>();

            var rewardRecords = await _employeeRewardRepository.GetRecordListAsync(employeeID);
            var rewardTypeParams = new SettingDictionaryParams()
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeReward",
                SettingTypeValue = "RewardTypeCode",
            };
            var rewardTypeDict = await _settingDictionaryService.GetSettingDictionaryDict(rewardTypeParams);
            var rewardLevelParam = new SettingDictionaryParams()
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeReward",
                SettingTypeValue = "RewardLevelCode"
            };
            var rewardLevelDict = await _settingDictionaryService.GetSettingDictionaryDict(rewardLevelParam);
            foreach (var item in rewardRecords)
            {
                var rewardRecordView = new EmployeeRewardView
                {
                    EmployeeID = item.EmployeeID,
                    EmployeeRewardID = item.EmployeeRewardID,
                    RewardContent = item.RewardContent,
                    RewardDate = item.RewardDate,
                    RewardLevelCode = item.RewardLevelCode,
                    RewardTypeCode = item.RewardTypeCode,
                    RewardLevelName = rewardLevelDict.Find(m => m.Value.Equals(item.RewardLevelCode))?.Label?.ToString(),
                    RewardTypeName = rewardTypeDict.Find(m => m.Value.Equals(item.RewardTypeCode))?.Label?.ToString()
                };
                rewardRecordsViews.Add(rewardRecordView);
            }
            return rewardRecordsViews;
        }

        /// <summary>
        /// 获取员工在职信息|前端呈现内容
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="settingList"></param>
        /// <param name="departmentListInfos"></param>
        /// <param name="postList"></param>
        /// <returns></returns>
        private async Task<List<EmployeeEmploymentView>> GetEmployeeEmploymentViewsAsync(string employeeID, List<AdministrationDictionaryInfo> settingList, List<DepartmentListInfo> departmentListInfos, List<PostInfo> postList)
        {
            var employeeEmploymentViews = new List<EmployeeEmploymentView>();
            var formalPositions = new EmployeeEmploymentView
            {
                Title = "formalPosition",
                Children = await GetEmployeeEmploymentRecordViewsAsync(employeeID, settingList, departmentListInfos, postList)
            };
            employeeEmploymentViews.Add(formalPositions);
            var partTimePosition = new EmployeeEmploymentView
            {
                Title = "partTimePosition",
                Children = await GetEmployeePartTimePosition(employeeID, settingList, departmentListInfos, postList)
            };
            employeeEmploymentViews.Add(partTimePosition);
            var societyPosition = new EmployeeEmploymentView
            {
                Title = "socialPosition",
                Children = await GetEmployeeSocialPosition(employeeID, settingList, departmentListInfos, postList)
            };
            employeeEmploymentViews.Add(societyPosition);
            return employeeEmploymentViews;
        }

        /// <summary>
        /// 获取员工的任职记录
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="settingList">配置字典</param>
        /// <param name="departmentListInfos">部门字典</param>
        /// <param name="postList">岗位字典</param>
        /// <returns></returns>
        private async Task<List<EmployeeEmploymentRecordView>> GetEmployeeEmploymentRecordViewsAsync(string employeeID, List<AdministrationDictionaryInfo> settingList, List<DepartmentListInfo> departmentListInfos, List<PostInfo> postList)
        {
            var employeeEmploymentRecordViews = new List<EmployeeEmploymentRecordView>();
            var employeeRecords = await _employeeEmploymentRecordRepository.GetRecordListAsync(employeeID, EMPLOYEE_POSTTYPE_OFFICIAL_1);
            var titleSettingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeProfessionalPosition",
                SettingTypeValue = "ProfessionalTypeCode",
            };
            var titleSetting = await _settingDictionaryRepository.GetSettingDictionary(titleSettingDictionaryParams);
            foreach (var item in employeeRecords)
            {
                var employeeEmploymentRecordView = new EmployeeEmploymentRecordView
                {
                    EmployeeID = item.EmployeeID,
                    DepartmentID = item.DepartmentID,
                    EmployeeEmploymentRecordID = item.EmployeeEmploymentRecordID,
                    Post = item.Post,
                    StartDate = item.StartDate,
                    EndDate = item.EndDate,
                    Title = item.Title,
                    TitleName = item.Title.HasValue ? titleSetting.FirstOrDefault(m => m.SettingValue == item.Title.ToString())?.Description : null,
                    PostName = item.Post.HasValue ? postList.Find(m => m.PostID == item.Post.Value)?.PostName : null
                };
                var department = departmentListInfos.Find(m => item.DepartmentID.HasValue && m.DepartmentID == item.DepartmentID.Value);
                employeeEmploymentRecordView.DepartmentName = department != null ? department.LocalShowName : "";
                employeeEmploymentRecordViews.Add(employeeEmploymentRecordView);
            }
            return employeeEmploymentRecordViews;
        }

        private async Task<List<EmployeeEmploymentRecordView>> GetEmployeePartTimePosition(string employeeID, List<AdministrationDictionaryInfo> settingList, List<DepartmentListInfo> departmentListInfos, List<PostInfo> postList)
        {
            var employeeEmploymentRecordViews = new List<EmployeeEmploymentRecordView>();
            var employeeRecords = await _employeeEmploymentRecordRepository.GetRecordListAsync(employeeID, EMPLOYEE_POSTTYPE_PARTTIMEJOB_2);
            if (employeeRecords.Count == 0)
            {
                return [];
            }
            var titleSettingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeePartTimeJob"
            };
            var titleSetting = await _settingDictionaryRepository.GetSettingDictionary(titleSettingDictionaryParams);
            foreach (var item in employeeRecords)
            {
                var employeeEmploymentRecordView = new EmployeeEmploymentRecordView
                {
                    EmployeeID = item.EmployeeID,
                    DepartmentID = item.DepartmentID,
                    EmployeeEmploymentRecordID = item.EmployeeEmploymentRecordID,
                    Post = item.Post,
                    StartDate = item.StartDate,
                    EndDate = item.EndDate,
                    Title = item.Title,
                    TitleName = item.Title.HasValue ? titleSetting.FirstOrDefault(m => m.SettingValue == item.Title.ToString())?.Description : null,
                    PostName = item.Post.HasValue ? postList.Find(m => m.PostID == item.Post.Value)?.PostName : null
                };
                employeeEmploymentRecordViews.Add(employeeEmploymentRecordView);
            }
            return employeeEmploymentRecordViews;
        }

        private async Task<List<EmployeeEmploymentRecordView>> GetEmployeeSocialPosition(string employeeID, List<AdministrationDictionaryInfo> settingList, List<DepartmentListInfo> departmentListInfos, List<PostInfo> postList)
        {
            var employeeEmploymentRecordViews = new List<EmployeeEmploymentRecordView>();
            var employeeRecords = await _employeeEmploymentRecordRepository.GetRecordListAsync(employeeID, EMPLOYEE_POSTTYPE_SOCIETY_3);
            if (employeeRecords.Count == 0)
            {
                return [];
            }
            foreach (var item in employeeRecords)
            {
                var employeeEmploymentRecordView = new EmployeeEmploymentRecordView
                {
                    EmployeeID = item.EmployeeID,
                    EmployeeEmploymentRecordID = item.EmployeeEmploymentRecordID,
                    StartDate = item.StartDate,
                    EndDate = item.EndDate,
                    TitleName = item.TitleName,
                };
                employeeEmploymentRecordViews.Add(employeeEmploymentRecordView);
            }
            return employeeEmploymentRecordViews;
        }

        /// <summary>
        /// 获取在职信息|前端呈现内容
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <param name="settingList">配置字典</param>
        /// <param name="departmentListInfos">部门字典</param>
        /// <param name="postList">岗位字典</param>
        /// <param name="capabilityLevels">能级字典</param>
        /// <returns></returns>
        private async Task<List<EmployeeStaffDataView>> GetEmployeeStaffDataViewsAsync(string employeeID, List<AdministrationDictionaryInfo> settingList, List<DepartmentListInfo> departmentListInfos, List<PostInfo> postList, List<CapabilityLevelInfo> capabilityLevels)
        {
            //todo 在职信息字段调整
            var staffDataViews = new List<EmployeeStaffDataView>();
            var employeeStaffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(employeeID);
            var jobCategoryParams = new SettingDictionaryParams
            {
                SettingType = "HumanResources",
                SettingTypeCode = "EmployeeStaffData",
                SettingTypeValue = "JobCategoryCode"
            };
            var jobCategorySetting = await _settingDictionaryRepository.GetSettingDictionary(jobCategoryParams);
            var staffDataView = new EmployeeStaffDataView
            {
                EmployeeID = employeeStaffData.EmployeeID,
                DepartmentID = employeeStaffData.DepartmentID,
                EntryDate = employeeStaffData.EntryDate,
                FileID = employeeStaffData.FileID,
                HisEmployeeID = employeeStaffData.HisEmployeeID,
                HrpEmployeeID = employeeStaffData.HrpEmployeeID,
                JobCategoryCode = employeeStaffData.JobCategoryCode,
                EmployeeLevelCode = employeeStaffData.EmployeeLevelCode,
                PostType = employeeStaffData.PostType,
                Probation = employeeStaffData.Probation,
                StatusCode = employeeStaffData.StatusCode,
                Title = employeeStaffData.Title,
                TurnRegularDate = employeeStaffData.TurnRegularDate,
            };
            var department = departmentListInfos.Find(m => employeeStaffData.DepartmentID.HasValue && m.DepartmentID == employeeStaffData.DepartmentID.Value);
            staffDataView.DepartmentName = department != null ? department.LocalShowName : "";
            staffDataView.JobCategory = jobCategorySetting.FirstOrDefault(m => m.SettingValue == employeeStaffData.JobCategoryCode)?.Description;
            staffDataView.StaffStatusName = employeeStaffData.StatusCode;
            staffDataView.EmployeeLevelName = GetLocalShowName("EmployeeLevelCode", employeeStaffData.EmployeeLevelCode, settingList);
            staffDataViews.Add(staffDataView);
            return staffDataViews;
        }

        /// <summary>
        /// 保存在职相关信息
        /// </summary>
        /// <param name="onJobView"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<object> SaveOnJobInfoAsync(EmployeeOnJobView onJobView, string userID)
        {
            if (onJobView == null)
            {
                throw new ArgumentNullException(nameof(EmployeeOnJobView));
            }
            var employeeID = onJobView.EmployeeID;
            //保存在职信息
            if (onJobView.EmployeeStaffData?.Count > 0)
            {
                var staffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(employeeID);
                if (staffData == null)
                {
                    return false;
                }
                foreach (var record in onJobView.EmployeeStaffData)
                {
                    UpdateEmployeeStaffData(record, staffData, userID);
                }
            }
            //奖惩记录
            if (onJobView.EmployeeReward?.Count > 0)
            {
                var rewardDatas = await _employeeRewardRepository.GetRecordListAsync(employeeID);
                if (rewardDatas.Count <= 0)
                {
                    return false;
                }
                foreach (var record in onJobView.EmployeeReward)
                {
                    UpdateEmployeeRewardInfo(record, rewardDatas, userID);
                }
            }
            //任职记录
            if (onJobView.EmployeeEmploymentRecord?.Count > 0)
            {
                var employeeRecords = await _employeeEmploymentRecordRepository.GetAllRecordListAsync(employeeID);
                if (employeeRecords.Count <= 0)
                {
                    return false;
                }
                foreach (var record in onJobView.EmployeeEmploymentRecord.FirstOrDefault().Children)
                {
                    if (!string.IsNullOrEmpty(record.EmployeeEmploymentRecordID))// 修改
                    {
                        UpdateEmployeeEmploymentRecordInfo(record, employeeRecords, userID);
                        continue;
                    }
                    var addData = new EmployeeEmploymentRecordInfo
                    {
                        EmployeeID = employeeID,
                        HospitalID = _options.Value.HospitalID,
                        StartDate = record.StartDate.Value.Date,
                        EndDate = record.EndDate.HasValue ? record.EndDate.Value.Date : null,
                        DepartmentID = record.DepartmentID,
                        Post = record.Post,
                        Title = record.Title,
                        AddDateTime = DateTime.Now,
                        AddEmployeeID = userID,
                        ModifyDateTime = DateTime.Now,
                        ModifyEmployeeID = userID,
                        DeleteFlag = string.Empty,
                        TitleName = record.TitleName
                    };
                    addData.EmployeeEmploymentRecordID = addData.GetId();
                    var title = onJobView.EmployeeEmploymentRecord.FirstOrDefault()?.Title;
                    addData.PostType = title switch
                    {
                        "formalPosition" => EMPLOYEE_POSTTYPE_OFFICIAL_1,
                        "partTimePosition" => EMPLOYEE_POSTTYPE_PARTTIMEJOB_2,
                        "socialPosition" => EMPLOYEE_POSTTYPE_SOCIETY_3,
                        _ => 0,
                    };
                    _unitOfWork.GetRepository<EmployeeEmploymentRecordInfo>().Insert(addData);
                }
            }
            //岗位等级
            if (onJobView.EmployeeCapabilityLevel?.Count > 0)
            {
                var insertList = new List<EmployeeCapabilityLevelInfo>();
                var capabilityLevelDatas = await _employeeCapabilityLevelRepository.GetRecordListAsync(employeeID);
                foreach (var item in onJobView.EmployeeCapabilityLevel)
                {
                    var nursingLevelData = capabilityLevelDatas.Find(m => m.EmployeeCapabilityLevelID == item.EmployeeCapabilityLevelID);
                    if (nursingLevelData == null)
                    {
                        //新增
                        insertList.Add(CreateEmployeeCapabilityLevelInfo(employeeID, item));
                    }
                    else
                    {
                        bool updateFlag = false;
                        if (nursingLevelData.CapabilityLevelID != item.CapabilityLevelID)
                        {
                            nursingLevelData.CapabilityLevelID = item.CapabilityLevelID;
                            updateFlag = true;
                        }
                        ;
                        if (nursingLevelData.PromotionDate != item.PromotionDate)
                        {
                            nursingLevelData.PromotionDate = item.PromotionDate;
                            updateFlag = true;
                        }
                        if (updateFlag)
                        {
                            nursingLevelData.Modify(employeeID);
                        }
                    }
                }
                var lastCapabilityLevelID = onJobView.EmployeeCapabilityLevel.OrderBy(m => m.PromotionDate.Value).LastOrDefault();
                EmployeeStaffDataInfo staffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(employeeID);
                if (staffData != null && staffData.CapabilityLevelID != lastCapabilityLevelID.CapabilityLevelID)
                {
                    staffData.CapabilityLevelID = lastCapabilityLevelID.CapabilityLevelID;
                    _unitOfWork.GetRepository<EmployeeStaffDataInfo>().Update(staffData);
                }
                if (insertList.Count > 0)
                {
                    await _unitOfWork.GetRepository<EmployeeCapabilityLevelInfo>().InsertAsync(insertList);
                }
            }
            await _unitOfWork.SaveChangesAsync();
            await _employeeStaffDataRepository.UpdateCache();
            return true;
        }

        private EmployeeCapabilityLevelInfo CreateEmployeeCapabilityLevelInfo(string employeeID, EmployeeCapabilityLevelView item)
        {
            var result = new EmployeeCapabilityLevelInfo
            {
                EmployeeID = employeeID,
                HospitalID = _options.Value.HospitalID,
                CapabilityLevelID = item.CapabilityLevelID,
                PromotionDate = item.PromotionDate,
                AddDateTime = DateTime.Now,
                AddEmployeeID = employeeID,
                ModifyDateTime = DateTime.Now,
                ModifyEmployeeID = employeeID
            };
            result.EmployeeCapabilityLevelID = result.GetId();
            return result;
        }

        /// <summary>
        /// 更新任职记录
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="employeeRecords"></param>
        /// <param name="userID"></param>
        private static void UpdateEmployeeEmploymentRecordInfo(EmployeeEmploymentRecordView newData, List<EmployeeEmploymentRecordInfo> employeeRecords, string userID)
        {
            var update = false;
            var employeeRecord = employeeRecords.Find(m => m.EmployeeEmploymentRecordID == newData.EmployeeEmploymentRecordID);
            if (employeeRecord == null)
            {
                return;
            }
            if (employeeRecord.Post != newData.Post)
            {
                update = true;
                employeeRecord.Post = newData.Post;
            }
            if (employeeRecord.Title != newData.Title)
            {
                update = true;
                employeeRecord.Title = newData.Title;
            }
            if (employeeRecord.StartDate != newData.StartDate)
            {
                update = true;
                employeeRecord.StartDate = newData.StartDate;
            }
            if (employeeRecord.EndDate != newData.EndDate)
            {
                update = true;
                employeeRecord.EndDate = newData.EndDate;
            }
            if (employeeRecord.DepartmentID != newData.DepartmentID)
            {
                update = true;
                employeeRecord.DepartmentID = newData.DepartmentID;
            }
            if (employeeRecord.TitleName != newData.TitleName)
            {
                update = true;
                employeeRecord.TitleName = newData.TitleName;
            }
            if (update)
            {
                employeeRecord.Modify(userID);
            }
        }

        /// <summary>
        /// 更新奖惩记录
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="rewardDatas"></param>
        /// <param name="userID"></param>
        private void UpdateEmployeeRewardInfo(EmployeeRewardView newData, List<EmployeeRewardInfo> rewardDatas, string userID)
        {
            var update = false;
            if (string.IsNullOrEmpty(newData.EmployeeRewardID))
            {
                return;
            }
            var rewardData = rewardDatas.Find(m => m.EmployeeRewardID == newData.EmployeeRewardID);
            if (rewardData == null)
            {
                return;
            }
            if (rewardData.RewardContent != newData.RewardContent)
            {
                update = true;
                rewardData.RewardContent = newData.RewardContent;
            }
            if (rewardData.RewardDate != newData.RewardDate)
            {
                update = true;
                rewardData.RewardDate = newData.RewardDate;
            }
            if (rewardData.RewardLevelCode != newData.RewardLevelCode)
            {
                update = true;
                rewardData.RewardLevelCode = newData.RewardLevelCode;
            }
            if (rewardData.RewardTypeCode != newData.RewardTypeCode)
            {
                update = true;
                rewardData.RewardTypeCode = newData.RewardTypeCode;
            }
            if (update)
            {
                rewardData.ModifyEmployeeID = userID;
                rewardData.ModifyDateTime = DateTime.Now;
            }
        }

        /// <summary>
        /// 更新员工在职信息
        /// </summary>
        /// <param name="newData"></param>
        /// <param name="existedData"></param>
        /// <param name="userID"></param>
        private void UpdateEmployeeStaffData(EmployeeStaffDataInfo newData, EmployeeStaffDataInfo existedData, string userID)
        {
            var update = false;
            if (existedData.DepartmentID != newData.DepartmentID)
            {
                update = true;
                existedData.DepartmentID = newData.DepartmentID;
            }
            if (existedData.HisEmployeeID != newData.HisEmployeeID)
            {
                update = true;
                existedData.HisEmployeeID = newData.HisEmployeeID;
            }
            if (existedData.HrpEmployeeID != newData.HrpEmployeeID)
            {
                update = true;
                existedData.HrpEmployeeID = newData.HrpEmployeeID;
            }
            if (existedData.Probation != newData.Probation)
            {
                update = true;
                existedData.Probation = newData.Probation;
            }
            if (existedData.TurnRegularDate != newData.TurnRegularDate)
            {
                update = true;
                existedData.TurnRegularDate = newData.TurnRegularDate;
            }
            if (existedData.StatusCode != newData.StatusCode)
            {
                update = true;
                existedData.StatusCode = newData.StatusCode;
            }
            if (existedData.JobCategoryCode != newData.JobCategoryCode)
            {
                update = true;
                existedData.JobCategoryCode = newData.JobCategoryCode;
            }
            if (existedData.EmployeeLevelCode != newData.EmployeeLevelCode)
            {
                update = true;
                existedData.EmployeeLevelCode = newData.EmployeeLevelCode;
            }
            if (existedData.PostType != newData.PostType)
            {
                update = true;
                existedData.PostType = newData.PostType;
            }
            if (existedData.Title != newData.Title)
            {
                update = true;
                existedData.Title = newData.Title;
            }
            if (existedData.FileID != newData.FileID)
            {
                update = true;
                existedData.FileID = newData.FileID;
            }
            if (existedData.EntryDate != newData.EntryDate)
            {
                update = true;
                existedData.EntryDate = newData.EntryDate;
            }
            if (update)
            {
                existedData.Modify(userID);
                _unitOfWork.GetRepository<EmployeeStaffDataInfo>().Update(existedData);
            }
        }

        public async Task<PersonalFileHeader> GetPersonalFileHeaderInfoAsync(string employeeID)
        {
            var deptName = "";
            var professionalLevelName = "";

            var dictionaryInfos = await _administrationDictionaryRepository.GetAll();
            var capabilityLevels = await _capabilityLevelRepository.GetAll<CapabilityLevelInfo>();
            //部分基本信息
            var personalData = await _employeePersonalDataRepository.GetFieldValueByEmployeeIDAsync(employeeID, m => new { m.GenderCode, m.EmployeeName });
            //部分在职信息
            var staffData = await _employeeStaffDataRepository.GetFieldValueByEmployeeIDAsync(employeeID, m => new { m.EntryDate, m.FileID, m.CapabilityLevelID, m.DepartmentID });
            if (staffData == null)
            {
                return null;
            }
            if (staffData.DepartmentID.HasValue)
            {
                var deptInfo = await _departmentListRepository.GetByIDAsync(staffData.DepartmentID.Value);
                deptName = deptInfo?.LocalShowName;
            }

            var positionInfo = await _employeeProfessionalPositionRepository.GetCurrentByEmployeeID(employeeID);
            if (positionInfo != null)
            {
                var titleSettingDictionaryParams = new SettingDictionaryParams
                {
                    SettingType = "EmployeeManagement",
                    SettingTypeCode = "EmployeeProfessionalPosition",
                    SettingTypeValue = "ProfessionalTypeCode",
                };
                var titleSetting = await _settingDictionaryRepository.GetSettingDictionary(titleSettingDictionaryParams);
                professionalLevelName = titleSetting.FirstOrDefault(m => m.SettingValue == positionInfo.ProfessionalLevelCode)?.Description;
            }
            return new PersonalFileHeader
            {
                FileID = staffData.FileID,
                EntryDate = staffData.EntryDate,
                CapabilityLevelName = capabilityLevels.Find(m => staffData.CapabilityLevelID.HasValue && m.CapabilityLevelID == staffData.CapabilityLevelID)?.CapabilityLevelName,
                EmployeeName = personalData?.EmployeeName,
                DepartmentName = deptName ?? staffData.DepartmentID?.ToString(),
                Sex = await GetGender(personalData?.GenderCode),
                ProfessionalLevelName = professionalLevelName,
            };
        }

        /// <summary>
        /// 获取性别名
        /// </summary>
        private async Task<string> GetGender(string genderCode)
        {
            var administrationParams = new AdministrationParams()
            {
                //国标
                SettingTypeCode = "EmployeeProfileList",
                SettingValue = "12",
                ReferenceValue = "GenderCode",
            };
            var dictionarySettingList = await _dictionaryService.GetAdministrationDict(administrationParams);
            return dictionarySettingList.Find(m => m.Value.ToString() == genderCode)?.Label;
        }

        /// <summary>
        /// 获取员工个人信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<List<CollapseView>> GetEmployeePersonalData(string employeeID)
        {
            List<CollapseView> employeePersonalView = [];
            var administrationParams = new AdministrationParams()
            {
                SettingTypeCode = "EmployeeProfileList"
            };
            var settingList = await _administrationDictionaryRepository.GetDictionary(administrationParams);
            var personalData = await GetEmployPersonalData(employeeID, settingList);
            employeePersonalView.Add(personalData);
            var contact = await GetEmployeeContactView(employeeID);
            employeePersonalView.Add(contact);
            var educationalExperience = await GetEmployeeEducationalExperience(employeeID, settingList);
            employeePersonalView.Add(educationalExperience);
            var workExperienceList = await GetWorkExperience(employeeID);
            employeePersonalView.Add(workExperienceList);
            var professionalPositionList = await GetEmployeeProfessionalPosition(employeeID, settingList);
            employeePersonalView.Add(professionalPositionList);
            var relativesList = await GetEmployeeRelatives(employeeID, settingList);
            employeePersonalView.Add(relativesList);
            var clothingData = await GetEmployeeClothingSizeAsync(employeeID);
            employeePersonalView.Add(clothingData);
            var employeeStrength = await GetEmployeeStrengthAsync(employeeID);
            employeePersonalView.Add(employeeStrength);
            return employeePersonalView;
        }

        /// <summary>
        /// 获取人员个人基本信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="settingList"></param>
        /// <returns></returns>
        private async Task<CollapseView> GetEmployPersonalData(string employeeID, List<AdministrationDictionaryInfo> settingList)
        {
            var collapseView = new CollapseView
            {
                Title = "基本信息",
                Name = "basicInformation",
                Class = "Collapse"
            };
            var employeePersonalView = new EmployeePersonalDataView();
            var employeePersonalData = await _employeePersonalDataRepository.GetDataByEmployeeID(employeeID);
            if (employeePersonalData == null)
            {
                return collapseView;
            }
            // 获取生育状态配置
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "HumanResources",
                SettingTypeCode = "EmployeePersonalData",
                SettingTypeValue = "DeliverCode",
            };
            var dictionarySettingList = await _settingDictionaryService.GetSettingDictionaryDict(settingDictionaryParams);
            employeePersonalView.EmployeeID = employeePersonalData.EmployeeID;
            employeePersonalView.HospitalID = employeePersonalData.HospitalID;
            employeePersonalView.EmployeeName = employeePersonalData.EmployeeName;
            employeePersonalView.NamePinyin = employeePersonalData.NamePinyin;
            employeePersonalView.Gender = GetDescription("GenderCode", employeePersonalData.GenderCode, settingList);
            // 没有配置，需要追
            employeePersonalView.Nationality = GetDescription("NationalityCode", employeePersonalData.NationalityCode, settingList);
            employeePersonalView.Nation = GetDescription("NationCode", employeePersonalData.NationCode, settingList);
            employeePersonalView.IDCardNo = employeePersonalData.IDCardNo;
            employeePersonalView.Birthdate = employeePersonalData.Birthdate.HasValue ? employeePersonalData.Birthdate.Value.ToString("yyyy-MM-dd") : "";
            employeePersonalView.LunarBirthdate = employeePersonalData.LunarBirthdate.HasValue ? employeePersonalData.LunarBirthdate.Value.ToString("yyyy-MM-dd") : "";
            employeePersonalView.HomeAddress = employeePersonalData.HomeAddress;
            employeePersonalView.ActualAddress = employeePersonalData.ActualAddress;
            employeePersonalView.NativePlace = employeePersonalData.NativePlace;
            employeePersonalView.Marriage = GetDescription("MarriageCode", employeePersonalData.MarriageCode, settingList);
            employeePersonalView.DeliverCode = dictionarySettingList.Find(m => m.Value.ToString() == employeePersonalData.DeliverCode)?.Label?.ToString();
            employeePersonalView.Polity = GetDescription("PolityCode", employeePersonalData.PolityCode, settingList);
            collapseView.Data = employeePersonalView;
            return collapseView;
        }

        /// <summary>
        /// 获取联系方式
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<CollapseView> GetEmployeeContactView(string employeeID)
        {
            var employeeContactViewList = new List<EmployeeContactView>();
            var collapseView = new CollapseView()
            {
                Title = "联系方式",
                Name = "contactHeader",
                Class = "Table",
                Data = employeeContactViewList,
            };
            var contantDataList = await _employeeContactRepository.GetDataByEmployeeID(employeeID);
            if (contantDataList.Count == 0)
            {
                return collapseView;
            }
            contantDataList = contantDataList.OrderBy(m => m.ContactWayCode).ToList();
            var settingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "HumanResources",
                SettingTypeCode = "EmployeeContact",
                SettingTypeValue = "ContactWayCode"
            };
            var settingList = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            foreach (var contantData in contantDataList)
            {
                var employeeContactView = new EmployeeContactView
                {
                    EmployeeContactID = contantData.EmployeeContactID,
                    EmployeeID = contantData.EmployeeID,
                    HospitalID = contantData.HospitalID,
                    ContactWay = settingList.FirstOrDefault(m => m.SettingValue == contantData.ContactWayCode)?.Description,
                    ContactContent = contantData.ContactContent,
                    Remark = contantData.Remark
                };
                employeeContactViewList.Add(employeeContactView);
            }
            collapseView.Data = employeeContactViewList;
            return collapseView;
        }

        /// <summary>
        /// 获取教育经历
        /// </summary>
        /// <param name="employeeID"></param>
        /// <param name="settingList"></param>
        /// <returns></returns>
        private async Task<CollapseView> GetEmployeeEducationalExperience(string employeeID, List<AdministrationDictionaryInfo> settingList)
        {
            var collapseView = new CollapseView
            {
                Title = "教育经历",
                Name = "educationalHeader",
                Class = "Table"
            };
            var employeeEducationalExperienceViewList = new List<EmployeeEducationalExperienceView>();
            var educationalExperienceList = await _employeeEducationalExperienceRepository.GetListByEmployeeID(employeeID);
            if (educationalExperienceList.Count <= 0)
            {
                return collapseView;
            }
            var administrationParams = new AdministrationParams()
            {
                SettingTypeCode = "EmployeeProfileList",
            };
            var dictionarySettingList = await _administrationDictionaryRepository.GetDictionary(administrationParams);
            var settingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeEducationalExperience",
                SettingTypeValue = "EducationCode"
            };
            var educationCodeSetting = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            // 获取毕业状态配置
            var educationStatusSettingList = dictionarySettingList.Where(m => m.SettingValue == "206").ToList();
            var majorSettingList = dictionarySettingList.Where(m => m.SettingValue == "205").ToList();
            foreach (var educationa in educationalExperienceList)
            {
                var educationaView = new EmployeeEducationalExperienceView()
                {
                    EmployeeEducationalExperienceID = educationa.EmployeeEducationalExperienceID,
                    EmployeeID = educationa.EmployeeID,
                    HospitalID = educationa.HospitalID,
                    EducationName = educationCodeSetting.FirstOrDefault(m => m.SettingValue == educationa.EducationCode)?.Description,
                    GraduateSchool = educationa.GraduateSchool,
                    GraduationMajor = majorSettingList.Find(m => m.TypeValue == educationa.GraduationMajor)?.LocalShowName,
                    EntryDate = educationa.EntryDate.HasValue ? educationa.EntryDate.Value.ToString("yyyy-MM-dd") : "",
                    GraduationDate = educationa.GraduationDate.HasValue ? educationa.GraduationDate.Value.ToString("yyyy-MM-dd") : "",
                    EducationStatus = educationStatusSettingList.Find(m => m.TypeValue == educationa.EducationStatus)?.LocalShowName,
                    Remark = educationa.Remark,
                };
                if (educationa.SpecialFlag == "F")
                {
                    educationaView.EducationType = "第一学历";
                }
                if (educationa.SpecialFlag == "H")
                {
                    educationaView.EducationType = "最高学历";
                }
                employeeEducationalExperienceViewList.Add(educationaView);
            }
            collapseView.Data = employeeEducationalExperienceViewList;
            return collapseView;
        }

        /// <summary>
        /// 获取职称
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<CollapseView> GetEmployeeProfessionalPosition(string employeeID, List<AdministrationDictionaryInfo> settingList)
        {
            var collapseView = new CollapseView
            {
                Title = "职称",
                Name = "positionHeader",
                Class = "Table"
            };
            var professionalPositionView = new List<EmployeeProfessionalPositionView>();
            var professionalPositionList = await _employeeProfessionalPositionRepository.GetListByEmployeeID(employeeID);
            if (professionalPositionList.Count <= 0)
            {
                return collapseView;
            }
            var titleSettingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeProfessionalPosition",
                SettingTypeValue = "ProfessionalTypeCode",
            };
            var titleSetting = await _settingDictionaryRepository.GetSettingDictionary(titleSettingDictionaryParams);
            foreach (var professional in professionalPositionList)
            {
                var view = new EmployeeProfessionalPositionView
                {
                    EmployeeProfessionalPositionID = professional.EmployeeProfessionalPositionID,
                    EmployeeID = professional.EmployeeID,
                    HospitalID = professional.HospitalID,
                    ProfessionalType = titleSetting.FirstOrDefault(m => m.SettingValue == professional.ProfessionalTypeCode)?.Description,
                    ProfessionalLevel = titleSetting.FirstOrDefault(m => m.SettingValue == professional.ProfessionalLevelCode)?.Description,
                    ObtainingDate = professional.ObtainingDate.HasValue ? professional.ObtainingDate.Value.ToString("yyyy-MM-dd") : "",
                    CertificateNo = professional.CertificateNo
                };
                professionalPositionView.Add(view);
            }
            collapseView.Data = professionalPositionView;
            return collapseView;
        }

        /// <summary>
        /// 获取亲属关系
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<CollapseView> GetEmployeeRelatives(string employeeID, List<AdministrationDictionaryInfo> settingList)
        {
            var collapseView = new CollapseView
            {
                Title = "家庭信息",
                Name = "relativesHeader",
                Class = "Table"
            };
            var employeeRelativesViews = new List<EmployeeRelativesView>();
            var employeeRelativesList = await _employeeRelativesRepository.GetListByEmployeeID(employeeID);
            var settingDictionaryParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeEducationalExperience",
                SettingTypeValue = "EducationCode"
            };
            var educationCodeSetting = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            if (employeeRelativesList.Count <= 0)
            {
                return collapseView;
            }
            foreach (var relatives in employeeRelativesList)
            {
                var view = new EmployeeRelativesView
                {
                    EmployeeRelativesID = relatives.EmployeeRelativesID,
                    EmployeeID = relatives.EmployeeID,
                    HospitalID = relatives.HospitalID,
                    Relationship = GetDescription("RelationshipCode", relatives.RelationshipCode, settingList),
                    RelativesName = relatives.RelativesName,
                    RelativesPhoneNumber = relatives.RelativesPhoneNumber,
                    RelativesIDCardNo = relatives.RelativesIDCardNo,
                    RelativesNativePlace = relatives.RelativesNativePlace,
                    RelativesEducation = educationCodeSetting.FirstOrDefault(m => m.SettingValue == relatives.RelativesEducation)?.Description,
                    RelativesSchool = relatives.RelativesSchool,
                    RelativesCompany = relatives.RelativesCompany,
                    RelativesPost = relatives.RelativesPost,
                };
                employeeRelativesViews.Add(view);
            }
            collapseView.Data = employeeRelativesViews;
            return collapseView;
        }

        /// <summary>
        /// 获取Code配置
        /// </summary>
        /// <param name="referenceValue"></param>
        /// <param name="typeValue"></param>
        /// <param name="settingList"></param>
        /// <returns></returns>
        private string GetDescription(string referenceValue, string typeValue, List<AdministrationDictionaryInfo> settingList)
        {
            return settingList.Find(m => m.ReferenceValue == referenceValue && m.TypeValue == typeValue)?.Description ?? string.Empty;
        }

        /// <summary>
        /// 获取工作经历
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<CollapseView> GetWorkExperience(string employeeID)
        {
            var collapseView = new CollapseView
            {
                Title = "工作经历",
                Name = "workExperienceHeader",
                Class = "Table"
            };
            var workExperienceViewList = new List<EmployeeWorkExperienceView>();
            var employeeWorkExperienceList = await _employeeWorkExperienceRepository.GetListByEmployeeID(employeeID);
            foreach (var work in employeeWorkExperienceList)
            {
                var view = new EmployeeWorkExperienceView
                {
                    EmployeeWorkExperienceID = work.EmployeeWorkExperienceID,
                    EmployeeID = work.EmployeeID,
                    HospitalID = work.HospitalID,
                    StartDate = work.StartDate.HasValue ? work.StartDate.Value.ToString("yyyy-MM-dd") : "",
                    EndDate = work.EndDate.HasValue ? work.EndDate.Value.ToString("yyyy-MM-dd") : "",
                    Company = work.Company,
                    Department = work.Department,
                    Remark = work.Remark
                };
                workExperienceViewList.Add(view);
            }
            collapseView.Data = workExperienceViewList;
            return collapseView;
        }

        #region 人员借调

        /// <summary>
        /// 获取人员借调数据
        /// </summary>
        /// <param name="employeeID">员工编号</param>
        /// <param name="effectiveRecordSwitch">有效记录开关， True:显示有效记录，结束时间在当前时间之后的数据并且没有撤销审批的数据 False:显示历史记录，结束时间在当前时间之前的数据或者已经撤销审批的数据</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordView>> GetEmployeeSecondmentRecordList(string employeeID, bool effectiveRecordSwitch, Session session)
        {
            var viewList = new List<EmployeeSecondmentRecordView>();
            //获取借调记录
            var recordList = await (!string.IsNullOrEmpty(employeeID) ? _employeeSecondmentRecordRepository.GetDataByEmployeeID(employeeID) : GetEmployeeSecondmenRecordBySessionRoles(session));
            recordList = effectiveRecordSwitch ? recordList.Where(m => m.EndDate.Date >= DateTime.Now.Date && m.StatusCode != "4").ToList() : recordList.Where(m => m.EndDate.Date < DateTime.Now.Date || m.StatusCode == "4").ToList();
            if (recordList.Count <= 0)
            {
                return viewList;
            }
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "ShiftManagement",
                SettingTypeCode = "EmployeeSecondmentRecord"
            };
            var dictionarySettingList = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            var typeSettingList = dictionarySettingList.Where(m => m.SettingTypeValue == "SecondmentType").ToList();
            var purposeList = dictionarySettingList.Where(m => m.SettingTypeValue == "SecondmentPurpose").ToList();
            var noonSettingParams = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            //审批记录
            var secondmentRecordIDList = recordList.Select(m => m.EmployeeSecondmentRecordID).ToList();
            var mainIDArrays = await _approveRecordRepository.GetMianIDArrayBySourceIDs(secondmentRecordIDList);
            var approveMainList = await _approveMainRepository.GetApproveMainsByMainIDsAsync(mainIDArrays);
            var approveDetailInfos = await _approveDetailRepository.GetApproveDetailsByMainIDAsync(mainIDArrays);
            var approveEmployeeIDList = approveDetailInfos.Select(m => m.ApproveEmployeeID).ToList();
            var preEmployeeIDList = approveDetailInfos.SelectMany(m => m.PreApproveEmployeeID.Split("||").ToList()).ToList();
            var noonSetting = await _settingDictionaryRepository.GetSettingDictionary(noonSettingParams);
            //审批状态
            var approvalStatusParams = new SettingDictionaryParams()
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveRecord",
                SettingTypeValue = "StatusCode"
            };
            var secondmentRecordEmployeeIDList = recordList.Select(m => m.EmployeeID).ToList();
            secondmentRecordEmployeeIDList.AddRange(approveEmployeeIDList);
            secondmentRecordEmployeeIDList.AddRange(preEmployeeIDList);
            var employeePersonalList = await _employeePersonalDataRepository.GetListByEmployeeIDs(secondmentRecordEmployeeIDList);
            var statusDict = await _settingDictionaryService.GetSettingDictionaryDict(approvalStatusParams);
            var departmentList = await _departmentListRepository.GetByOrganizationType("1");
            if (employeePersonalList.Count <= 0 || departmentList.Count <= 0)
            {
                return viewList;
            }
            recordList = recordList.OrderByDescending(m => m.StartDate).ThenByDescending(m => m.EndDate).ThenByDescending(m => m.AddDateTime).ToList();
            foreach (var record in recordList)
            {
                var view = new EmployeeSecondmentRecordView()
                {
                    EmployeeSecondmentRecordID = record.EmployeeSecondmentRecordID,
                    EmployeeID = record.EmployeeID,
                    EmployeeName = employeePersonalList.FirstOrDefault(m => m.EmployeeID == record.EmployeeID)?.EmployeeName ?? "",
                    DepartmentName = departmentList.Find(m => m.DepartmentID == record.DepartmentID)?.DepartmentContent,
                    DepartmentID = record.DepartmentID,
                    SecondmentDepartmentName = departmentList.Find(m => m.DepartmentID == record.SecondmentDepartmentID)?.DepartmentContent,
                    SecondmentDepartmentID = record.SecondmentDepartmentID,
                    StartDate = record.StartDate,
                    EndDate = record.EndDate,
                    SecondmentPurpose = record.SecondmentPurpose,
                    SecondmentPurposeName = purposeList.FirstOrDefault(m => m.SettingValue == record.SecondmentPurpose)?.Description,
                    SecondmentType = record.SecondmentType,
                    SecondmentTypeName = typeSettingList.FirstOrDefault(m => m.SettingValue == record.SecondmentType)?.Description,
                    StartNoon = record.StartNoon,
                    StartNoonName = noonSetting.FirstOrDefault(m => m.SettingValue == record.StartNoon)?.Description,
                    EndNoon = record.EndNoon,
                    EndNoonName = noonSetting.FirstOrDefault(m => m.SettingValue == record.EndNoon)?.Description,
                    NamePinyin = employeePersonalList.FirstOrDefault(m => m.EmployeeID == record.EmployeeID)?.NamePinyin ?? "",
                    SecondmentDays = record.SecondmentDays,
                    Status = statusDict.Find(m => m.Value.Equals(record.StatusCode))?.Label?.ToString(),
                    StatusCode = record.StatusCode,
                    AddDateTime = record.AddDateTime,
                    ActualEndDate = record.ActualEndDate,
                    ActualEndNoon = record.ActualEndNoon,
                    ActualEndNoonName = noonSetting.FirstOrDefault(m => m.SettingValue == record.ActualEndNoon)?.Description,
                    ApproveFlag = approveMainList.Any(m => m.ApproveRecordID == record.ApproveRecordID),
                };
                var recordApproveMainList = approveMainList.Where(m => m.ApproveRecordID == record.ApproveRecordID).ToList();
                //如果所有节点已经审批
                if (recordApproveMainList.Find(m => !m.ApproveDateTime.HasValue) == null)
                {
                    var approveMainid = recordApproveMainList.FirstOrDefault(m => string.IsNullOrEmpty(m.NextApproveMainID) && m.ApproveRecordID == record.ApproveRecordID)?.ApproveMainID;
                    var approveDetail = approveDetailInfos.Where(m => m.ApproveMainID == approveMainid && m.ApproveDateTime.HasValue).OrderByDescending(m => m.ApproveDateTime).FirstOrDefault();
                    view.ApproveEmployeeName = employeePersonalList.FirstOrDefault(m => m.EmployeeID == approveDetail?.ApproveEmployeeID)?.EmployeeName ?? "";
                    view.ApproveEmployeeID = approveDetail?.ApproveEmployeeID;
                }
                else
                {
                    var notApproveMainList = recordApproveMainList.Where(m => !m.ApproveDateTime.HasValue && m.ApproveRecordID == record.ApproveRecordID).ToList();
                    var nextApproveMainIDList = notApproveMainList.Where(m => !string.IsNullOrEmpty(m.NextApproveMainID)).Select(m => m.NextApproveMainID).ToList();
                    var approveMainid = nextApproveMainIDList.Count > 0 ? notApproveMainList.FirstOrDefault(m => !nextApproveMainIDList.Any(n => n == m.ApproveMainID))?.ApproveMainID : approveMainList.FirstOrDefault(m => m.ApproveRecordID == record.ApproveRecordID)?.ApproveMainID;
                    var approveDetail = approveDetailInfos.FirstOrDefault(m => m.ApproveMainID == approveMainid && !m.ApproveDateTime.HasValue);
                    var preApproveEmployeeIDList = approveDetail?.PreApproveEmployeeID.Split("||").ToList();
                    var preApproveEmployeeName = employeePersonalList.Where(m => preApproveEmployeeIDList != null && preApproveEmployeeIDList.Any(n => n == m.EmployeeID)).Select(m => m.EmployeeName).ToList();
                    view.ApproveEmployeeID = approveDetail?.PreApproveEmployeeID;
                    view.ApproveEmployeeName = string.Join(", ", preApproveEmployeeName);
                }
                viewList.Add(view);
            }
            return [.. viewList.OrderBy(m => m.AddDateTime)];
        }

        /// <summary>
        /// 根据权限获取人员借调数据
        /// </summary>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        private async Task<List<EmployeeSecondmentRecordInfo>> GetEmployeeSecondmenRecordBySessionRoles(Session session)
        {
            var recordList = new List<EmployeeSecondmentRecordInfo>();
            var settingDictionaryParams = new SettingDictionaryParams()
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeSecondmentRecord",
                SettingTypeValue = "GetAlllDataLimits"
            };
            var getAllDattaLimitsSetting = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryParams);
            if (session.Roles.Any(role => getAllDattaLimitsSetting.Any(setting => setting.SettingValue == role.ToString())))
            {
                //护理部管理人员权限获取所有数据
                recordList = await _employeeSecondmentRecordRepository.GetAllData();
                return recordList;
            }
            if (session.Roles.Any(m => m == 40))
            {
                //病区及片区主任获取对应部门数据
                var departments = await _employeeToDepartmentRepository.GetDepartmentByEmployeeIDAndType(session.EmployeeID, "1");
                var departmentIDs = departments.Select(m => m.DepartmentID).ToList();
                //若没有员工对应部门信息，这里使用员工当前所在部门获取数据
                if (departmentIDs.Count <= 0)
                {
                    departmentIDs.Add(session.DepartmentID);
                }
                recordList = await _employeeSecondmentRecordRepository.GetDataByDepartmentIDList(departmentIDs);
                return recordList;
            }
            //普通护士获取个人数据
            recordList = await _employeeSecondmentRecordRepository.GetDataByEmployeeID(session.EmployeeID);
            return recordList;
        }

        /// <summary>
        /// 删除人员借调数据
        /// </summary>
        /// <param name="employeeSecondmentRecordID"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteEmployeeSecondmentRecord(string employeeSecondmentRecordID, string employeeID)
        {
            var record = await _employeeSecondmentRecordRepository.GetDataByID(employeeSecondmentRecordID);
            if (record == null)
            {
                return false;
            }
            record.Delete(employeeID);
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                var apparoveRecordService = _serviceProvider.GetService<IApproveRecordService>();
                await apparoveRecordService.StopApprovalAsync(record.EmployeeSecondmentRecordID, employeeID, true);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 保存人员借调数据
        /// </summary>
        /// <param name="recordView">主记录视图</param>
        /// <returns></returns>
        public async Task<SaveReponseView> SaveEmployeeSecondmentRecord(EmployeeSecondmentRecordView recordView)
        {
            var result = new SaveReponseView();
            if (recordView == null)
            {
                return result;
            }
            var updateSecondmentTypeFlag = false;
            var record = new EmployeeSecondmentRecordInfo();
            if (!string.IsNullOrEmpty(recordView.EmployeeSecondmentRecordID))
            {
                record = await _employeeSecondmentRecordRepository.GetDataByID(recordView.EmployeeSecondmentRecordID);
                if (record.SecondmentType != recordView.SecondmentType)
                {
                    updateSecondmentTypeFlag = true;
                    record.SecondmentType = recordView.SecondmentType;
                }
                record.EmployeeID = recordView.EmployeeID;
                record.DepartmentID = recordView.DepartmentID;
                record.SecondmentDepartmentID = recordView.SecondmentDepartmentID;
                record.StartDate = recordView.StartDate;
                record.EndDate = recordView.EndDate;
                record.StartNoon = recordView.StartNoon;
                record.EndNoon = recordView.EndNoon;
                record.SecondmentPurpose = recordView.SecondmentPurpose;
                record.SecondmentDays = recordView.SecondmentDays;
                if (recordView.ActualEndDate.HasValue && !string.IsNullOrEmpty(recordView.ActualEndNoon))
                {
                    record.ActualEndDate = recordView.ActualEndDate;
                    record.ActualEndNoon = recordView.ActualEndNoon;
                }
                record.Modify(recordView.UserID);
            }
            else
            {
                // 判断时间间隔内是否已经存在对应的借调记录
                var secondmentRecordInfo = await CheckSecondmentRecordInRange(recordView);
                if (secondmentRecordInfo != null)
                {
                    result.ResponseMessage = await GetTipMessage(secondmentRecordInfo);
                    return result;
                }
                record = await CreateEmployeeSecondmentRecordInfo(recordView);
            }
            var success = await _unitOfWork.SaveChangesAsync() >= 0;
            result.RecordSaveFlag = success;
            if (!success)
            {
                return result;
            }
            if (recordView.EarlyClosureFlag)
            {
                await DeelEmployeeSecondmentScheduleData(record, recordView.UserID);
            }
            result.ApproveSaveFlag = await CreateOrUpdateEmployeeSecondmentApprove(record, updateSecondmentTypeFlag);
            return result;
        }

        /// <summary>
        /// 获取检核到时间冲突时的提示文本
        /// </summary>
        /// <param name="secondmentRecordInfo">有时间冲突的借调记录</param>
        /// <returns>提示文本信息</returns>
        private async Task<string> GetTipMessage(EmployeeSecondmentRecordInfo secondmentRecordInfo)
        {
            var departmentList = await _departmentListRepository.GetDepartmentIDByIDsAsync([secondmentRecordInfo.SecondmentDepartmentID]);
            //借调部门
            var secondmentDepartmentName = departmentList.FirstOrDefault()?.LocalShowName;
            //借调实际结束时间
            var endDate = secondmentRecordInfo.ActualEndDate ?? secondmentRecordInfo.EndDate;
            var settingDictionaryarams = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            // 午别配置名称
            var settingDict = await _settingDictionaryRepository.GetSettingDictionary(settingDictionaryarams);
            var startNoonStr = settingDict.Find(m => m.SettingValue == secondmentRecordInfo.StartNoon)?.Description ?? "";
            var endNoonStr = settingDict.Find(m => !string.IsNullOrEmpty(secondmentRecordInfo.ActualEndNoon)
                ? m.SettingValue == secondmentRecordInfo.ActualEndNoon : m.SettingValue == secondmentRecordInfo.StartNoon)?.Description ?? "";
            // 返回提示文本
            return $"{secondmentRecordInfo.StartDate:yyyy-MM-dd}{startNoonStr}至{endDate:yyyy-MM-dd}{endNoonStr} 借调到{secondmentDepartmentName}";
        }

        /// <summary>
        /// 判断时间间隔内是否已经存在借调记录
        /// </summary>
        /// <param name="recordView">借调记录保存参数</param>
        /// <returns>是否在时间范围内已经存在借调记录</returns>
        private async Task<EmployeeSecondmentRecordInfo> CheckSecondmentRecordInRange(EmployeeSecondmentRecordView recordView)
        {
            var secondmentRecords = await _employeeSecondmentRecordRepository.GetPartDataByEmployeeIDsAndDateRange(new List<string>() { recordView.EmployeeID }, recordView.StartDate, recordView.EndDate);
            if (secondmentRecords.Count <= 0)
            {
                return null;
            }
            foreach (var secondmentItem in secondmentRecords)
            {
                var actualEndDate = secondmentItem.ActualEndDate ?? secondmentItem.EndDate;
                // 临界时间交叉日 - 判断午别是否交叉 交叉返回true，不交叉返回false
                if (secondmentItem.StartDate == recordView.EndDate)
                {
                    if (int.TryParse(secondmentItem.StartNoon, out var oldNoon) && int.TryParse(recordView.EndNoon, out var newNoon))
                    {
                        // 区间右侧 时间交叉
                        if (oldNoon <= newNoon)
                        {
                            return secondmentItem;
                        }
                    }
                }
                else if (actualEndDate == recordView.StartDate)
                {
                    if (int.TryParse(secondmentItem.EndNoon, out var oldNoon) && int.TryParse(recordView.StartNoon, out var newNoon))
                    {
                        // 区间左侧 时间交叉
                        if (oldNoon >= newNoon)
                        {
                            return secondmentItem;
                        }
                    }
                }
                else
                {
                    return secondmentItem;
                }
            }
            // 说明所有的可能交叉项目为空
            return null;
        }

        /// <summary>
        /// 创建借调对象实例
        /// </summary>
        /// <param name="recordView"></param>
        /// <returns></returns>
        private async Task<EmployeeSecondmentRecordInfo> CreateEmployeeSecondmentRecordInfo(EmployeeSecondmentRecordView recordView)
        {
            var record = new EmployeeSecondmentRecordInfo
            {
                HospitalID = recordView.HospitalID,
                EmployeeID = recordView.EmployeeID,
                DepartmentID = recordView.DepartmentID,
                SecondmentDepartmentID = recordView.SecondmentDepartmentID,
                StartDate = recordView.StartDate,
                EndDate = recordView.EndDate,
                StartNoon = recordView.StartNoon,
                EndNoon = recordView.EndNoon,
                SecondmentPurpose = recordView.SecondmentPurpose,
                SecondmentType = recordView.SecondmentType,
                SecondmentDays = recordView.SecondmentDays,
                StatusCode = "0",
                DeleteFlag = "",
                ActualEndDate = recordView.EndDate,
                ActualEndNoon = recordView.EndNoon,
            };
            record.Add(recordView.UserID);
            record.Modify(recordView.UserID);
            record.EmployeeSecondmentRecordID = record.GetId();
            record.ApproveRecordID = record.GetId();
            await _unitOfWork.GetRepository<EmployeeSecondmentRecordInfo>().InsertAsync(record);
            return record;
        }

        /// <summary>
        /// 获取借调记录
        /// </summary>
        /// <param name="type"></param>
        /// <param name="departmentID"></param>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <returns></returns>
        public async Task<List<EmployeeSecondmentRecordInfo>> GetSecondmentRecordByDepartmentIDAndDate(int type, int departmentID, DateTime startDate, DateTime endDate)
        {
            var list = new List<EmployeeSecondmentRecordInfo>();
            // 借调到本部门人员的借调记录
            if (type == 1)
            {
                list = await _employeeSecondmentRecordRepository.GetDataBySecondmentDepartmentID(departmentID, startDate, endDate);
            }
            else
            {
                // 本部门借调出去人员的借调记录
                list = await _employeeSecondmentRecordRepository.GetDataByDepartmentID(departmentID, startDate, endDate);
            }
            return list;
        }

        public async Task<List<EmployeeSecondmentRecordInfo>> GetSecondmentRecordByEmployeeIDs(List<string> employeeIDs, DateTime startDate, DateTime endDate)
        {
            return await _employeeSecondmentRecordRepository.GetPartDataByEmployeeIDsAndDateRange(employeeIDs, startDate, endDate);
        }

        /// <summary>
        /// 创建对应的审批记录
        /// </summary>
        /// <param name="employeeSecondmentRecordInfo">人员借调信息</param>
        /// <param name="updateSecondmentTypeFlag">是否修改借调类型</param>
        /// <returns>是否创建成功</returns>
        public async Task<bool> CreateOrUpdateEmployeeSecondmentApprove(EmployeeSecondmentRecordInfo employeeSecondmentRecordInfo, bool updateSecondmentTypeFlag)
        {
            //return false;
            var approveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            var departmentList = await _departmentListRepository.GetByCacheAsync();
            if (departmentList.Count <= 0)
            {
                _logger.Error($"获取字典数据失败");
                return false;
            }
            string proveCategory = employeeSecondmentRecordInfo.SecondmentType switch
            {
                "40" => "HR-041",
                "60" => "HR-042",
                "70" => "HR-043",
                _ => ""
            };
            if (string.IsNullOrEmpty(proveCategory))
            {
                _logger.Error($"人员借调类型没有找到对应的审批类型，人员借调类型Type：{employeeSecondmentRecordInfo.SecondmentType}");
                return false;
            }
            //调用添加审批
            var view = await CreateEmployeeSecondmentApproveViewAsync(employeeSecondmentRecordInfo, departmentList, proveCategory);
            var (approveProcessID, contentTemplate) = await _approveProcessService.GetProcessIDAndContentTemplateByTypeAndDepartmentID(view.ProveCategory, view.DepartmentID);
            if (string.IsNullOrWhiteSpace(approveProcessID))
            {
                _logger.Warn($"未找到审批流程，ProveCategory={view.ProveCategory},DepartmentID={view.DepartmentID}");
                return false;
            }
            //审批人
            if (updateSecondmentTypeFlag)
            {
                //先停止原审批流程
                await approveRecordService.StopApprovalAsync(employeeSecondmentRecordInfo.EmployeeSecondmentRecordID, employeeSecondmentRecordInfo.ModifyEmployeeID, true);
                //新增审批
                employeeSecondmentRecordInfo.ApproveRecordID = employeeSecondmentRecordInfo.GetId();
                view = await CreateEmployeeSecondmentApproveViewAsync(employeeSecondmentRecordInfo, departmentList, proveCategory);
                view.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                return await approveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID).ConfigureAwait(false);
            }
            var approveRecordInfo = await _approveRecordRepository.GetApproveRecordByRecordIDAsync(employeeSecondmentRecordInfo.ApproveRecordID);
            if (approveRecordInfo == null)
            {
                view.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                if (!await approveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID).ConfigureAwait(false))
                {
                    return false;
                }
            }
            else
            {
                approveRecordInfo.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                approveRecordInfo.Modify(view.AddEmployeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 创建人员借调审批需要的参数
        /// </summary>
        /// <param name="employeeSecondmentRecordInfo"></param>
        /// <param name="departmentList"></param>
        /// <returns></returns>
        private async Task<ApproveMainAndDetailParamView> CreateEmployeeSecondmentApproveViewAsync(EmployeeSecondmentRecordInfo employeeSecondmentRecordInfo, List<DepartmentListInfo> departmentList, string proveCategory)
        {
            var approveEmployeeIDs = new List<string>();
            var noonParam = new SettingDictionaryParams()
            {
                SettingType = "PositionManagement",
                SettingTypeCode = "JobPositions",
                SettingTypeValue = "NoonType"
            };
            var employeeDatas = await _employeePersonalDataRepository.GetDataByEmployeeID(employeeSecondmentRecordInfo.EmployeeID);
            var noonSetting = await _settingDictionaryRepository.GetSettingDictionary(noonParam);
            var noonPairs = noonSetting.Select(m => new KeyValueString { Key = m.SettingValue.ToString(), Value = m.Description }).ToList();
            // 获取上级科室ID
            var upperDeptIDs = await _dictionaryService.GetUpperDepts<int, List<int>>(employeeSecondmentRecordInfo.DepartmentID, m => m.DepartmentID);
            var oneLevelDepartment = departmentList.FirstOrDefault(m => upperDeptIDs.Contains(m.DepartmentID) && m.Level == 1 && m.UpperLevelDepartmentID == 0);
            if (oneLevelDepartment != null)
            {
                //片区调配使用的是业务审批人，这里是为了找到片区主任
                approveEmployeeIDs = await _employeeToDepartmentRepository.GetEmployeeByDepartmentIDAndType(oneLevelDepartment.DepartmentID, oneLevelDepartment.OrganizationType);
            }
            return new ApproveMainAndDetailParamView
            {
                SourceID = employeeSecondmentRecordInfo.EmployeeSecondmentRecordID,
                ApproveRecordID = employeeSecondmentRecordInfo.ApproveRecordID,
                ProveCategory = proveCategory,
                DepartmentID = employeeSecondmentRecordInfo.SecondmentDepartmentID,
                AddEmployeeID = employeeSecondmentRecordInfo.AddEmployeeID,
                AdjustEmployeeName = employeeDatas?.EmployeeName,
                DepartmentName = departmentList.Find(m => m.DepartmentID == employeeSecondmentRecordInfo.SecondmentDepartmentID)?.DepartmentContent,
                OriginalDepartmentName = departmentList.Find(m => m.DepartmentID == employeeSecondmentRecordInfo.DepartmentID)?.DepartmentContent,
                StartDate = employeeSecondmentRecordInfo.StartDate.ToLongDateString(),
                EndDate = employeeSecondmentRecordInfo.EndDate.ToLongDateString(),
                StartNoon = noonPairs.Find(m => m.Key == employeeSecondmentRecordInfo.StartNoon)?.Value,
                EndNoon = noonPairs.Find(m => m.Key == employeeSecondmentRecordInfo.EndNoon)?.Value,
                SecondmentDays = employeeSecondmentRecordInfo.SecondmentDays,
                SelfSelectedApprover = approveEmployeeIDs,
            };
        }

        #endregion

        #region 带教关系

        /// <summary>
        /// 获取带教关系及员工信息
        /// </summary>
        /// <param name="departmentID">科室ID</param>
        /// <returns></returns>
        public async Task<Dictionary<string, List<EmployeeTeachingRelationView>>> GetEmployeeTeachingRelationView(int departmentID)
        {
            var view = new Dictionary<string, List<EmployeeTeachingRelationView>>();
            var employeeList = await _employeeStaffDataRepository.GetEmployeeViewByDepartmentIDAsync(departmentID);
            if (employeeList.Count <= 0 || employeeList == null)
            {
                _logger.Warn($"未获取到科室人员信息，科室ID：{departmentID}");
                return view;
            }
            var employeeIDs = employeeList.Where(m => (m.CapabilityLevelID != 1 && m.CapabilityLevelID.HasValue) || (m.CapabilityLevelID == 1 || !m.CapabilityLevelID.HasValue)).Select(m => m.EmployeeID).ToList();
            var employeeTeachingRelations = await _employeeTeachintRelationRepository.GetEmployeeTeachingRelation(employeeIDs);
            if (employeeTeachingRelations.Count <= 0 || employeeTeachingRelations == null)
            {
                _logger.Warn($"未获取到科室人员带教信息，科室ID：{departmentID}");
            }
            var capabilityLevelList = await _capabilityLevelRepository.GetByCacheAsync();
            var settingList = await _administrationDictionaryRepository.GetAll();
            var employeeTeachingRelationList = CreateEmployeeTeachingRelationView(employeeList, employeeTeachingRelations, capabilityLevelList, settingList);
            //带教关系视图
            view.Add("employeeTeachingRelationList", employeeTeachingRelationList);
            //无证护士
            //var employeeN0List = employeeList.Where(m => m.CapabilityLevelID == 1 || !m.CapabilityLevelID.HasValue).ToList();
            //var employeeNOViews = CreateEmployeeView(employeeN0List, capabilityLevelList, settingList);
            var employeeNOViews = CreateEmployeeView(employeeList, capabilityLevelList, settingList);
            view.Add("studyEmployeeList", employeeNOViews);
            //带教老师
            var employeeTeacherList = employeeList.Where(m => m.CapabilityLevelID != 1 && m.CapabilityLevelID.HasValue).ToList();
            var employeeTeacherViews = CreateEmployeeView(employeeTeacherList, capabilityLevelList, settingList);
            view.Add("teacherEmployeeList", employeeTeacherViews);
            return view;
        }

        /// <summary>
        /// 创建带教关系视图
        /// </summary>
        /// <param name="employeeList">护士工号</param>
        /// <param name="employeeTeachingRelations">带教关系</param>
        /// <param name="capabilityLevelList">层级字典数据</param>
        /// <param name="settingList">配置字典数据</param>
        /// <returns></returns>
        private List<EmployeeTeachingRelationView> CreateEmployeeTeachingRelationView(List<EmployeeListView> employeeList, List<EmployeeTeachingRelationInfo> employeeTeachingRelations
            , List<CapabilityLevelInfo> capabilityLevelList, List<AdministrationDictionaryInfo> settingList)
        {
            var employeeTeachingRelationList = new List<EmployeeTeachingRelationView>();
            foreach (var employeeTeachingRelation in employeeTeachingRelations)
            {
                var employee = employeeList.FirstOrDefault(m => m.EmployeeID == employeeTeachingRelation.EmployeeID);
                var employeeTeacher = employeeList.FirstOrDefault(m => m.EmployeeID == employeeTeachingRelation.TeacherEmployeeID);
                if (employee == null || employeeTeacher == null)
                {
                    continue;
                }
                var returnView = new EmployeeTeachingRelationView
                {
                    EmployeeTeachingRelationID = employeeTeachingRelation.EmployeeTeachingRelationID,
                    DepartmentName = employee.DepartmentName,
                    EmployeeID = employee.EmployeeID,
                    EmployeeName = employee.EmployeeName,
                    CapabilityLevel = employee.CapabilityLevelID.HasValue ? capabilityLevelList.FirstOrDefault(m => m.CapabilityLevelID == employee.CapabilityLevelID)?.CapabilityLevelName : "",
                    Gender = GetLocalShowName("GenderCode", employee.GenderCode, settingList),
                    TeacherEmployeeID = employeeTeacher.EmployeeID,
                    TeacherName = employeeTeacher.EmployeeName,
                    TeacherCapabilityLevel = employeeTeacher.CapabilityLevelID.HasValue ? capabilityLevelList.FirstOrDefault(m => m.CapabilityLevelID == employeeTeacher.CapabilityLevelID)?.CapabilityLevelName : "",
                    StartDate = employeeTeachingRelation.StartDate,
                    EndDate = employeeTeachingRelation.EndDate,
                    Remark = employeeTeachingRelation.Remark
                };
                employeeTeachingRelationList.Add(returnView);
            }
            if (employeeTeachingRelationList.Count > 0)
            {
                employeeTeachingRelationList = employeeTeachingRelationList.OrderByDescending(m => m.StartDate).ToList();
            }
            return employeeTeachingRelationList;
        }

        /// <summary>
        /// 创建人员信息视图
        /// </summary>
        /// <param name="employeeList">员工集合</param>
        /// <param name="capabilityLevelList">层级字典数据</param>
        /// <param name="settingList">配置字典数据</param>
        /// <returns></returns>
        private List<EmployeeTeachingRelationView> CreateEmployeeView(List<EmployeeListView> employeeList, List<CapabilityLevelInfo> capabilityLevelList
            , List<AdministrationDictionaryInfo> settingList)
        {
            var employeeNOViews = new List<EmployeeTeachingRelationView>();
            foreach (var employee in employeeList)
            {
                var returnView = new EmployeeTeachingRelationView
                {
                    EmployeeID = employee.EmployeeID,
                    DepartmentName = employee.DepartmentName,
                    EmployeeName = employee.EmployeeName,
                    CapabilityLevel = employee.CapabilityLevelID.HasValue ? capabilityLevelList.FirstOrDefault(m => m.CapabilityLevelID == employee.CapabilityLevelID)?.CapabilityLevelName : "",
                    Gender = GetLocalShowName("GenderCode", employee.GenderCode, settingList),
                };
                employeeNOViews.Add(returnView);
            }
            return employeeNOViews;
        }

        /// <summary>
        /// 新增保存带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationView">保存信息</param>
        /// <param name="userID">护士工号</param>
        /// <param name="saveFlag">保存标记</param>
        /// <returns></returns>
        public async Task<bool> SaveTeachRelation(EmployeeTeachingRelationView employeeTeachingRelationView, string userID, bool saveFlag = true)
        {
            var employeeStaffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(employeeTeachingRelationView.EmployeeID);
            if (employeeStaffData == null)
            {
                _logger.Warn($"未找到工号为：{employeeTeachingRelationView.EmployeeID}的员工信息");
                return false;
            }
            var employeeTeachRelation = new EmployeeTeachingRelationInfo();
            if (employeeTeachingRelationView.EmployeeTeachingRelationID == 0)
            {
                employeeTeachRelation = new EmployeeTeachingRelationInfo
                {
                    EmployeeID = employeeTeachingRelationView.EmployeeID,
                    TeacherEmployeeID = employeeTeachingRelationView.TeacherEmployeeID,
                    StartDate = employeeTeachingRelationView.StartDate.HasValue ? employeeTeachingRelationView.StartDate.Value.Date : null,
                    EndDate = employeeTeachingRelationView.EndDate.HasValue ? employeeTeachingRelationView.EndDate.Value.Date : null,
                    Remark = employeeTeachingRelationView.Remark
                };
                employeeTeachRelation.Add(userID);
                employeeTeachRelation.Modify(userID);
                await _unitOfWork.GetRepository<EmployeeTeachingRelationInfo>().InsertAsync(employeeTeachRelation);
            }
            else
            {
                employeeTeachRelation = await _employeeTeachintRelationRepository.GetTeachRelationByID(employeeTeachingRelationView.EmployeeTeachingRelationID);
                if (employeeTeachRelation == null)
                {
                    _logger.Error($"未获取到主键ID为：{employeeTeachingRelationView.EmployeeTeachingRelationID}的带教关系记录");
                    return false;
                }
                employeeTeachRelation.EmployeeID = employeeTeachingRelationView.EmployeeID;
                employeeTeachRelation.TeacherEmployeeID = employeeTeachingRelationView.TeacherEmployeeID;
                employeeTeachRelation.StartDate = employeeTeachingRelationView.StartDate.HasValue ? employeeTeachingRelationView.StartDate.Value.Date : null;
                employeeTeachRelation.EndDate = employeeTeachingRelationView.EndDate.HasValue ? employeeTeachingRelationView.EndDate.Value.Date : null;
                employeeTeachRelation.Remark = employeeTeachingRelationView.Remark;
                employeeTeachRelation.Modify(userID);
            }
            await _unitOfWork.SaveChangesAsync();
            await UpdateEmployeeStaffTeacherEmployeeID(employeeTeachRelation, userID, true);
            try
            {
                if (saveFlag)
                {
                    return await _unitOfWork.SaveChangesAsync() > 0;
                }
                return true;
            }
            catch (Exception ex)
            {
                _logger.Warn($"保存失败，失败原因：{ex}");
                return false;
            }
        }

        /// <summary>
        /// 停止带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationID">带教关系主键ID</param>
        /// <param name="userID">护士工号</param>
        /// <returns></returns>
        public async Task<bool> StopTeachRelation(int employeeTeachingRelationID, string userID)
        {
            if (employeeTeachingRelationID == 0)
            {
                _logger.Error("前端传递带教关系主键ID有误");
                return false;
            }
            var employeeTeachingRelation = await _employeeTeachintRelationRepository.GetTeachRelationByID(employeeTeachingRelationID);
            if (employeeTeachingRelation == null)
            {
                _logger.Error($"未获取到主键ID为：{employeeTeachingRelationID}的带教关系记录");
                return false;
            }
            await UpdateEmployeeStaffTeacherEmployeeID(employeeTeachingRelation, userID);
            employeeTeachingRelation.EndDate = DateTime.Now.Date;
            employeeTeachingRelation.Modify(userID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 删除带教关系
        /// </summary>
        /// <param name="employeeTeachingRelationID">带教关系主键ID</param>
        /// <param name="userID">护士工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteTeachRelation(int employeeTeachingRelationID, string userID)
        {
            if (employeeTeachingRelationID == 0)
            {
                _logger.Error("前端传递带教关系主键ID有误");
                return false;
            }
            var employeeTeachingRelation = await _employeeTeachintRelationRepository.GetTeachRelationByID(employeeTeachingRelationID);
            if (employeeTeachingRelation == null)
            {
                _logger.Error($"未获取到主键ID为：{employeeTeachingRelationID}的带教关系记录");
                return false;
            }
            await UpdateEmployeeStaffTeacherEmployeeID(employeeTeachingRelation, userID);
            employeeTeachingRelation.Delete(userID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 批量保存数据
        /// </summary>
        /// <param name="employeeTeachingRelationView">保存信息</param>
        /// <param name="userID">护士工号</param>
        /// <returns></returns>
        public async Task<bool> BachSaveTeachRelation(List<EmployeeTeachingRelationView> employeeTeachingRelationView, string userID)
        {
            if (employeeTeachingRelationView == null || employeeTeachingRelationView.Count <= 0)
            {
                _logger.Error("前端传递的保存数据为空");
                return false;
            }
            foreach (var employeeTeachRelation in employeeTeachingRelationView)
            {
                await SaveTeachRelation(employeeTeachRelation, userID, false);
            }
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 更新Staff中带教老师字段
        /// </summary>
        /// <param name="relation">带教关系</param>
        /// <param name="userID">员工编号</param>
        /// <param name="isInclude">是否包含</param>
        /// <returns></returns>
        private async Task UpdateEmployeeStaffTeacherEmployeeID(EmployeeTeachingRelationInfo relation, string userID, bool isInclude = false)
        {
            var nowDate = DateTime.Today;
            var employeeStaffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(relation.EmployeeID);
            var employeeTeachingRelationList = await _employeeTeachintRelationRepository.GetTeachRelationByEmployeeID(relation.EmployeeID);
            var employeeRelation = employeeTeachingRelationList.Where(m => (!isInclude ? m.EmployeeTeachingRelationID != relation.EmployeeTeachingRelationID : true)
            && m.StartDate <= nowDate && ((m.EndDate.HasValue && m.EndDate >= nowDate) || !m.EndDate.HasValue)).OrderByDescending(m => m.StartDate).FirstOrDefault();
            // 新增带教关系时，将带教老师ID保存到EmployeeStaffData中
            employeeStaffData.TeacherEmployeeID = employeeRelation == null ? "" : employeeRelation.TeacherEmployeeID;
            employeeStaffData.Modify(userID);
            _unitOfWork.GetRepository<EmployeeStaffDataInfo>().Update(employeeStaffData);
            await _employeeStaffDataRepository.UpdateCache();
        }

        #endregion

        #region 人员部门变更申请

        /// <summary>
        /// 保存人员部门变更申请
        /// </summary>
        /// <param name="employeeListView">保存人员信息</param>
        /// <param name="departmentID">部门ID</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<bool> SaveEmployeeDepartmentChangeRequest(List<EmployeeListView> employeeListView, int departmentID, Session session)
        {
            if (employeeListView.Count <= 0)
            {
                return false;
            }
            var approveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            bool approvalFlag = false;
            var employeeDepartmentChangeRequestView = new List<EmployeeDepartmentChangeRequestInfo>();
            var deptVSDeptList = await _departmentVSDepartmentRepository.GetByCacheAsync();
            var departmentList = await _departmentListRepository.GetByCacheAsync();
            if (departmentList.Count <= 0 || deptVSDeptList.Count <= 0)
            {
                _logger.Error($"获取字典数据失败");
                return false;
            }
            var deptVSDept = deptVSDeptList.FirstOrDefault(m => m.DepartmentID2 == departmentID);
            if (deptVSDept == null)
            {
                _logger.Error($"未获取到部门ID：{departmentID}对照部门字典数据");
                return false;
            }
            var department = departmentList.FirstOrDefault(m => m.DepartmentID == deptVSDept.DepartmentID1);
            if (department == null)
            {
                _logger.Error($"未获取到部门ID：{deptVSDept.DepartmentID1}部门字典数据");
                return false;
            }
            if (department.OrganizationType == "6" && department.SourceType == "1")
            {
                approvalFlag = true;
            }
            var employeeIDs = employeeListView.Select(m => m.EmployeeID).ToList();
            var employeeDatas = await _employeePersonalDataRepository.GetDataByEmployeeIDs(employeeIDs);

            foreach (var employee in employeeListView)
            {
                var employeeDepartmentChangeRequestInfo = new EmployeeDepartmentChangeRequestInfo
                {
                    HospitalID = session.HospitalID,
                    EmployeeID = employee.EmployeeID,
                    DepartmentID = departmentID,
                    OriginalDepartmentID = employee.DepartmentID.Value,
                    StatusCode = "2",
                    DeleteFlag = ""
                };
                employeeDepartmentChangeRequestInfo.ID = employeeDepartmentChangeRequestInfo.GetId();
                //需要审批
                if (approvalFlag)
                {
                    employeeDepartmentChangeRequestInfo.StatusCode = "0";
                    employeeDepartmentChangeRequestInfo.ApproveRecordID = employeeDepartmentChangeRequestInfo.GetId();
                    //添加审批流程
                    var view = CreateApproveMainAndDetailParamViewAsync(employeeDepartmentChangeRequestInfo, employeeDatas, departmentList);
                    var (approveProcessID, contentTemplate) = await _approveProcessService.GetProcessIDAndContentTemplateByTypeAndDepartmentID(view.ProveCategory, view.DepartmentID);
                    view.Content = await approveRecordService.GetApproveContentByBizView(contentTemplate, view);
                    await approveRecordService.AddApproveRecordAndDetailAsync(view, approveProcessID);
                }
                else
                {
                    //更新Staff中的员工部门
                    var employeeStaffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(employee.EmployeeID);
                    if (employeeStaffData == null)
                    {
                        continue;
                    }
                    employeeStaffData.DepartmentID = departmentID;
                    employeeStaffData.Modify(session.EmployeeID);
                    _unitOfWork.GetRepository<EmployeeStaffDataInfo>().Update(employeeStaffData);
                }
                employeeDepartmentChangeRequestInfo.Add(session.EmployeeID).Modify(session.EmployeeID);
                employeeDepartmentChangeRequestView.Add(employeeDepartmentChangeRequestInfo);
            }
            await _unitOfWork.GetRepository<EmployeeDepartmentChangeRequestInfo>().InsertAsync(employeeDepartmentChangeRequestView);
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                //不需要审批
                if (!approvalFlag)
                {
                    await _employeeStaffDataRepository.UpdateCache();
                    //调用同步程序接口，向中间库表存放消息
                    await _requestApiService.RequestAPI("EmployeeDepartmentChangeRequest", ListToJson.ToJson(employeeDepartmentChangeRequestView));
                }
                return true;
            }
            return false;
        }

        /// <summary>
        /// 获取部门可以变更的人员
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <param name="hospitalID">医院编码</param>
        /// <returns></returns>
        public async Task<List<EmployeeListView>> GetDepartmentChangeRequestEmployeeByDepartmentID(int departmentID, string hospitalID)
        {
            var employeeList = await _employeeStaffDataRepository.GetEmployeeViewByDepartmentIDAsync(departmentID);
            if (employeeList.Count() <= 0)
            {
                _logger.Warn($"未找到部门相关人员信息，部门ID{departmentID}");
                return employeeList;
            }
            return employeeList;
        }

        /// <summary>
        /// 获取人员部门变更申请记录
        /// </summary>
        /// <param name="requestStartDate">申请开始时间</param>
        /// <param name="requestEndDate">申请结束时间</param>
        /// <param name="session">缓存</param>
        /// <returns></returns>
        public async Task<List<EmployeeDepartmentChangeView>> GetEmployeeDepartmentChangeRequestView(DateTime requestStartDate, DateTime requestEndDate, Session session)
        {
            var views = await _employeeDepartmentChangeRequestRepository.GetEmployeeDepartmentChangeRequestViews(session.HospitalID, requestStartDate, requestEndDate);
            if (views.Count <= 0)
            {
                return views;
            }
            var personDatas = await _employeePersonalDataRepository.GetAll<EmployeePersonalDataListView>();
            //获取申请状态字典
            var settingDictionaryarams = new SettingDictionaryParams()
            {
                SettingType = "ApprovalManagement",
                SettingTypeCode = "ApproveRecord",
                SettingTypeValue = "StatusCode"
            };
            var statusDict = await _settingDictionaryService.GetSettingDictionaryDict(settingDictionaryarams);
            var depts = await _departmentListRepository.GetAll<DepartmentListInfo>();
            foreach (var view in views)
            {
                view.EmployeeName = personDatas.Find(m => m.EmployeeID == view.EmployeeID)?.EmployeeName ?? view.EmployeeID;
                view.AddEmployeeName = personDatas.Find(m => m.EmployeeID == view.AddEmployeeID)?.EmployeeName ?? view.EmployeeID;
                view.DepartmentName = depts.Find(m => m.DepartmentID == view.DepartmentID)?.DepartmentContent;
                view.OriginalDepartmentName = depts.Find(m => m.DepartmentID == view.OriginalDepartmentID)?.DepartmentContent;
                view.Status = statusDict.Find(m => m.Value.Equals(view.StatusCode))?.Label?.ToString();
            }
            return views.OrderByDescending(m => m.AddDateTime).ToList();
        }

        /// <summary>
        /// 根据主键ID删除人员部门变更申请记录（未审核的）
        /// </summary>
        /// <param name="dataID">主键ID</param>
        /// <param name="employeeID">人员工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteEmployeeDepartmentChangeRequest(string dataID, string employeeID)
        {
            var requestInfo = await _employeeDepartmentChangeRequestRepository.GetEmployeeDepartmentChangeRequestByID(dataID);
            if (requestInfo == null)
            {
                _logger.Warn($"未找到对应申请记录");
                return false;
            }
            var apparoveRecordService = _serviceProvider.GetService<IApproveRecordService>();
            requestInfo.Delete(employeeID);
            if (await _unitOfWork.SaveChangesAsync() > 0)
            {
                await apparoveRecordService.StopApprovalAsync(requestInfo.ID, employeeID);
                return true;
            }
            return false;
        }

        /// <summary>
        /// 创建请求审批所需参数
        /// </summary>
        /// <param name="employeeDepartmentChangeRequestInfo"></param>
        /// <param name="employeeDatas"></param>
        /// <param name="departmentList"></param>
        /// <returns></returns>
        private ApproveMainAndDetailParamView CreateApproveMainAndDetailParamViewAsync(EmployeeDepartmentChangeRequestInfo employeeDepartmentChangeRequestInfo
            , Dictionary<string, string> employeeDatas, List<DepartmentListInfo> departmentList)
        {
            //方法暂时保留，审批流程功能正常正常后打开测试
            return new ApproveMainAndDetailParamView
            {
                SourceID = employeeDepartmentChangeRequestInfo.ID,
                ApproveRecordID = employeeDepartmentChangeRequestInfo.ApproveRecordID,
                ProveCategory = "HR-040",
                DepartmentID = employeeDepartmentChangeRequestInfo.DepartmentID,
                AddEmployeeID = employeeDepartmentChangeRequestInfo.AddEmployeeID,
                AdjustEmployeeName = employeeDatas.FirstOrDefault(m => m.Key == employeeDepartmentChangeRequestInfo.EmployeeID).Value,
                DepartmentName = departmentList.Find(m => m.DepartmentID == employeeDepartmentChangeRequestInfo.DepartmentID)?.DepartmentContent,
                OriginalDepartmentName = departmentList.Find(m => m.DepartmentID == employeeDepartmentChangeRequestInfo.OriginalDepartmentID)?.DepartmentContent,
            };
        }

        #endregion

        /// <summary>
        /// 保存员工服饰尺码信息
        /// </summary>
        /// <param name="employeeClothingSize">员工服饰尺码信息</param>
        /// <param name="hospitalID">医院编号</param>
        /// <returns></returns>
        public async Task<bool> SaveEmployeeClothingSizes(EmployeeClothingSizesView employeeClothingSize, string hospitalID)
        {
            if (employeeClothingSize == null)
            {
                _logger.Error("保存服饰信息失败，前端传递服饰信息为空！");
                return false;
            }
            var employeeStaffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(employeeClothingSize.EmployeeID);
            if (employeeStaffData == null)
            {
                _logger.Error("保存服饰信息失败，不存在该员工或员工已离职");
                return false;
            }
            var clothingSizesData = await _employeeClothingSizesRepository.GetDataByEmployeeID(employeeClothingSize.EmployeeID);
            if (clothingSizesData == null)
            {
                //新增
                var clothingSizes = new EmployeeClothingSizesInfo()
                {
                    EmployeeID = employeeClothingSize.EmployeeID,
                    HospitalID = hospitalID,
                    Height = employeeClothingSize.Height,
                    Weight = employeeClothingSize.Weight,
                    ClothesSize = employeeClothingSize.ClothesSize?.Trim() ?? "",
                    PantsSize = employeeClothingSize.PantsSize?.Trim() ?? "",
                    ShoeSize = employeeClothingSize.ShoeSize
                };
                clothingSizes.EmployeeClothingSizesID = clothingSizes.GetId();
                clothingSizes.Add(employeeClothingSize.EmployeeID);
                clothingSizes.Modify(employeeClothingSize.EmployeeID);
                await _unitOfWork.GetRepository<EmployeeClothingSizesInfo>().InsertAsync(clothingSizes);
            }
            else
            {
                //修改
                clothingSizesData.Height = employeeClothingSize.Height;
                clothingSizesData.Weight = employeeClothingSize.Weight;
                clothingSizesData.ClothesSize = employeeClothingSize.ClothesSize?.Trim() ?? "";
                clothingSizesData.PantsSize = employeeClothingSize.PantsSize?.Trim() ?? "";
                clothingSizesData.ShoeSize = employeeClothingSize.ShoeSize;
                clothingSizesData.Modify(employeeClothingSize.EmployeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        #region 人员多组织架构部门

        /// <summary>
        /// 获取列表
        /// </summary>
        /// <returns></returns>
        public async Task<EmployeeToDepartmentView[]> GetEmpToDeptViews()
        {
            var employeeToDepartments = await _employeeToDepartmentRepository.GetEmployeeToDepartments();
            var employees = await _employeePersonalDataRepository.GetIDAndNameData();
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();
            // 按人员分组呈现
            var employeeToDepartmentViews = employeeToDepartments.Select(employeeToDepartment =>
            {
                var view = new EmployeeToDepartmentView
                {
                    EmployeeID = employeeToDepartment.Key,
                    EmployeeName = employees.FirstOrDefault(m => m.EmployeeID == employeeToDepartment.Key)?.EmployeeName,
                    Departments = employeeToDepartment.Value.Select(GetCreateEmployeeDepartViewFunc(departmentList)).OrderBy(m => m.IsMainDepartment ? 0 : 1).ToArray()
                };
                return view;
            }).ToArray();
            return employeeToDepartmentViews;
        }

        /// <summary>
        /// 获取创建View的函数
        /// </summary>
        /// <param name="departmentList">部门列表</param>
        /// <returns></returns>
        private static Func<EmployeeToDepartmentInfo, EmployeeDepartmentView> GetCreateEmployeeDepartViewFunc(List<DepartmentListInfo> departmentList)
        {
            return m => CreateEmployeeDepartmentView(m, departmentList);
        }

        /// <summary>
        /// 根据工号获取人员多组织架构部门
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<EmployeeDepartmentView[]> GetEmpDepartmentsByEmployeeID(string employeeID)
        {
            var employeeDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            var departmentList = await _departmentListRepository.GetAll<DepartmentListInfo>();

            var views = employeeDepartments.Select(GetCreateEmployeeDepartViewFunc(departmentList)).OrderBy(m => m.IsMainDepartment ? 0 : 1).ToArray();

            return views;
        }

        /// <summary>
        /// 保存人员多组织架构部门
        /// </summary>
        /// <param name="employeeToDepartmentView"></param>
        /// <param name="EmployeeID"></param>
        /// <returns></returns>
        public async Task<bool> SaveEmployeeToDepartments(EmployeeToDepartmentView employeeToDepartmentView, string employeeID)
        {
            if (employeeToDepartmentView == null)
            {
                return false;
            }
            var employeeToDepartments = await _employeeToDepartmentRepository.GetNoCacheInfosByEmployeeID(employeeToDepartmentView.EmployeeID);
            var modifyEmployeeID = _sessionCommonServer.GetSessionByCache().EmployeeID;
            EmployeeDepartmentView[] newInfoViews = employeeToDepartmentView.Departments;
            EmployeeDepartmentView[] deleteInfoViews = [];
            // 全部新增
            if (employeeToDepartments.Length == 0)
            {
                await InsertNewEmployeeToDepartmentInfos(employeeToDepartmentView.Departments, employeeToDepartmentView.EmployeeID, employeeToDepartmentView.HospitalID, employeeID);
            }
            else
            {
                var departmentListSelector = (EmployeeDepartmentView m) => (m.DepartmentID, m.OrganizationType);
                var employeeToDepartmentSelector = (EmployeeToDepartmentInfo m) => (m.DepartmentID, m.OrganizationType);
                // 新增
                newInfoViews = employeeToDepartmentView.Departments
                   .ExceptBy(employeeToDepartments.Select(employeeToDepartmentSelector), departmentListSelector).ToArray();
                await InsertNewEmployeeToDepartmentInfos(newInfoViews, employeeToDepartmentView.EmployeeID, employeeToDepartmentView.HospitalID, employeeID);
                // 删除
                var deleteInfo = employeeToDepartments.ExceptBy(
                    employeeToDepartmentView.Departments.Select(departmentListSelector), employeeToDepartmentSelector).ToList();
                deleteInfoViews = deleteInfo.Select(m => new EmployeeDepartmentView { DepartmentID = m.DepartmentID, OrganizationType = m.OrganizationType }).ToArray();
                deleteInfo.ForEach(m => m.Delete(employeeID));
            }

            var isSuccess = await _unitOfWork.SaveChangesAsync() > 0;
            if (isSuccess)
            {
                await _employeeToDepartmentRepository.UpdateCache();
                //成功后保存人员病区权限
                await SaveEmployeeDepartmentSwitch(employeeToDepartmentView, newInfoViews, deleteInfoViews, employeeID);
            }
            return isSuccess;
        }

        /// <summary>
        /// 保存人员病区权限
        /// </summary>
        /// <param name="employeeToDepartmentView"></param>
        /// <param name="newInfoViews"></param>
        /// <param name="deleteInfoViews"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        public async Task<bool> SaveEmployeeDepartmentSwitch(EmployeeToDepartmentView employeeToDepartmentView, EmployeeDepartmentView[] newInfoViews, EmployeeDepartmentView[] deleteInfoViews, string employeeID)
        {
            var switchInfos = await _employeeDepartmentSwitchRepository.GetDepartmentSwitchByEmployeeIDAsync(employeeToDepartmentView.EmployeeID, false);
            var saveFlag = false;
            if (newInfoViews.Length > 0)
            {
                newInfoViews.ToList().ForEach(async m =>
                {
                    var info = switchInfos.FirstOrDefault(n => n.DepartmentID == m.DepartmentID);
                    if (info == null)
                    {
                        saveFlag = true;
                        var newEmployeeDepartmentSwitchInfo = new EmployeeDepartmentSwitchInfo
                        {
                            EmployeeID = employeeToDepartmentView.EmployeeID,
                            DepartmentID = m.DepartmentID,
                            HospitalID = employeeToDepartmentView.HospitalID,
                            OrganizationType = m.OrganizationType
                        };
                        newEmployeeDepartmentSwitchInfo.Modify(employeeID);
                        await _unitOfWork.GetRepository<EmployeeDepartmentSwitchInfo>().InsertAsync(newEmployeeDepartmentSwitchInfo);
                    }
                });
            }
            if (deleteInfoViews.Length > 0)
            {
                switchInfos.ForEach(m =>
                {
                    var info = deleteInfoViews.FirstOrDefault(n => n.DepartmentID == m.DepartmentID);
                    if (info != null)
                    {
                        saveFlag = true;
                        m.Delete(employeeID);
                    }
                });
            }
            var ret = false;
            if (saveFlag)
            {
                ret = await _unitOfWork.SaveChangesAsync() >= 0;
                if (ret)
                {
                    await _employeeDepartmentSwitchRepository.UpdateCache();
                }
            }
            return ret;
        }

        /// <summary>
        /// 创建员工多组织架构部门视图
        /// </summary>
        /// <param name="employeeToDepartmentInfo">人员部门</param>
        /// <param name="departmentList">部门字典ID</param>
        /// <returns></returns>
        private static EmployeeDepartmentView CreateEmployeeDepartmentView(EmployeeToDepartmentInfo employeeToDepartmentInfo, List<DepartmentListInfo> departmentList)
        {
            return new EmployeeDepartmentView
            {
                DepartmentID = employeeToDepartmentInfo.DepartmentID,
                LocalShowName = departmentList.FirstOrDefault(m => m.DepartmentID == employeeToDepartmentInfo.DepartmentID)?.LocalShowName,
                OrganizationType = employeeToDepartmentInfo.OrganizationType,
                IsMainDepartment = employeeToDepartmentInfo.IsMainDepartment ?? false
            };
        }

        /// <summary>
        /// 插入新的员工多组织架构部门数据
        /// </summary>
        /// <param name="departments">部门集合</param>
        /// <param name="employeeID">工号</param>
        /// <param name="hospitalID">医院序号</param>
        /// <returns></returns>
        private async Task InsertNewEmployeeToDepartmentInfos(EmployeeDepartmentView[] departments, string employeeID, string hospitalID, string modifyEmployeeID)
        {
            var newInfos = departments.Select(department =>
            {
                var employeeToDepartment = new EmployeeToDepartmentInfo
                {
                    EmployeeID = employeeID,
                    DepartmentID = department.DepartmentID,
                    OrganizationType = department.OrganizationType,
                    HospitalID = hospitalID,
                    DeleteFlag = "",
                    IsMainDepartment = false
                };
                employeeToDepartment.Add(modifyEmployeeID);
                employeeToDepartment.Modify(modifyEmployeeID);
                return employeeToDepartment;
            }).ToArray();
            await _unitOfWork.GetRepository<EmployeeToDepartmentInfo>().InsertAsync(newInfos);
            // 新增多部门权限
            //var employeeDepartmentSwitchParam = new EmployeeDepartmentSwitchParamView()
            //{
            //    EmployeeID = employeeID,
            //    DepartmentIDs = departments.Select(m => m.DepartmentID).ToList(),
            //    HospitalID = hospitalID,
            //};
            //await _employeeDepartmentSwitchService.SaveEmployeeDepartmentSwitchAsync(employeeDepartmentSwitchParam, modifyEmployeeID);
        }

        /// <summary>
        /// 删除人员多组织架构部门
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> DeleteEmployeeToDepartmentByEmployeeID(string employeeID)
        {
            var employeeToDepartments = await _employeeToDepartmentRepository.GetNoCacheInfosByEmployeeID(employeeID);
            if (employeeToDepartments.Length == 0)
            {
                return true;
            }

            foreach (var employeeToDepartment in employeeToDepartments)
            {
                employeeToDepartment.Delete(employeeID);
            }
            var isSuccess = await _unitOfWork.SaveChangesAsync() > 0;
            if (isSuccess)
            {
                await _employeeToDepartmentRepository.UpdateCache();
            }
            return isSuccess;
        }

        #endregion

        public async Task<List<EmployeeStaffDataInfo>> GetDimissionEmployeeList(int? departmentID, string[] employeeIDs = null)
        {
            return await _employeeStaffDataRepository.GetEmployeeListByDeptIDOrEmployeeIDs("0", departmentID, employeeIDs);
        }

        /// <summary>
        /// 获取服饰尺码信息
        /// </summary>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task<CollapseView> GetEmployeeClothingSizeAsync(string employeeID)
        {
            var collapseView = new CollapseView
            {
                Title = "服饰档案",
                Name = "clothingArchives",
                Class = "Collapse",
                Data = new EmployeeClothingSizesView(),
                EditFlag = true
            };
            var clothingSizes = await _employeeClothingSizesRepository.GetDataByEmployeeID(employeeID);
            if (clothingSizes == null)
            {
                return collapseView;
            }
            collapseView.Data = clothingSizes;
            return collapseView;
        }

        #region 员工基本信息-个人特长相关逻辑

        /// <summary>
        /// 获取个人特长
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task<CollapseView> GetEmployeeStrengthAsync(string employeeID)
        {
            var collapseView = new CollapseView
            {
                Title = "个人特长",
                Name = "strongPointHeader",
                Class = "Table",
                Data = new List<EmployeeStrengthView>(),
                EditFlag = true
            };
            var employeeStrengthViews = new List<EmployeeStrengthView>();
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeStrengthSetting",
            };
            var settings = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            var employeeStrengths = await _employeeStrengthRepository.GetListByEmployeeID(employeeID);
            if (settings.Count == 0 || employeeStrengths.Count == 0)
            {
                return collapseView;
            }
            foreach (var strength in employeeStrengths.OrderBy(m => m.AddDateTime))
            {
                var setting = settings.Find(m => m.SettingValue == strength.StrengthID);
                if (setting == null)
                {
                    continue;
                }
                var view = new EmployeeStrengthView
                {
                    EmployeeStrengthID = strength.EmployeeStrengthID,
                    EmployeeID = strength.EmployeeID,
                    StrengthID = strength.StrengthID,
                    Honor = strength.Honors,
                    StrengthName = setting.Description
                };
                employeeStrengthViews.Add(view);
            }
            collapseView.Data = employeeStrengthViews;
            return collapseView;
        }

        /// <summary>
        /// 保存个人特长数据
        /// </summary>
        /// <param name="employeeStrengths">前端传进来的需要保存的数据</param>
        /// <returns></returns>
        public async Task<bool> SaveEmployeeStrengthsAsync(List<EmployeeStrengthView> employeeStrengths)
        {
            var settingParams = new SettingDictionaryParams
            {
                SettingType = "EmployeeManagement",
                SettingTypeCode = "EmployeeStrengthSetting",
            };
            var settings = await _settingDictionaryRepository.GetSettingDictionary(settingParams);
            if (employeeStrengths.Count == 0 || settings.Count == 0)
            {
                return false;
            }
            string employeeID = employeeStrengths.FirstOrDefault().EmployeeID;
            var newSettings = employeeStrengths.Where(m => string.IsNullOrEmpty(m.StrengthID)).ToList();
            if (newSettings.Count > 0)
            {
                settings = await SetStrengthSettings(newSettings, settings, employeeID);
            }
            foreach (var strength in employeeStrengths)
            {
                await SetStrengthData(strength, settings, employeeID);
            }
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 创建新的个人特长字典数据
        /// </summary>
        /// <param name="newSettings">新增的SettingDictionary数据</param>
        /// <param name="settings">原有SettingDictionary数据</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task<List<SettingDictionaryInfo>> SetStrengthSettings(List<EmployeeStrengthView> newSettings, List<SettingDictionaryInfo> settings, string employeeID)
        {
            var result = new List<SettingDictionaryInfo>();
            int addValue = settings.Count == 0 ? 1 : (settings.Max(m => Convert.ToInt32(m.SettingValue)) + 1);
            foreach (var newSetting in newSettings)
            {
                var setting = new SettingDictionaryInfo
                {
                    HospitalID = _options.Value.HospitalID,
                    Language = _options.Value.Language,
                    DataType = "1",
                    SettingType = "EmployeeManagement",
                    SettingTypeCode = "EmployeeStrengthSetting",
                    SettingTypeValue = "EmployeeStrength",
                    SettingValue = addValue.ToString(),
                    Description = newSetting.StrengthName,
                    ModifyFlag = true,
                    AddEmployeeID = employeeID,
                    AddDateTime = DateTime.Now,
                    ModifyEmployeeID = employeeID,
                    ModifyDateTime = DateTime.Now,
                    DeleteFlag = "",
                    Sort = addValue
                };
                result.Add(setting);
                newSetting.StrengthID = setting.SettingValue;
                addValue++;
            }
            await _unitOfWork.GetRepository<SettingDictionaryInfo>().InsertAsync(result);
            await _settingDictionaryRepository.UpdateCache();
            settings.AddRange(result);
            return settings;
        }

        /// <summary>
        /// 新增或者修改个人特长数据
        /// </summary>
        /// <param name="strength">特长数据</param>
        /// <param name="settings">配置</param>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        private async Task SetStrengthData(EmployeeStrengthView strength, List<SettingDictionaryInfo> settings, string employeeID)
        {
            var setting = settings.Find(m => m.SettingValue == strength.StrengthID);
            if (setting == null)
            {
                return;
            }
            var employeeStrengthList = await _employeeStrengthRepository.GetListByEmployeeID(employeeID);
            if (!string.IsNullOrEmpty(strength.EmployeeStrengthID))
            {
                UpdateStrengthData(employeeStrengthList, strength, setting, employeeID);
                return;
            }
            var employeeStrength = new EmployeeStrengthInfo
            {
                EmployeeID = employeeID,
                StrengthID = setting.SettingValue,
                Honors = string.IsNullOrEmpty(strength.Honor) ? string.Empty : strength.Honor,
                DeleteFlag = ""
            };
            employeeStrength.EmployeeStrengthID = employeeStrength.GetId();
            employeeStrength.Add(employeeID);
            employeeStrength.Modify(employeeID);
            await _unitOfWork.GetRepository<EmployeeStrengthInfo>().InsertAsync(employeeStrength);
        }

        /// <summary>
        /// 更新个人特长数据
        /// </summary>
        /// <param name="employeeStrengthList">个人特长数据列表</param>
        /// <param name="strength">特长数据</param>
        /// <param name="setting">配置</param>
        /// <param name="employeeID">工号</param>
        private static void UpdateStrengthData(List<EmployeeStrengthInfo> employeeStrengthList, EmployeeStrengthView strength, SettingDictionaryInfo setting, string employeeID)
        {
            var existingStrength = employeeStrengthList.FirstOrDefault(m => m.EmployeeStrengthID == strength.EmployeeStrengthID);
            if (existingStrength == null)
            {
                return;
            }
            var updates = new Dictionary<Func<bool>, Action>
                    {
                        { () => existingStrength.StrengthID != setting.SettingValue, () => existingStrength.StrengthID = setting.SettingValue },
                        { () => existingStrength.Honors != strength.Honor, () => existingStrength.Honors = strength.Honor },
                        { () => existingStrength.EmployeeID != strength.EmployeeID, () => existingStrength.EmployeeID = strength.EmployeeID }
                    };
            bool isModified = false;
            foreach (var update in updates)
            {
                if (update.Key())
                {
                    update.Value();
                    isModified = true;
                }
            }
            if (isModified)
            {
                existingStrength.Modify(employeeID);
            }
        }

        #endregion

        /// <summary>
        /// 根据员工ID和组织类型获取员工所属部门的信息。
        /// </summary>
        /// <param name="employeeID">员工的ID。</param>
        /// <param name="organizationType">组织类型。</param>
        /// <returns>包含员工组织部门信息</returns>
        public async Task<List<EmployeeDepartmentView>> GetEmployeeToDeptByEmployeeIDAndOrganizationTypeAsync(string employeeID, string organizationType)
        {
            var cacheEmployeeToDeptInfos = await _employeeToDepartmentRepository.GetByOrganizationTypeAndEmployeeIDAsync(employeeID, organizationType);

            var departmentList = await _departmentListRepository.GetByOrganizationType(organizationType);

            var views = cacheEmployeeToDeptInfos.Select(GetCreateEmployeeDepartViewFunc(departmentList)).OrderBy(m => m.IsMainDepartment ? 0 : 1).ToList();

            return views;
        }

        /// <summary>
        /// 获取员工体检信息列表
        /// </summary>
        /// <param name="employeeID">员工明细</param>
        /// <returns></returns>
        public async Task<(bool, object)> GetPhyExamList(string employeeID)
        {
            var employeeStaffData = await _employeeStaffDataRepository.GetEmployeeStaffDataByID(employeeID);
            if (employeeStaffData == null)
            {
                _logger.Error($"获取体检报告失败，未找到员工信息，EmployeeID：{employeeID}");
                return (false, null);
            }
            if (string.IsNullOrEmpty(employeeStaffData.ChartNo))
            {
                _logger.Warn($"获取体检报告失败，未找到员工病案号，EmployeeID：{employeeID}");
                return (false, $"体检号未关联OA");
            }
            try
            {
                var requestParam = $"?phyExamID={employeeStaffData.ChartNo}";
                var result = await _requestApiService.RequestAPI("GetPhyExamList", requestParam);
                var stringResult = result?.ToString();
                if (string.IsNullOrEmpty(stringResult))
                {
                    _logger.Error("获取体检报告数据失败 参数||" + employeeStaffData.ChartNo);
                    return (false, null);
                }
                var response = ListToJson.ToList<ResponseResult>(stringResult);
                return (true,response.Data);
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString() + "获取体检报告数据失败 参数||" + employeeStaffData.ChartNo);
                return (false, null);
            }
        }

        /// <summary>
        /// 根据检验号和体检次数获取检验明细数据
        /// </summary>
        /// <param name="phyExamID">体检号</param>
        /// <param name="visitID">体检次数</param>
        /// <returns></returns>
        public async Task<object> GetPhyExamRecord(string phyExamID, int visitID)
        {
            if (string.IsNullOrEmpty(phyExamID))
            {
                _logger.Warn($"获取体检报告明细失败，未传递员工病案号");
                return null;
            }
            try
            {
                var requestParam = $"?phyExamID={phyExamID}&visitID={visitID}";
                var result = await _requestApiService.RequestAPI("GetPhyExamRecord", requestParam);
                string stringResult = result == null ? "" : result.ToString();
                if (string.IsNullOrEmpty(stringResult))
                {
                    _logger.Error($"获取体检报告明细失败 参数||phyExamID：{phyExamID}||visitID：{visitID}");
                    return null;
                }
                var response = ListToJson.ToList<ResponseResult>(stringResult);
                return response.Data;
            }
            catch (Exception ex)
            {
                _logger.Error(ex.ToString() + $"获取体检报告明细失败 参数||phyExamID：{phyExamID}||visitID：{visitID}");
                return null;
            }
        }

        /// 删除个人特长数据 </summary> <param name="employeeStrengthID"></param> <returns></returns>
        public async Task<bool> DeleteEmployeeStrengthsAsync(string employeeStrengthID, string userID)
        {
            var employeeStrength = await _employeeStrengthRepository.GetDataByStrengthID(employeeStrengthID);
            if (employeeStrength == null)
            {
                return false;
            }
            employeeStrength.Delete(userID);
            return await _unitOfWork.SaveChangesAsync() > 0;
        }

        /// <summary>
        /// 提前结束借调，并且已经审批完成，删除借调之后的数据
        /// </summary>
        /// <param name="record"></param>
        /// <param name="employeeID"></param>
        /// <returns></returns>
        private async Task DeelEmployeeSecondmentScheduleData(EmployeeSecondmentRecordInfo record, string employeeID)
        {
            var approveRecord = await _approveRecordRepository.GetApproveRecordByRecordIDAsync(record.ApproveRecordID);
            if (approveRecord != null && approveRecord.StatusCode != "2")
            {
                return;
            }
            // 获取借调部门在借调结束后的排班数据，删除
            var secondmentDepartmentShiftScheduleList = await _shiftSchedulingDetailRepository.GetDetailByEmployeeID(record.EmployeeID, record.SecondmentDepartmentID, null, record.ActualEndNoon, null, record.ActualEndDate);
            if (secondmentDepartmentShiftScheduleList.Count > 0)
            {
                foreach (var item in secondmentDepartmentShiftScheduleList)
                {
                    item.Delete(employeeID);
                }
            }
            ;
            // 获取借调部门在借调结束后的排班明细标识数据，删除
            var secondmentDepartmentShiftScheduleRecordIDLIst = secondmentDepartmentShiftScheduleList.Select(m => m.ShiftSchedulingRecordID).ToList();
            var secondmentDepartmentShiftScheduleDetailMarkList = await _shiftSchedulingDetailMarkRepository.GetMarkByEmployeeID(secondmentDepartmentShiftScheduleRecordIDLIst, record.EmployeeID, record.StartDate, record.EndDate);
            if (secondmentDepartmentShiftScheduleDetailMarkList.Count > 0)
            {
                foreach (var item in secondmentDepartmentShiftScheduleDetailMarkList)
                {
                    item.Delete(employeeID);
                }
            }
            ;
        }

        /// <summary>
        /// 删除个人任职记录数据
        /// </summary>
        /// <param name="employeeStrengthID"></param>
        /// <param name="userID"></param>
        /// <returns></returns>
        public async Task<bool> DeleteEmployeePositionAsync(string employeeStrengthID, string userID)
        {
            var employeePosition = await _employeeEmploymentRecordRepository.GetDataByID(employeeStrengthID);
            if (employeePosition == null)
            {
                return false;
            }
            employeePosition.Delete(userID);
            return await _unitOfWork.SaveChangesAsync() >= 0;
        }

        /// <summary>
        /// 获取片区权限
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<bool> GetEmployeeToDepartmentAsync(string employeeID)
        {
            var employeeToDepartments = await _employeeToDepartmentRepository.GetDepartmentsByEmployeeID(employeeID);
            if (employeeToDepartments.Length <= 0)
            {
                return false;
            }
            return employeeToDepartments.Any(m => DEPARTMENT_AUTHORITY.Contains(m.DepartmentID));
        }

        /// <summary>
        /// 获取用户权限科室
        /// </summary>
        /// <param name="employeeID">工号</param>
        /// <returns></returns>
        public async Task<Dictionary<int, string>> GetEmployeeDepartmentIDAndNames(string employeeID)
        {
            var departmentIDs = await _employeeToDepartmentRepository.GetEmployeeToDepartmentIDs(employeeID);
            var departments = await _departmentListRepository.GetAll<DepartmentListInfo>();
            return departments.Where(m => departmentIDs.Contains(m.DepartmentID))
                .ToDictionary(m => m.DepartmentID, m => m.LocalShowName);
        }

        /// <summary>
        /// 获取用户信息
        /// </summary>
        /// <returns></returns>
        public async Task<EmployeeInformation[]> GetEmployeesInformation()
        {
            var departments = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var idAndNamesDict = await _employeePersonalDataRepository.GetEmployeeIDAndNames();
            var idAndDepartmentIDDict = await _employeeStaffDataRepository.GetEmployeeDepartmentIDs(idAndNamesDict.Keys);
            var employeesInformation = (from a in idAndNamesDict
                                        join b in idAndDepartmentIDDict
                                        on a.Key equals b.Key
                                        let departmentName = b.Value.HasValue ? departments.Find(n => n.DepartmentID == b.Value.Value)?.LocalShowName : ""
                                        select new EmployeeInformation(a.Key, a.Value.name, a.Value.pinyin, b.Value, departmentName))
                                        .ToArray();
            return employeesInformation;
        }

        /// <summary>
        /// 获取员工离职列表
        /// </summary>
        /// <param name="queryView"></param>
        /// <returns></returns>
        public async Task<List<EmployeeResignationView>> GetEmployeeResignationList(EmployeeQueryView queryView)
        {
            List<string> departments = [];
            if (queryView.DepartmentIDs != null && queryView.DepartmentIDs.Length > 0)
            {
                departments = [.. queryView.DepartmentIDs.Select(m => m.ToString())];
            }
            DateTime? beginResignationApplyDate = null;
            DateTime? endResignationApplyDate = null;
            DateTime? beginResignationDate = null;
            DateTime? endResignationDate = null;
            if (queryView.ResignationApplyDate != null && queryView.ResignationApplyDate.Length > 0)
            {
                if (DateTime.TryParse(queryView.ResignationApplyDate[0], out DateTime beginApplyDate))
                {
                    beginResignationApplyDate = beginApplyDate;
                }
                if (queryView.ResignationApplyDate.Length > 1 && DateTime.TryParse(queryView.ResignationApplyDate[1], out DateTime endApplyDate))
                {
                    endResignationApplyDate = endApplyDate;
                }
            }
            if (queryView.ResignationDate != null && queryView.ResignationDate.Length > 0)
            {
                if (DateTime.TryParse(queryView.ResignationDate[0], out DateTime beginResignation))
                {
                    beginResignationDate = beginResignation;
                }
                if (queryView.ResignationDate.Length > 1 && DateTime.TryParse(queryView.ResignationDate[1], out DateTime endResignation))
                {
                    endResignationDate = endResignation;
                }
            }
            var employeeResignationList = await _employeeStaffDataRepository.GetEmployeeResignationListAsync(queryView.HospitalID, departments, beginResignationApplyDate, endResignationApplyDate, beginResignationDate, endResignationDate);
            if (employeeResignationList.Count <= 0)
            {
                return new List<EmployeeResignationView>();
            }
            var departmentInfos = await _departmentListRepository.GetAll<DepartmentListInfo>();
            foreach (var item in employeeResignationList)
            {
                item.DepartmentName = departmentInfos.FirstOrDefault(m => m.DepartmentID.ToString() == item.DepartmentID)?.DepartmentContent ?? string.Empty;
                item.Gender = item.Gender == "10" ? "男" : "女";
            }
            return employeeResignationList;
        }

        public async Task<Dictionary<string, EmployeeAsOption>> GetEmployeesAsOptions()
        {
            var departments = await _departmentListRepository.GetAll<DepartmentListInfo>();
            var idAndNamesDict = await _employeePersonalDataRepository.GetEmployeeIDAndNames();
            var idAndDepartmentIDDict = await _employeeStaffDataRepository.GetEmployeeDepartmentIDs(idAndNamesDict.Keys);
            var employeeOptions = (from a in idAndNamesDict
                                   join b in idAndDepartmentIDDict
                                   on a.Key equals b.Key
                                   let departmentName = b.Value.HasValue ? departments.Find(n => n.DepartmentID == b.Value.Value)?.LocalShowName : ""
                                   select new EmployeeAsOption(a.Key, a.Value.name, a.Value.pinyin, departmentName))
                                   .ToDictionary(m => m.ID);
            return employeeOptions;
        }
    }
}
