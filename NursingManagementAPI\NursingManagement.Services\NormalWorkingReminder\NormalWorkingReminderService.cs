﻿using Microsoft.Extensions.Options;
using NLog;
using NursingManagement.Common;
using NursingManagement.Data.Interface;
using NursingManagement.Models;
using NursingManagement.Services.Interface;
using NursingManagement.Services.Interface.NormalWorkingReminder;
using NursingManagement.ViewModels;
using NursingManagement.ViewModels.NormalWorkingReminder;

namespace NursingManagement.Services.NormalWorkingReminder
{
    /// <summary>
    /// 常态工作控制提醒服务实现
    /// </summary>
    public class NormalWorkingReminderService : INormalWorkingReminderService
    {
        private static readonly Logger _logger = LogManager.GetCurrentClassLogger();
        private readonly IOptions<SystemConfig> _systemConfig;
        private readonly IHierarchicalQCMainRepository _hierarchicalQCMainRepository;
        private readonly IProblemRectificationRepository _problemRectificationRepository;
        private readonly IEmployeeToJobRepository _employeeToJobRepository;
        private readonly IDepartmentListRepository _departmentListRepository;
        private readonly IEmployeePersonalDataRepository _employeePersonalDataRepository;
        private readonly IMessageService _messageService;
        private readonly IEmployeeToDepartmentRepository _employeeToDepartmentRepository;
        private readonly IEmployeeRoleRepository _employeeRoleRepository;
        /// <summary>
        /// 护士长职务编号
        /// </summary>
        private const string HEAD_NURSE_JOB_CODE = "975";
        /// <summary>
        ///  护理组织类型
        /// </summary>
        private const string ORGANIZATION_TYPE_DISTRICT = "1";
        /// <summary>
        /// // 片区级别
        /// </summary>
        private const int DISTRICT_LEVEL = 1;
        /// <summary>
        /// 3天提醒类型
        /// </summary>
        private const int REMINDER_TYPE_3_DAYS = 3;
        /// <summary>
        /// 6天提醒类型
        /// </summary>
        private const int REMINDER_TYPE_6_DAYS = 6; 
        /// <summary>
        /// 常态工作控制类型
        /// </summary>
        private const string NORMAL_CONTROL_FORM_TYPE = "6";
        // 每月检查 管理层没有进行质控的结束日期节点 每月25号
        private const int MONTH_REMINDER_END_DAY = 25;
        /// <summary>
        /// 片区主任角色
        /// </summary>
        private const int DISTRICT_DIRECTOR_ROLE_ID = 60;


        public NormalWorkingReminderService(
            IHierarchicalQCMainRepository hierarchicalQCMainRepository,
            IProblemRectificationRepository problemRectificationRepository,
            IEmployeeToJobRepository employeeToJobRepository,
            IDepartmentListRepository departmentListRepository,
            IEmployeePersonalDataRepository employeePersonalDataRepository,
            IMessageService messageService,
            IOptions<SystemConfig> systemConfig,
            IEmployeeToDepartmentRepository employeeToDepartmentRepository,
            IEmployeeRoleRepository employeeRoleRepository
            )
        {
            _hierarchicalQCMainRepository = hierarchicalQCMainRepository;
            _problemRectificationRepository = problemRectificationRepository;
            _employeeToJobRepository = employeeToJobRepository;
            _departmentListRepository = departmentListRepository;
            _employeePersonalDataRepository = employeePersonalDataRepository;
            _messageService = messageService;
            _systemConfig = systemConfig;
            _employeeToDepartmentRepository = employeeToDepartmentRepository;
            _employeeRoleRepository = employeeRoleRepository;
        }
        #region 常态过程质量控制闭环-质控问题监督提醒

        /// <summary>
        /// 执行常态工作控制提醒
        /// </summary>
        /// <param name="requestView">提醒请求参数</param>
        /// <returns>提醒执行结果</returns>
        public async Task<ReminderResultView> ExecuteReminderAsync(ReminderRequestView requestView)
        {
            var result = new ReminderResultView();
            if (requestView == null)
            {
                _logger.Error("ExecuteReminderAsync: 请求参数为空");
                return result.SetSuccessStatus(false, "请求参数不能为空");
            }
            // 如果HospitalID为空，从配置中获取
            if (string.IsNullOrWhiteSpace(requestView.HospitalID))
            {
                requestView.HospitalID = _systemConfig.Value.HospitalID;
                if (string.IsNullOrWhiteSpace(requestView.HospitalID))
                {
                    _logger.Error("医院ID为空且配置中未找到默认医院ID");
                    return result.SetSuccessStatus(false, "医院ID不能为空，且配置中未找到默认医院ID");
                }
            }
            _logger.Info($"开始执行常态工作控制提醒，医院ID：{requestView.HospitalID}，提醒类型：{requestView.ReminderType}");
            // 根据提醒类型执行不同的提醒逻辑
            switch (requestView.ReminderType)
            {
                case REMINDER_TYPE_3_DAYS:
                    return await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, requestView.ReminderType, requestView.DepartmentID);
                case REMINDER_TYPE_6_DAYS:
                    return await ExecuteReminderByTypeInternalAsync(requestView.HospitalID, requestView.ReminderType, requestView.DepartmentID);
                default:
                    _logger.Error($"不支持的提醒类型：{requestView.ReminderType}");
                    return result.SetSuccessStatus(false, $"不支持的提醒类型：{requestView.ReminderType}");
            }
        }

        /// <summary>
        /// 执行指定类型的提醒 - 内部通用方法
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="reminderType">提醒类型（3或6）</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>提醒执行结果</returns>
        private async Task<ReminderResultView> ExecuteReminderByTypeInternalAsync(string hospitalID, int reminderType, int? departmentID = null)
        {
            var result = new ReminderResultView();
            var reminderTypeName = reminderType == REMINDER_TYPE_3_DAYS ? "三天" : "六天";

            _logger.Info($"开始执行{reminderTypeName}未整改提醒，医院ID：{hospitalID}，部门ID：{departmentID}");

            // 获取需要提醒的问题列表
            var problems = await GetUnrectifiedProblemsInternalAsync(hospitalID, reminderType, departmentID);
            if (problems == null || problems.Count == 0)
            {
                _logger.Info($"没有需要{reminderTypeName}提醒的问题");
                return result.SetSuccessStatus(true,$"没有需要{reminderTypeName}提醒的问题");
            }

            _logger.Info($"找到{problems.Count}个需要{reminderTypeName}提醒的问题");
            // 发送通知
            result.ReminderDetails = await SendReminderAsync(problems, reminderType);

            result.ProblemsNeedReminder = problems.Count;
            result.SuccessfulReminders = result.ReminderDetails.Count(d => d.Success);
            result.FailedReminders = result.ReminderDetails.Count(d => !d.Success);
            result.Success = result.FailedReminders == 0;
            result.Message = $"{reminderTypeName}提醒执行完成，成功：{result.SuccessfulReminders}，失败：{result.FailedReminders}";

            _logger.Info($"质控未整改消息提醒完成：{result.Message}");
            return result;

        }

        /// <summary>
        /// 查询未整改问题列表
        /// </summary>
        /// <param name="hospitalID">医院ID</param>
        /// <param name="reminderType">提醒类型（3或6）</param>
        /// <param name="departmentID">部门ID（可选）</param>
        /// <returns>需要提醒的问题列表</returns>
        private async Task<List<ReminderProblemView>> GetUnrectifiedProblemsInternalAsync(string hospitalID, int reminderType, int? departmentID = null)
        {
            // 直接从数据库获取指定天数范围的质控维护记录
            var mainRecords = await _hierarchicalQCMainRepository.GetMainRecordsByDaysAgoAndFormTypeAsNoTrackAsync(
                hospitalID, reminderType, NORMAL_CONTROL_FORM_TYPE, departmentID);
            if (mainRecords.Count == 0)
            {
                _logger.Info($"没有找到{reminderType}天未整改的质控维护记录");
                return [];
            }
            // 获取这些记录的整改状态
            var mainIDs = mainRecords.Select(m => m.HierarchicalQCMainID).ToList();
            var rectificationRecords = await _problemRectificationRepository.GetProblemRectificationByHierarchicalQCMainID(mainIDs);
            var rectificationDict = rectificationRecords?.ToDictionary(r => r.HierarchicalQCMainID, r => r) ?? [];

            var currentDate = DateTime.Now.Date;
            var reminderProblemViews = new List<ReminderProblemView>();
            foreach (var mainRecord in mainRecords)
            {
                if (mainRecord.AssessDate == null)
                {
                    mainRecord.AssessDate = mainRecord.ModifyDateTime;
                }
                // 检查是否已整改
                var hasRectification = rectificationDict.ContainsKey(mainRecord.HierarchicalQCMainID);
                if (hasRectification)
                {
                    continue;
                }
                // 计算未整改天数
                int unrectifiedDays = (currentDate - mainRecord.AssessDate.Value.Date).Days;
                // 三天未整改 -在第四天 6天未整改 -在第七天提醒
                if (unrectifiedDays!= reminderType)
                {
                    continue;
                }
                var reminderProblemView = new ReminderProblemView
                {
                    HierarchicalQCMainID = mainRecord.HierarchicalQCMainID,
                    HierarchicalQCRecordID = mainRecord.HierarchicalQCRecordID,
                    ExamineDate = mainRecord.AssessDate.Value,
                    DepartmentID = mainRecord.DepartmentID,
                    Guidance = mainRecord.Guidance,
                    Improvement = mainRecord.Improvement,
                    UnrectifiedDays = unrectifiedDays,
                    RectificationDateTime = null,
                    IsRectified = hasRectification,
                    ReminderType = reminderType,
                };
                reminderProblemViews.Add(reminderProblemView);
            }
            _logger.Info($"找到{reminderProblemViews.Count}个未整改问题");
            return reminderProblemViews;
        }

        /// <summary>
        /// 发送提醒消息给护士长 - 内部方法
        /// </summary>
        /// <param name="reminderProblems">需要提醒的问题列表</param>
        /// <param name="reminderType">通知类型</param>
        /// <returns>提醒详情列表</returns>
        private async Task<List<ReminderDetailView>> SendReminderAsync(List<ReminderProblemView> reminderProblems, int reminderType)
        {
            var result = new List<ReminderDetailView>();
            // 按部门分组
            var reminderProblemsByDepartment = reminderProblems.GroupBy(p => p.DepartmentID).ToList();
            foreach (var groupItem in reminderProblemsByDepartment)
            {
                var departmentProblems = groupItem.ToList();
                if (departmentProblems.Count == 0)
                {
                    continue;
                }
                var departmentID = groupItem.Key;
                // 获取部门名称
                var departmentInfo = await GetDepartmentInfoAsync(departmentID);
                if (departmentInfo == null)
                {
                    _logger.Error($"获取部门信息失败DepartmentID={departmentID}");
                    continue;
                }
                var reminderDetail = new ReminderDetailView
                {
                    DepartmentID = departmentID,
                    ReminderType = reminderType
                };
                // 查找通知人员
                var employeeIDs = await GetNotifyEmployeeIDByReminderType(departmentID, reminderType);
                if (employeeIDs == null || employeeIDs.Count == 0)
                {
                    result.Add(reminderDetail.Error($"未找到需要通知的人员工号，部门=[{departmentID}],reminderType={reminderType}"));
                    continue;
                }
                // 构建提醒消息
                var messageContent = BuildReminderMessage(departmentProblems, REMINDER_TYPE_3_DAYS);
                // 发送消息通知
                var (allSuccess, errorMessages) = await BatchSendMessage(employeeIDs, messageContent);
                // 返回结果
                _logger.Info($"部门{departmentID}护士长提醒发送完成，成功：{allSuccess}");
                reminderDetail.Success = allSuccess;
                reminderDetail.ReceiverEmployeeIDs = employeeIDs;
                reminderDetail.DepartmentName = departmentInfo.LocalShowName;
                reminderDetail.Message = allSuccess ? "提醒发送成功" : "部分提醒发送失败";
                reminderDetail.ErrorMessage = errorMessages.Count > 0 ? string.Join("; ", errorMessages) : null;
                result.Add(reminderDetail);
            }
            return result;
        }
        /// <summary>
        /// 获取通知人员工号集合
        /// </summary>
        /// <param name="departmentID">部门</param>
        /// <param name="reminderType">未整改提醒类型</param>
        /// <returns></returns>
        private async Task<List<string>> GetNotifyEmployeeIDByReminderType(int departmentID, int reminderType)
        {
            if (reminderType == REMINDER_TYPE_3_DAYS)
            {
                var headNurseEmployeeIDs = await _employeeToJobRepository.GetEmployeeIDByJobCode(HEAD_NURSE_JOB_CODE, departmentID);
                if (headNurseEmployeeIDs == null || headNurseEmployeeIDs.Count == 0)
                {
                    _logger.Warn($"未找到部门=[{departmentID}]护士长");
                }
                return headNurseEmployeeIDs;
            }
            if (reminderType == REMINDER_TYPE_6_DAYS)
            {
                var districtDirectorEmployeeIDs = await GetDistrictDirectorIDs(departmentID);
                if (districtDirectorEmployeeIDs == null || districtDirectorEmployeeIDs.Count == 0)
                {
                    _logger.Warn($"未找到部门=[{departmentID}]的片区主任");
                }
                return districtDirectorEmployeeIDs;
            }
            return null;
        }

        /// <summary>
        /// 批量发送消息通知
        /// </summary>
        /// <param name="employeeIDs">消息通知人员工号集合</param>
        /// <param name="messageContent">具体的通知内容</param>
        /// <returns></returns>
        private async Task<(bool, List<string>)> BatchSendMessage(List<string> employeeIDs, string messageContent)
        {
            var allSuccess = true;
            List<string> errorMessages = [];
            foreach (var employeeID in employeeIDs)
            {
                var successFlag = await SendMessageToEmployee(employeeID, messageContent);
                if (!successFlag)
                {
                    allSuccess = false;
                    errorMessages.Add($"消息通知发送失败，工号{employeeID}失败，messageContent={messageContent}");
                }
            }
            return (allSuccess, errorMessages);
        }

        #region 私有辅助方法

        /// <summary>
        /// 查找片区主任员工ID列表
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>片区主任员工ID列表</returns>
        private async Task<List<string>> GetDistrictDirectorIDs(int departmentID)
        {
            // 查找上级片区部门
            var districtDepartment = await GetDistrictDepartmentAsync(departmentID);
            if (districtDepartment == null)
            {
                _logger.Warn($"部门{departmentID}未找到对应的片区部门");
                return new List<string>();
            }
            var employeeIDs = await _employeeToDepartmentRepository.GetEmployeeByDepartmentIDAndType(districtDepartment.DepartmentID, ORGANIZATION_TYPE_DISTRICT);
            if (employeeIDs.Count == 0)
            {
                _logger.Warn($"根据片区部门{departmentID}未找到拥有部门权限的人员");
                return [];
            }
            var directorEmployeeIDs = await _employeeRoleRepository.FilterEmployeeByRole(employeeIDs, DISTRICT_DIRECTOR_ROLE_ID);
            if (directorEmployeeIDs.Count == 0)
            {
                _logger.Warn($"未找到拥有片区主任角色的人员,片区为【{districtDepartment.LocalShowName}】,departmentID=【{districtDepartment.DepartmentID}】");
                return [];
            }
            return directorEmployeeIDs;
        }

        /// <summary>
        /// 查找部门对应的片区部门
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>片区部门信息</returns>
        private async Task<DepartmentListInfo> GetDistrictDepartmentAsync(int departmentID)
        {
            var currentDepartmentID = departmentID;
            // 递归查找上级部门，直到找到片区
            while (currentDepartmentID != 0)
            {
                var department = await GetDepartmentInfoAsync(currentDepartmentID);
                if (department == null)
                {
                    break;
                }
                // 检查是否为片区部门
                if (department.OrganizationType == ORGANIZATION_TYPE_DISTRICT && department.Level == DISTRICT_LEVEL)
                {
                    return department;
                }
                // 查找上级部门
                currentDepartmentID = department.UpperLevelDepartmentID;
            }
            return null;
        }

        /// <summary>
        /// 获取部门信息
        /// </summary>
        /// <param name="departmentID">部门ID</param>
        /// <returns>部门信息</returns>
        private async Task<DepartmentListInfo> GetDepartmentInfoAsync(int departmentID)
        {
            var allDepartments = await _departmentListRepository.GetByOrganizationType(ORGANIZATION_TYPE_DISTRICT);
            return allDepartments?.FirstOrDefault(d => d.DepartmentID == departmentID);
        }

        /// <summary>
        /// 构建提醒消息内容
        /// </summary>
        /// <param name="problems">问题列表</param>
        /// <param name="reminderType">提醒类型</param>
        /// <returns>消息内容</returns>
        private string BuildReminderMessage(List<ReminderProblemView> problems, int reminderType)
        {
            var messageBuilder = new System.Text.StringBuilder();
            var reminderTypeText = $"{reminderType}天";

            messageBuilder.AppendLine($"以下是{reminderTypeText}未整改的常态工作过程控制问题：");
            for (int i = 0; i < problems.Count; i++)
            {
                var problem = problems[i];
                messageBuilder.AppendLine($"{i + 1}.问题发生人：{problem.QcEmployeeName}");
                messageBuilder.AppendLine($"发现日期：{problem.ExamineDate:yyyy-MM-dd}");
                messageBuilder.AppendLine($"未整改天数：{problem.UnrectifiedDays}天");
                messageBuilder.AppendLine();
            }
            messageBuilder.AppendLine("请及时督促相关人员进行整改。");

            return messageBuilder.ToString();

        }

        /// <summary>
        /// 发送消息给指定员工
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="content">消息内容</param>
        /// <returns>发送是否成功</returns>
        private async Task<bool> SendMessageToEmployeeAsync(string employeeID, string content)
        {
            if (string.IsNullOrWhiteSpace(employeeID))
            {
                _logger.Error("SendMessageToEmployeeAsync: 用户ID为空");
                return false;
            }
            var messageView = new MessageView
            {
                EmployeeID = employeeID,
                MessageCondition = new MessageConditionView
                {
                    Message = content,
                    MessageContent = content,
                    SendMessageDateTime = DateTime.Now
                }
            };
            var result = await _messageService.SendMessage(messageView);
            if (!result)
            {
                _logger.Error($"发送消息给{employeeID}失败");
            }
            return result;
        }

        #endregion

        #endregion

        #region 管理层参与动态监测提醒

        /// <summary>
        /// 发送管理层参与动态监测提醒
        /// </summary>
        /// <returns>提醒执行结果(不满足条件的日期不会进行题型 比如必须周一、 每月25号 -20250705)</returns>
        public async Task<bool> SendManagementParticipationReminderAsync()
        {
            _logger.Info("开始执行管理层参与动态监测提醒");
            // 1. 计算时间范围
            var timeRanges = GetTimeRangesByCurrentDate();
            if (!timeRanges.TodayIsMonday || !timeRanges.TodayIsTwentyFiveDay)
            {
                _logger.Info("今天不是周一也不是当月25号，不进行题型");
                return false;
            }
            // 2. 获取护士长列表（包含姓名）
            var headNurses = await GetHeadNursesWithNames();
            if (headNurses == null || headNurses.Count == 0)
            {
                _logger.Error("找不到护士长信息");
                return false;
            }
            // 3. 批量查询质控记录
            var employeeIDs = headNurses.Select(h => h.EmployeeID).ToList();
            var allQCRecords = await GetQCRecords(employeeIDs, timeRanges);
            // 4. 循环检查并收集提醒数据
            var reminderData = await CheckHeadNursesQC(headNurses, timeRanges, allQCRecords);
            // 5. 统一发送通知
            var result = await SendReminders(reminderData);

            return result;
        }

        /// <summary>
        /// 获取护士长列表（包含姓名）
        /// </summary>
        /// <returns>护士长信息列表</returns>
        private async Task<List<HeadNurseView>> GetHeadNursesWithNames()
        {
            var headNurseJobs = await _employeeToJobRepository.GetHeadNurseJobByJobCodeAsync(HEAD_NURSE_JOB_CODE);
            if (headNurseJobs == null || headNurseJobs.Count == 0)
            {
                _logger.Info("GetHeadNursesWithNames: 未找到护士长数据");
                return null;
            }
            var employeeIDs = headNurseJobs.Select(h => h.EmployeeID).ToList();
            var personalDataList = await _employeePersonalDataRepository.GetListByEmployeeIDs(employeeIDs);
            if (personalDataList.Count == 0)
            {
                _logger.Warn("GetHeadNursesWithNames: 未找到护士长个人基本信息");
                return null;
            }
            var headNurseViews = (from job in headNurseJobs
                                  join pd in personalDataList
                                 on job.EmployeeID equals pd.EmployeeID
                                  where !string.IsNullOrWhiteSpace(pd.EmployeeName)
                                  select new HeadNurseView
                                  {
                                      EmployeeID = pd.EmployeeID,
                                      EmployeeName = pd.EmployeeName,
                                      DepartmentID = job.DepartmentID
                                  }).ToList();

            _logger.Info($"GetHeadNursesWithNames: 找到{headNurseViews.Count}个护士长信息");
            return headNurseViews;
        }

        /// <summary>
        /// 计算时间范围
        /// </summary>
        /// <returns>时间范围信息</returns>
        private QCTimeRangesView GetTimeRangesByCurrentDate()
        {
            var now = DateTime.Now;
            var today = now.Date;
            // 计算上周（自然周：周一到周日）
            var dayOfWeek = (int)today.DayOfWeek;
            if (dayOfWeek == 0)
            {
                dayOfWeek = 7;
            }
            // 上周一
            var lastWeekStart = today.AddDays(-(dayOfWeek + 6));
            // 上周日
            var lastWeekEnd = lastWeekStart.AddDays(6);
            // 上两周
            var twoWeeksAgoStart = lastWeekStart.AddDays(-7);
            // 计算当月1日到25日
            var monthStart = new DateTime(now.Year, now.Month, 1);
            var month25th = new DateTime(now.Year, now.Month, 25);
            var timeRanges = new QCTimeRangesView
            {
                LastWeekStart = lastWeekStart,
                LastWeekEnd = lastWeekEnd,
                TwoWeeksAgoStart = twoWeeksAgoStart,
                MonthStart = monthStart,
                Month25th = month25th,
                TodayIsMonday = dayOfWeek == 1,
                TodayIsTwentyFiveDay = today.Day == MONTH_REMINDER_END_DAY
            };
            _logger.Info($"GetTimeRanges: 上周{lastWeekStart:yyyy-MM-dd}至{lastWeekEnd:yyyy-MM-dd}，" +
                        $"两周前{twoWeeksAgoStart:yyyy-MM-dd}，当月1-25日{monthStart:yyyy-MM-dd}至{month25th:yyyy-MM-dd}");
            return timeRanges;
        }

        /// <summary>
        /// 查询质控记录
        /// </summary>
        /// <param name="employeeIDs">员工ID列表</param>
        /// <param name="timeRanges">时间范围</param>
        /// <returns>按员工ID分组的质控记录</returns>
        private async Task<Dictionary<string, List<QCRecordView>>> GetQCRecords(List<string> employeeIDs, QCTimeRangesView timeRanges)
        {
            var hospitalID = _systemConfig.Value.HospitalID;
            var startDateTime = timeRanges.TwoWeeksAgoStart;
            // 每月26号，通知护理部 本月没有进行管理层动态监测的人员，所以26号统计时，从月初开始查询
            if (timeRanges.TwoWeeksAgoStart.Day == MONTH_REMINDER_END_DAY + 1)
            {
                startDateTime = new DateTime(DateTime.Now.Year, DateTime.Now.Month, 1);
            }
            var records = await _hierarchicalQCMainRepository.GetQCRecordViewsByEmployeeIDsAndDateRangeAsync(hospitalID, employeeIDs, timeRanges.TwoWeeksAgoStart);
            if (records == null)
            {
                return new Dictionary<string, List<QCRecordView>>();
            }
            //按照记录人分组
            var groupQcRecordView = records.GroupBy(m => m.ModifyEmployeeID).ToDictionary(m => m.Key, n => n.ToList());

            _logger.Info($"GetAllQCRecordsBatch: 查询到{records.Count}条质控记录，涉及{groupQcRecordView.Count}个护士长");
            return groupQcRecordView;
        }

        /// <summary>
        /// 检查护士长质控记录
        /// </summary>
        /// <param name="headNurses">护士长列表</param>
        /// <param name="timeRanges">时间范围</param>
        /// <param name="allQCRecords">所有质控记录</param>
        /// <returns>提醒收集数据</returns>
        private async Task<ReminderCollectionDataView> CheckHeadNursesQC(List<HeadNurseView> headNurses, QCTimeRangesView timeRanges, Dictionary<string, List<QCRecordView>> allQCRecords)
        {
            var reminderData = new ReminderCollectionDataView();

            foreach (var headNurse in headNurses)
            {
                if (string.IsNullOrWhiteSpace(headNurse.EmployeeID) || string.IsNullOrWhiteSpace(headNurse.EmployeeName))
                {
                    continue;
                }
                if (!allQCRecords.TryGetValue(headNurse.EmployeeID, out var nurseQCRecords))
                {
                    nurseQCRecords = new List<QCRecordView>();
                }
                // 检查一周内质控记录
                var weeklyReminder = CheckWeeklyQC(headNurse, nurseQCRecords, timeRanges);
                if (weeklyReminder != null)
                {
                    reminderData.WeeklyReminders.Add(weeklyReminder);
                }
                // 检查连续两周质控记录
                var biWeeklyReminder = await CheckBiWeeklyQC(headNurse, nurseQCRecords, timeRanges);
                if (biWeeklyReminder != null)
                {
                    reminderData.BiWeeklyReminders.Add(biWeeklyReminder);
                }
                // 检查当月25日前质控记录
                var monthlyReminder = CheckMonthlyQC(headNurse, nurseQCRecords, timeRanges);
                if (monthlyReminder != null)
                {
                    reminderData.BiWeeklyReminders.Add(monthlyReminder);
                }
            }

            _logger.Info($"CheckHeadNursesQC: 一周提醒{reminderData.WeeklyReminders.Count}人，" +
                        $"两周提醒{reminderData.BiWeeklyReminders.Count}人，月度提醒{reminderData.MonthlyReminders.Count}人");

            return reminderData;
        }

        /// <summary>
        /// 检查一周质控记录
        /// </summary>
        /// <param name="headNurse">护士长信息</param>
        /// <param name="nurseQCRecords">护士长质控记录</param>
        /// <param name="timeRanges">时间范围</param>
        private MessageReminderView CheckWeeklyQC(HeadNurseView headNurse, List<QCRecordView> nurseQCRecords, QCTimeRangesView timeRanges)
        {
            var lastWeekRecords = nurseQCRecords.Where(r => r.ModifyDateTime >= timeRanges.LastWeekStart && r.ModifyDateTime <= timeRanges.LastWeekEnd).ToList();

            if (lastWeekRecords.Count > 0)
            {
                return null;
            }
            var weeklyReminder = new MessageReminderView
            {
                EmployeeID = headNurse.EmployeeID,
                Message = $"上周（{timeRanges.LastWeekStart:M月d日}至{timeRanges.LastWeekEnd:M月d日}）还未有您的质控记录"
            };
            _logger.Info($"CheckWeeklyQC: {headNurse.EmployeeName}({headNurse.EmployeeID}) 上周无质控记录");
            return weeklyReminder;
        }

        /// <summary>
        /// 检查两周质控记录
        /// </summary>
        /// <param name="headNurse">护士长信息</param>
        /// <param name="nurseQCRecords">护士长质控记录</param>
        /// <param name="timeRanges">时间范围</param>

        private async Task<MessageReminderView> CheckBiWeeklyQC(
            HeadNurseView headNurse, List<QCRecordView> nurseQCRecords, QCTimeRangesView timeRanges)
        {
            var twoWeeksRecords = nurseQCRecords.Where(r => r.ModifyDateTime >= timeRanges.TwoWeeksAgoStart && r.ModifyDateTime <= timeRanges.LastWeekEnd).ToList();
            if (twoWeeksRecords.Count > 0)
            {
                return null;
            }
            var districtDirectorIDs = await GetDistrictDirectorIDs(headNurse.DepartmentID);
            if (districtDirectorIDs.Count == 0)
            {
                _logger.Warn($"CheckBiWeeklyQC: 护士长{headNurse.EmployeeName}({headNurse.EmployeeID}) 未找到片区行政主任");
                return null;
            }
            var biWeeklyReminder = new MessageReminderView
            {
                EmployeeID = districtDirectorIDs.First(),
                Message = $"{headNurse.EmployeeName}已连续两周无常态工作过程控制记录，请关注！"
            };
            _logger.Info($"CheckBiWeeklyQC: {headNurse.EmployeeName}({headNurse.EmployeeID}) 连续两周无质控记录");
            return biWeeklyReminder;
        }

        /// <summary>
        /// 检查月度质控记录
        /// </summary>
        /// <param name="headNurse">护士长信息</param>
        /// <param name="nurseQCRecords">护士长质控记录</param>
        /// <param name="timeRanges">时间范围</param>
        private MessageReminderView CheckMonthlyQC(HeadNurseView headNurse, List<QCRecordView> nurseQCRecords, QCTimeRangesView timeRanges)
        {
            if (DateTime.Now < timeRanges.Month25th)
            {
                return null;
            }
            var monthRecords = nurseQCRecords.Where(r => r.ModifyDateTime >= timeRanges.MonthStart && r.ModifyDateTime <= timeRanges.Month25th).ToList();
            if (monthRecords.Count == 0)
            {
                _logger.Info($"CheckMonthlyQC: {headNurse.EmployeeName}({headNurse.EmployeeID}) 当月25日前无质控记录");
                var monthlyReminder = new MessageReminderView
                {
                    EmployeeID = headNurse.EmployeeID,
                    Message = $"{headNurse.EmployeeName}目前无常态工作过程控制记录，请关注！"
                };
                return monthlyReminder;
            }
            return null;
        }

        /// <summary>
        /// 统一发送提醒
        /// </summary>
        /// <param name="reminderData">提醒收集数据</param>
        /// <returns>提醒执行结果</returns>
        private async Task<bool> SendReminders(ReminderCollectionDataView reminderData)
        {
            var tasks = new List<System.Threading.Tasks.Task>();
            // 发送一周提醒（给护士长本人）
            tasks.AddRange(reminderData.WeeklyReminders.Select(reminder => SendMessageToEmployee(reminder.EmployeeID, reminder.Message)));

            // 发送两周提醒（给片区行政主任）
            tasks.AddRange(reminderData.BiWeeklyReminders.Select(reminder => SendMessageToEmployee(reminder.EmployeeID, reminder.Message)));

            // 发送月度提醒（给护理部李丹慧）            
            // TODO:先写死，后续正常后走配置
            const string nursingDepartmentEmployeeID = "147310";
            tasks.AddRange(reminderData.MonthlyReminders.Select(reminder => SendMessageToEmployee(nursingDepartmentEmployeeID, reminder.Message)));
            await System.Threading.Tasks.Task.WhenAll(tasks);

            return true;
        }
        /// <summary>
        /// 发送消息给员工
        /// </summary>
        /// <param name="employeeID">员工ID</param>
        /// <param name="message">消息内容</param>
        /// <returns>发送是否成功</returns>
        private async Task<bool> SendMessageToEmployee(string employeeID, string message)
        {
            if (string.IsNullOrWhiteSpace(employeeID) || string.IsNullOrWhiteSpace(message))
            {
                return false;
            }

            var messageView = new MessageView
            {
                MessageTools = new List<MessageTool> { MessageTool.MQ, MessageTool.Wechat },
                EmployeeID = employeeID,
                MessageCondition = new MessageConditionView
                {
                    Type = MessageType.Notification,
                    Message = message,
                    SendMessageDateTime = DateTime.Now,
                    MQExchangeName = "MQNotification",
                    MQRoutingKey = employeeID
                },
                ClientType = (int)Enums.ClientType.Mobile
            };

            var success =  await _messageService.SendMessage(messageView);
            if (success)
            {
                _logger.Info($"发送通知成功，通知人EmployeeID=【{employeeID}】,Message=【{message}】");
                return success;
            }
            _logger.Error($"发送通知失败，通知人EmployeeID=【{employeeID}】,Message=【{message}】");
            return success;
        }

        #endregion
    }
}
